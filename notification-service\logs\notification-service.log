{"level":"info","message":"Socket service initialized","service":"notification-service","timestamp":"2025-07-16T03:30:39.306Z"}
{"level":"info","message":"Notification Service running on port 3005","service":"notification-service","timestamp":"2025-07-16T03:30:39.311Z"}
{"ip":"::1","level":"warn","message":"Service authentication failed: Invalid service key","path":"/status","service":"notification-service","timestamp":"2025-07-16T03:31:13.986Z"}
{"level":"info","message":"Socket service initialized","service":"notification-service","timestamp":"2025-07-16T03:31:46.413Z"}
{"level":"info","message":"Notification Service running on port 3005","service":"notification-service","timestamp":"2025-07-16T03:31:46.417Z"}
{"event":"analysis-complete","level":"warn","message":"No active connections for user 123e4567-e89b-12d3-a456-426614174000","service":"notification-service","timestamp":"2025-07-16T03:31:52.795Z"}
{"jobId":"123e4567-e89b-12d3-a456-426614174001","level":"info","message":"Analysis complete notification processed","resultId":"123e4567-e89b-12d3-a456-426614174002","sent":false,"service":"notification-service","timestamp":"2025-07-16T03:31:52.795Z","userId":"123e4567-e89b-12d3-a456-426614174000"}
{"level":"info","message":"Socket service initialized","service":"notification-service","timestamp":"2025-07-16T03:33:35.281Z"}
{"level":"info","message":"Notification Service running on port 3005","service":"notification-service","timestamp":"2025-07-16T03:33:35.286Z"}
{"event":"analysis-complete","level":"warn","message":"No active connections for user 123e4567-e89b-12d3-a456-426614174000","service":"notification-service","timestamp":"2025-07-16T03:34:14.508Z"}
{"jobId":"123e4567-e89b-12d3-a456-426614174001","level":"info","message":"Analysis complete notification processed","resultId":"123e4567-e89b-12d3-a456-426614174002","sent":false,"service":"notification-service","timestamp":"2025-07-16T03:34:14.509Z","userId":"123e4567-e89b-12d3-a456-426614174000"}
{"event":"analysis-failed","level":"warn","message":"No active connections for user 123e4567-e89b-12d3-a456-426614174000","service":"notification-service","timestamp":"2025-07-16T03:34:14.543Z"}
{"error":"PROCESSING_ERROR","jobId":"123e4567-e89b-12d3-a456-426614174003","level":"info","message":"Analysis failed notification processed","sent":false,"service":"notification-service","timestamp":"2025-07-16T03:34:14.543Z","userId":"123e4567-e89b-12d3-a456-426614174000"}
{"headers":{"x-internal-service":false,"x-service-key":false},"ip":"::1","level":"warn","message":"Service authentication failed: Missing headers","path":"/status","service":"notification-service","timestamp":"2025-07-16T03:34:14.572Z"}
{"level":"info","message":"Socket service initialized","service":"notification-service","timestamp":"2025-07-16T06:16:09.171Z"}
{"level":"info","message":"Notification Service running on port 3005","service":"notification-service","timestamp":"2025-07-16T06:16:09.179Z"}
{"level":"info","message":"Socket service initialized","service":"notification-service","timestamp":"2025-07-16T06:20:59.789Z"}
{"level":"info","message":"Notification Service running on port 3005","service":"notification-service","timestamp":"2025-07-16T06:20:59.796Z"}
{"level":"info","message":"Socket service initialized","service":"notification-service","timestamp":"2025-07-16T06:26:05.778Z"}
{"level":"info","message":"Notification Service running on port 3005","service":"notification-service","timestamp":"2025-07-16T06:26:05.785Z"}
{"level":"info","message":"Socket connected: EJ9TocZCUL7-UM07AAAB","service":"notification-service","timestamp":"2025-07-16T06:26:33.661Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"EJ9TocZCUL7-UM07AAAB","timestamp":"2025-07-16T06:26:33.689Z","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"EJ9TocZCUL7-UM07AAAB","timestamp":"2025-07-16T06:28:52.702Z","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Socket connected: lDgbCtSj_XSjIGy6AAAD","service":"notification-service","timestamp":"2025-07-16T06:28:53.018Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"lDgbCtSj_XSjIGy6AAAD","timestamp":"2025-07-16T06:28:53.030Z","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Socket connected: cgkhEBCqpY4rB8qUAAAF","service":"notification-service","timestamp":"2025-07-16T06:29:18.364Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"cgkhEBCqpY4rB8qUAAAF","timestamp":"2025-07-16T06:29:18.421Z","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"cgkhEBCqpY4rB8qUAAAF","timestamp":"2025-07-16T06:29:18.690Z","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"lDgbCtSj_XSjIGy6AAAD","timestamp":"2025-07-16T06:29:47.735Z","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Socket connected: xJvSG3FE4u6VxQs1AAAH","service":"notification-service","timestamp":"2025-07-16T06:36:46.977Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"xJvSG3FE4u6VxQs1AAAH","timestamp":"2025-07-16T06:36:46.985Z","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"xJvSG3FE4u6VxQs1AAAH","timestamp":"2025-07-16T06:39:05.144Z","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Socket connected: Rq9eH5CdFCLoI-bVAAAJ","service":"notification-service","timestamp":"2025-07-16T06:39:05.430Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"Rq9eH5CdFCLoI-bVAAAJ","timestamp":"2025-07-16T06:39:05.440Z","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"Rq9eH5CdFCLoI-bVAAAJ","timestamp":"2025-07-16T06:39:16.138Z","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Socket connected: ZQRcMjNpoyl_al56AAAL","service":"notification-service","timestamp":"2025-07-16T06:39:16.307Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"ZQRcMjNpoyl_al56AAAL","timestamp":"2025-07-16T06:39:16.316Z","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"ZQRcMjNpoyl_al56AAAL","timestamp":"2025-07-16T06:39:22.120Z","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Socket connected: fm-vfZEHc08lFnvoAAAN","service":"notification-service","timestamp":"2025-07-16T06:39:22.263Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"fm-vfZEHc08lFnvoAAAN","timestamp":"2025-07-16T06:39:22.271Z","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"fm-vfZEHc08lFnvoAAAN","timestamp":"2025-07-16T06:56:01.587Z","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Socket service initialized","service":"notification-service","timestamp":"2025-07-16T07:41:37.766Z"}
{"level":"info","message":"Notification Service running on port 3005","service":"notification-service","timestamp":"2025-07-16T07:41:37.775Z"}
{"level":"info","message":"Socket connected: -V6dwL7-ylSutR4aAAAB","service":"notification-service","timestamp":"2025-07-16T07:42:04.472Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"-V6dwL7-ylSutR4aAAAB","timestamp":"2025-07-16T07:42:04.488Z","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"-V6dwL7-ylSutR4aAAAB","timestamp":"2025-07-16T07:46:57.273Z","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Socket connected: NpAdrFmkJLW9-S0LAAAD","service":"notification-service","timestamp":"2025-07-16T07:46:57.484Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"NpAdrFmkJLW9-S0LAAAD","timestamp":"2025-07-16T07:46:57.496Z","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Socket service initialized","service":"notification-service","timestamp":"2025-07-16T08:05:24.188Z"}
{"level":"info","message":"Notification Service running on port 3005","service":"notification-service","timestamp":"2025-07-16T08:05:24.196Z"}
{"level":"info","message":"Socket connected: OO69LSr36Ep8TcGoAAAB","service":"notification-service","timestamp":"2025-07-16T08:20:28.826Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"OO69LSr36Ep8TcGoAAAB","timestamp":"2025-07-16T08:20:28.839Z","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"OO69LSr36Ep8TcGoAAAB","timestamp":"2025-07-16T08:22:20.064Z","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Socket connected: sFCVIFDfnSBkKSmZAAAD","service":"notification-service","timestamp":"2025-07-16T08:22:20.298Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"sFCVIFDfnSBkKSmZAAAD","timestamp":"2025-07-16T08:22:20.310Z","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"sFCVIFDfnSBkKSmZAAAD","timestamp":"2025-07-16T08:43:44.649Z","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Socket connected: wQyjVzVE6Xzn2hNSAAAF","service":"notification-service","timestamp":"2025-07-16T08:43:45.130Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"wQyjVzVE6Xzn2hNSAAAF","timestamp":"2025-07-16T08:43:45.181Z","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"wQyjVzVE6Xzn2hNSAAAF","timestamp":"2025-07-16T08:43:47.447Z","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Socket connected: -8BDx5KKB_1mqOunAAAH","service":"notification-service","timestamp":"2025-07-16T08:57:35.922Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"-8BDx5KKB_1mqOunAAAH","timestamp":"2025-07-16T08:57:35.934Z","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"-8BDx5KKB_1mqOunAAAH","timestamp":"2025-07-16T08:57:43.468Z","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"level":"info","message":"Socket connected: EcrOQdRdvl57EID8AAAJ","service":"notification-service","timestamp":"2025-07-16T08:58:03.696Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"EcrOQdRdvl57EID8AAAJ","timestamp":"2025-07-16T08:58:03.707Z","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"EcrOQdRdvl57EID8AAAJ","timestamp":"2025-07-16T08:58:27.520Z","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"level":"info","message":"Socket connected: VqQtowsEJIPaE4vhAAAL","service":"notification-service","timestamp":"2025-07-16T08:58:27.534Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"VqQtowsEJIPaE4vhAAAL","timestamp":"2025-07-16T08:58:27.551Z","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"VqQtowsEJIPaE4vhAAAL","timestamp":"2025-07-16T08:58:31.762Z","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"level":"info","message":"Socket connected: 61QPjmP5lMxoQ4shAAAN","service":"notification-service","timestamp":"2025-07-16T08:58:31.772Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"61QPjmP5lMxoQ4shAAAN","timestamp":"2025-07-16T08:58:31.779Z","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"61QPjmP5lMxoQ4shAAAN","timestamp":"2025-07-16T08:58:36.339Z","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"level":"info","message":"Socket connected: iHIK5ST-OoQ0smOfAAAP","service":"notification-service","timestamp":"2025-07-16T08:58:36.586Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"iHIK5ST-OoQ0smOfAAAP","timestamp":"2025-07-16T08:58:36.599Z","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"level":"info","message":"Socket connected: zGZ3cizI7JA3VF4TAAAR","service":"notification-service","timestamp":"2025-07-16T09:18:28.028Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"zGZ3cizI7JA3VF4TAAAR","timestamp":"2025-07-16T09:18:28.043Z","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"iHIK5ST-OoQ0smOfAAAP","timestamp":"2025-07-16T09:18:44.216Z","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"level":"info","message":"Socket connected: 7EPh4MGtjNF9XwAMAAAT","service":"notification-service","timestamp":"2025-07-16T09:18:44.586Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"7EPh4MGtjNF9XwAMAAAT","timestamp":"2025-07-16T09:18:44.604Z","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"7EPh4MGtjNF9XwAMAAAT","timestamp":"2025-07-16T09:18:45.990Z","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"zGZ3cizI7JA3VF4TAAAR","timestamp":"2025-07-16T09:18:47.206Z","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"level":"info","message":"Socket service initialized","service":"notification-service","timestamp":"2025-07-16T16:26:02.054Z"}
{"level":"info","message":"Notification Service running on port 3005","service":"notification-service","timestamp":"2025-07-16T16:26:02.065Z"}
{"level":"info","message":"Socket service initialized","service":"notification-service","timestamp":"2025-07-16T16:26:46.318Z"}
{"level":"info","message":"Notification Service running on port 3005","service":"notification-service","timestamp":"2025-07-16T16:26:46.325Z"}
{"headers":{"x-internal-service":true,"x-service-key":false},"ip":"::1","level":"warn","message":"Service authentication failed: Missing headers","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-16T16:48:49.067Z"}
{"headers":{"x-internal-service":true,"x-service-key":false},"ip":"::1","level":"warn","message":"Service authentication failed: Missing headers","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-16T16:49:06.149Z"}
{"headers":{"x-internal-service":true,"x-service-key":false},"ip":"::1","level":"warn","message":"Service authentication failed: Missing headers","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-16T16:49:23.724Z"}
{"level":"info","message":"Socket service initialized","service":"notification-service","timestamp":"2025-07-16T16:54:08.960Z"}
{"level":"info","message":"Notification Service running on port 3005","service":"notification-service","timestamp":"2025-07-16T16:54:08.965Z"}
{"headers":{"x-internal-service":true,"x-service-key":false},"ip":"::1","level":"warn","message":"Service authentication failed: Missing headers","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-16T16:55:28.633Z"}
{"headers":{"x-internal-service":true,"x-service-key":false},"ip":"::1","level":"warn","message":"Service authentication failed: Missing headers","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-16T16:56:22.810Z"}
{"headers":{"x-internal-service":true,"x-service-key":false},"ip":"::1","level":"warn","message":"Service authentication failed: Missing headers","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-16T16:57:14.911Z"}
{"headers":{"x-internal-service":true,"x-service-key":false},"ip":"::1","level":"warn","message":"Service authentication failed: Missing headers","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-16T16:58:10.056Z"}
{"headers":{"x-internal-service":true,"x-service-key":false},"ip":"::1","level":"warn","message":"Service authentication failed: Missing headers","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-16T16:59:03.605Z"}
{"headers":{"x-internal-service":true,"x-service-key":false},"ip":"::1","level":"warn","message":"Service authentication failed: Missing headers","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-16T16:59:57.395Z"}
{"headers":{"x-internal-service":true,"x-service-key":false},"ip":"::1","level":"warn","message":"Service authentication failed: Missing headers","path":"/analysis-complete","service":"notification-service","timestamp":"2025-07-16T17:00:18.455Z"}
{"level":"info","message":"Socket service initialized","service":"notification-service","timestamp":"2025-07-16T22:44:43.405Z"}
{"level":"info","message":"Notification Service running on port 3005","service":"notification-service","timestamp":"2025-07-16T22:44:43.414Z"}
{"level":"info","message":"Socket connected: FdXEUpsoSjTR2AE0AAAB","service":"notification-service","timestamp":"2025-07-16T22:45:54.782Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"FdXEUpsoSjTR2AE0AAAB","timestamp":"2025-07-16T22:45:54.803Z","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"FdXEUpsoSjTR2AE0AAAB","timestamp":"2025-07-16T23:00:08.498Z","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Socket connected: lmhGAqPBQ4SE1FyZAAAD","service":"notification-service","timestamp":"2025-07-16T23:00:08.721Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"lmhGAqPBQ4SE1FyZAAAD","timestamp":"2025-07-16T23:00:08.732Z","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"lmhGAqPBQ4SE1FyZAAAD","timestamp":"2025-07-16T23:00:09.255Z","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Socket connected: L_Z5BCd6medUgdinAAAF","service":"notification-service","timestamp":"2025-07-16T23:00:09.375Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"L_Z5BCd6medUgdinAAAF","timestamp":"2025-07-16T23:00:09.385Z","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"L_Z5BCd6medUgdinAAAF","timestamp":"2025-07-16T23:00:09.705Z","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Socket connected: -OGM5t-wkMUGtjDAAAAH","service":"notification-service","timestamp":"2025-07-16T23:00:09.821Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"-OGM5t-wkMUGtjDAAAAH","timestamp":"2025-07-16T23:00:09.831Z","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"-OGM5t-wkMUGtjDAAAAH","timestamp":"2025-07-16T23:00:20.224Z","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Socket connected: rDfKTQGRgLU6AKTNAAAJ","service":"notification-service","timestamp":"2025-07-16T23:00:33.924Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"rDfKTQGRgLU6AKTNAAAJ","timestamp":"2025-07-16T23:00:33.937Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"rDfKTQGRgLU6AKTNAAAJ","timestamp":"2025-07-16T23:00:36.895Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket connected: LPkgJEZcQmpl6F7vAAAL","service":"notification-service","timestamp":"2025-07-16T23:00:41.101Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"LPkgJEZcQmpl6F7vAAAL","timestamp":"2025-07-16T23:00:41.110Z","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"LPkgJEZcQmpl6F7vAAAL","timestamp":"2025-07-16T23:00:45.075Z","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Socket connected: 1ZD0gRj0rxreWAv1AAAN","service":"notification-service","timestamp":"2025-07-16T23:16:44.376Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"1ZD0gRj0rxreWAv1AAAN","timestamp":"2025-07-16T23:16:44.389Z","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Socket connected: KCGfbpXDBkoknJLvAAAR","service":"notification-service","timestamp":"2025-07-16T23:43:00.519Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"KCGfbpXDBkoknJLvAAAR","timestamp":"2025-07-16T23:43:00.529Z","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"1ZD0gRj0rxreWAv1AAAN","timestamp":"2025-07-16T23:43:01.387Z","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"KCGfbpXDBkoknJLvAAAR","timestamp":"2025-07-16T23:43:03.230Z","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Socket connected: jqm_lgO5io2WJ_HlAAAU","service":"notification-service","timestamp":"2025-07-16T23:43:06.212Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"jqm_lgO5io2WJ_HlAAAU","timestamp":"2025-07-16T23:43:06.222Z","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"jqm_lgO5io2WJ_HlAAAU","timestamp":"2025-07-16T23:43:07.484Z","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Socket connected: WfdtU48UqbDsir-DAAAY","service":"notification-service","timestamp":"2025-07-16T23:48:35.456Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"WfdtU48UqbDsir-DAAAY","timestamp":"2025-07-16T23:48:35.465Z","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"WfdtU48UqbDsir-DAAAY","timestamp":"2025-07-16T23:48:37.298Z","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Socket connected: P6gSrI1-P0FoNt7bAAAc","service":"notification-service","timestamp":"2025-07-17T00:03:54.544Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"P6gSrI1-P0FoNt7bAAAc","timestamp":"2025-07-17T00:03:54.555Z","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"P6gSrI1-P0FoNt7bAAAc","timestamp":"2025-07-17T00:04:46.889Z","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Socket connected: 8zBL6k3GKM4jVnTPAAAg","service":"notification-service","timestamp":"2025-07-17T00:11:25.894Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"8zBL6k3GKM4jVnTPAAAg","timestamp":"2025-07-17T00:11:25.901Z","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"8zBL6k3GKM4jVnTPAAAg","timestamp":"2025-07-17T00:11:26.831Z","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Socket connected: AihkmnkMJ6nMgMgYAAAk","service":"notification-service","timestamp":"2025-07-17T00:11:31.613Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"AihkmnkMJ6nMgMgYAAAk","timestamp":"2025-07-17T00:11:31.625Z","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"AihkmnkMJ6nMgMgYAAAk","timestamp":"2025-07-17T00:11:34.552Z","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Socket connected: pjtpCYpZbjkhBj6bAAAo","service":"notification-service","timestamp":"2025-07-17T00:17:19.373Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"pjtpCYpZbjkhBj6bAAAo","timestamp":"2025-07-17T00:17:19.382Z","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"pjtpCYpZbjkhBj6bAAAo","timestamp":"2025-07-17T00:17:20.394Z","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Socket connected: xEVvQ5kdRuHnX6-wAAAs","service":"notification-service","timestamp":"2025-07-17T00:17:42.464Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"xEVvQ5kdRuHnX6-wAAAs","timestamp":"2025-07-17T00:17:42.477Z","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"xEVvQ5kdRuHnX6-wAAAs","timestamp":"2025-07-17T00:17:50.889Z","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Socket connected: NOIrjdfM6e28xU3GAAAv","service":"notification-service","timestamp":"2025-07-17T00:18:47.270Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"NOIrjdfM6e28xU3GAAAv","timestamp":"2025-07-17T00:18:47.282Z","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Socket connected: uSIUxbjEGhpWoGPnAAAx","service":"notification-service","timestamp":"2025-07-17T00:23:32.685Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"uSIUxbjEGhpWoGPnAAAx","timestamp":"2025-07-17T00:23:32.712Z","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"NOIrjdfM6e28xU3GAAAv","timestamp":"2025-07-17T00:23:42.037Z","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"uSIUxbjEGhpWoGPnAAAx","timestamp":"2025-07-17T00:23:42.379Z","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Socket connected: dtxNng_4PriwhLVOAAAz","service":"notification-service","timestamp":"2025-07-17T00:30:23.239Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"dtxNng_4PriwhLVOAAAz","timestamp":"2025-07-17T00:30:23.261Z","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Socket connected: -lSl3jb8FyoFmIPLAAA1","service":"notification-service","timestamp":"2025-07-17T00:30:45.974Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"-lSl3jb8FyoFmIPLAAA1","timestamp":"2025-07-17T00:30:45.995Z","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"-lSl3jb8FyoFmIPLAAA1","timestamp":"2025-07-17T00:30:50.400Z","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"dtxNng_4PriwhLVOAAAz","timestamp":"2025-07-17T00:30:50.836Z","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Socket connected: 4YscUW2V9q3pM4-lAAA5","service":"notification-service","timestamp":"2025-07-17T00:42:13.616Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"4YscUW2V9q3pM4-lAAA5","timestamp":"2025-07-17T00:42:13.624Z","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"4YscUW2V9q3pM4-lAAA5","timestamp":"2025-07-17T00:42:13.786Z","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Socket connected: ZwuibmZ1Ssx6pTYzAAA8","service":"notification-service","timestamp":"2025-07-17T00:42:13.797Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"ZwuibmZ1Ssx6pTYzAAA8","timestamp":"2025-07-17T00:42:13.803Z","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"ZwuibmZ1Ssx6pTYzAAA8","timestamp":"2025-07-17T00:42:16.374Z","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"level":"info","message":"Socket connected: 3KEOkO-QR_mg3-AcAABA","service":"notification-service","timestamp":"2025-07-17T00:42:22.288Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"3KEOkO-QR_mg3-AcAABA","timestamp":"2025-07-17T00:42:22.298Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"3KEOkO-QR_mg3-AcAABA","timestamp":"2025-07-17T00:42:22.322Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket connected: Xrb7WBPVMCfm6LSpAABD","service":"notification-service","timestamp":"2025-07-17T00:42:22.334Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"Xrb7WBPVMCfm6LSpAABD","timestamp":"2025-07-17T00:42:22.340Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"Xrb7WBPVMCfm6LSpAABD","timestamp":"2025-07-17T00:43:27.132Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket connected: RP8uAJYvOFelHjilAABG","service":"notification-service","timestamp":"2025-07-17T00:43:27.262Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"RP8uAJYvOFelHjilAABG","timestamp":"2025-07-17T00:43:27.269Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"RP8uAJYvOFelHjilAABG","timestamp":"2025-07-17T00:43:28.106Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket connected: 1yUz75mws9W3Dj7EAABJ","service":"notification-service","timestamp":"2025-07-17T00:43:28.142Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"1yUz75mws9W3Dj7EAABJ","timestamp":"2025-07-17T00:43:28.148Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"1yUz75mws9W3Dj7EAABJ","timestamp":"2025-07-17T00:43:33.118Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket connected: q1C9AYfCviRu5YNBAABM","service":"notification-service","timestamp":"2025-07-17T00:43:33.154Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"q1C9AYfCviRu5YNBAABM","timestamp":"2025-07-17T00:43:33.159Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"q1C9AYfCviRu5YNBAABM","timestamp":"2025-07-17T00:43:37.127Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket connected: j16MF9kU7gcsCqJoAABP","service":"notification-service","timestamp":"2025-07-17T00:43:37.171Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"j16MF9kU7gcsCqJoAABP","timestamp":"2025-07-17T00:43:37.176Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"j16MF9kU7gcsCqJoAABP","timestamp":"2025-07-17T00:43:39.106Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket connected: oCUfwdRoeCRs0BO-AABS","service":"notification-service","timestamp":"2025-07-17T00:43:39.138Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"oCUfwdRoeCRs0BO-AABS","timestamp":"2025-07-17T00:43:39.144Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"oCUfwdRoeCRs0BO-AABS","timestamp":"2025-07-17T00:43:47.116Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket connected: IVICkwB6ZFSqm0tPAABV","service":"notification-service","timestamp":"2025-07-17T00:43:47.152Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"IVICkwB6ZFSqm0tPAABV","timestamp":"2025-07-17T00:43:47.158Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"IVICkwB6ZFSqm0tPAABV","timestamp":"2025-07-17T00:43:49.110Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket connected: fgb6ccr1THPfrn_4AABY","service":"notification-service","timestamp":"2025-07-17T00:43:49.136Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"fgb6ccr1THPfrn_4AABY","timestamp":"2025-07-17T00:43:49.142Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"fgb6ccr1THPfrn_4AABY","timestamp":"2025-07-17T00:43:53.111Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket connected: 2ROX4BgPSGd05TGFAABb","service":"notification-service","timestamp":"2025-07-17T00:43:53.147Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"2ROX4BgPSGd05TGFAABb","timestamp":"2025-07-17T00:43:53.155Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"2ROX4BgPSGd05TGFAABb","timestamp":"2025-07-17T00:44:13.205Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket connected: 4QM5ntPgAkw1yLMbAABd","service":"notification-service","timestamp":"2025-07-17T00:48:16.952Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"4QM5ntPgAkw1yLMbAABd","timestamp":"2025-07-17T00:48:16.963Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"4QM5ntPgAkw1yLMbAABd","timestamp":"2025-07-17T00:48:17.399Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket connected: XKSTgJxjrjdx69AxAABg","service":"notification-service","timestamp":"2025-07-17T00:48:17.440Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"XKSTgJxjrjdx69AxAABg","timestamp":"2025-07-17T00:48:17.451Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"XKSTgJxjrjdx69AxAABg","timestamp":"2025-07-17T00:48:17.530Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket connected: HpZh9Zis-fpUGYFzAABj","service":"notification-service","timestamp":"2025-07-17T00:48:17.542Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"HpZh9Zis-fpUGYFzAABj","timestamp":"2025-07-17T00:48:17.548Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"HpZh9Zis-fpUGYFzAABj","timestamp":"2025-07-17T00:48:24.405Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket connected: KCzI7LAJSzSrE2joAABn","service":"notification-service","timestamp":"2025-07-17T00:48:24.563Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"KCzI7LAJSzSrE2joAABn","timestamp":"2025-07-17T00:48:24.569Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"KCzI7LAJSzSrE2joAABn","timestamp":"2025-07-17T00:48:24.594Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket connected: qqOBxOGjImUHNRE2AABq","service":"notification-service","timestamp":"2025-07-17T00:48:24.610Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"qqOBxOGjImUHNRE2AABq","timestamp":"2025-07-17T00:48:24.616Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"qqOBxOGjImUHNRE2AABq","timestamp":"2025-07-17T00:48:31.145Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket connected: Fht_JirIJb9I0g_pAABt","service":"notification-service","timestamp":"2025-07-17T00:48:31.194Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"Fht_JirIJb9I0g_pAABt","timestamp":"2025-07-17T00:48:31.199Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"Fht_JirIJb9I0g_pAABt","timestamp":"2025-07-17T00:48:37.367Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket connected: 4H_on38cf5oBAv0tAABw","service":"notification-service","timestamp":"2025-07-17T00:48:39.173Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"4H_on38cf5oBAv0tAABw","timestamp":"2025-07-17T00:48:39.182Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"4H_on38cf5oBAv0tAABw","timestamp":"2025-07-17T00:48:39.199Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket connected: Zjan7rl06dhinS8CAABz","service":"notification-service","timestamp":"2025-07-17T00:48:39.210Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"Zjan7rl06dhinS8CAABz","timestamp":"2025-07-17T00:48:39.217Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"Zjan7rl06dhinS8CAABz","timestamp":"2025-07-17T00:49:00.318Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket connected: Zt_kUjGkCg-EurjjAAB3","service":"notification-service","timestamp":"2025-07-17T01:01:33.936Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"Zt_kUjGkCg-EurjjAAB3","timestamp":"2025-07-17T01:01:33.943Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"Zt_kUjGkCg-EurjjAAB3","timestamp":"2025-07-17T01:01:34.043Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket connected: LzYRFSSBGivV55lbAAB6","service":"notification-service","timestamp":"2025-07-17T01:01:34.058Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"LzYRFSSBGivV55lbAAB6","timestamp":"2025-07-17T01:01:34.063Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"LzYRFSSBGivV55lbAAB6","timestamp":"2025-07-17T01:01:34.998Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket connected: A9zDKEcKGeyka_VrAAB9","service":"notification-service","timestamp":"2025-07-17T01:01:56.040Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"A9zDKEcKGeyka_VrAAB9","timestamp":"2025-07-17T01:01:56.050Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"A9zDKEcKGeyka_VrAAB9","timestamp":"2025-07-17T01:02:24.919Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket connected: imoYO6czMyyEecJSAACB","service":"notification-service","timestamp":"2025-07-17T01:02:25.171Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"imoYO6czMyyEecJSAACB","timestamp":"2025-07-17T01:02:25.179Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"imoYO6czMyyEecJSAACB","timestamp":"2025-07-17T01:02:25.889Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket connected: zt4SEHjEc7e-gr0oAACE","service":"notification-service","timestamp":"2025-07-17T01:02:26.523Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"zt4SEHjEc7e-gr0oAACE","timestamp":"2025-07-17T01:02:26.533Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"zt4SEHjEc7e-gr0oAACE","timestamp":"2025-07-17T01:02:26.595Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket connected: 6NSoO9CdZvytvX1lAACH","service":"notification-service","timestamp":"2025-07-17T01:02:26.616Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"6NSoO9CdZvytvX1lAACH","timestamp":"2025-07-17T01:02:26.622Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"6NSoO9CdZvytvX1lAACH","timestamp":"2025-07-17T01:02:28.864Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket connected: ivsKru3WN1EQw3EyAACL","service":"notification-service","timestamp":"2025-07-17T01:02:29.026Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"ivsKru3WN1EQw3EyAACL","timestamp":"2025-07-17T01:02:29.035Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"ivsKru3WN1EQw3EyAACL","timestamp":"2025-07-17T01:02:29.052Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket connected: tiU8akybhNkE1a1TAACO","service":"notification-service","timestamp":"2025-07-17T01:02:29.065Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"tiU8akybhNkE1a1TAACO","timestamp":"2025-07-17T01:02:29.073Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"tiU8akybhNkE1a1TAACO","timestamp":"2025-07-17T01:02:35.426Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket service initialized","service":"notification-service","timestamp":"2025-07-17T01:31:28.454Z"}
{"level":"info","message":"Notification Service running on port 3005","service":"notification-service","timestamp":"2025-07-17T01:31:28.463Z"}
{"level":"info","message":"Socket service initialized","service":"notification-service","timestamp":"2025-07-17T03:04:33.059Z"}
{"level":"info","message":"Notification Service running on port 3005","service":"notification-service","timestamp":"2025-07-17T03:04:33.067Z"}
{"level":"info","message":"Socket connected: EESAslvslfZqkRGfAAAB","service":"notification-service","timestamp":"2025-07-17T03:04:33.151Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"EESAslvslfZqkRGfAAAB","timestamp":"2025-07-17T03:04:33.170Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"EESAslvslfZqkRGfAAAB","timestamp":"2025-07-17T03:15:26.170Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket connected: uyrhc1SThZFxRERcAAAE","service":"notification-service","timestamp":"2025-07-17T03:15:26.519Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"uyrhc1SThZFxRERcAAAE","timestamp":"2025-07-17T03:15:26.524Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket service initialized","service":"notification-service","timestamp":"2025-07-17T03:42:59.119Z"}
{"level":"info","message":"Notification Service running on port 3005","service":"notification-service","timestamp":"2025-07-17T03:42:59.129Z"}
{"level":"info","message":"Socket connected: kkornm4Ipk7e_Xk5AAAD","service":"notification-service","timestamp":"2025-07-17T03:44:36.584Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"kkornm4Ipk7e_Xk5AAAD","timestamp":"2025-07-17T03:44:36.598Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"kkornm4Ipk7e_Xk5AAAD","timestamp":"2025-07-17T03:44:36.730Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket connected: W2_XY0qABPKvyorhAAAG","service":"notification-service","timestamp":"2025-07-17T03:44:36.758Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"W2_XY0qABPKvyorhAAAG","timestamp":"2025-07-17T03:44:36.776Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"W2_XY0qABPKvyorhAAAG","timestamp":"2025-07-17T03:47:29.854Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket connected: 8IwWCILmUn4NcBWKAAAI","service":"notification-service","timestamp":"2025-07-17T03:49:38.019Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"8IwWCILmUn4NcBWKAAAI","timestamp":"2025-07-17T03:49:38.034Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"8IwWCILmUn4NcBWKAAAI","timestamp":"2025-07-17T03:54:40.070Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket service initialized","service":"notification-service","timestamp":"2025-07-17T04:14:02.756Z"}
{"level":"info","message":"Notification Service running on port 3005","service":"notification-service","timestamp":"2025-07-17T04:14:02.763Z"}
{"headers":{"x-internal-service":true,"x-service-key":false},"ip":"::1","level":"warn","message":"Service authentication failed: Missing headers","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-17T04:14:32.578Z"}
{"headers":{"x-internal-service":true,"x-service-key":false},"ip":"::1","level":"warn","message":"Service authentication failed: Missing headers","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-17T04:15:16.989Z"}
{"headers":{"x-internal-service":true,"x-service-key":false},"ip":"::1","level":"warn","message":"Service authentication failed: Missing headers","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-17T04:15:35.232Z"}
{"headers":{"x-internal-service":true,"x-service-key":false},"ip":"::1","level":"warn","message":"Service authentication failed: Missing headers","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-17T04:16:14.589Z"}
{"headers":{"x-internal-service":true,"x-service-key":false},"ip":"::1","level":"warn","message":"Service authentication failed: Missing headers","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-17T04:16:49.626Z"}
{"headers":{"x-internal-service":true,"x-service-key":false},"ip":"::1","level":"warn","message":"Service authentication failed: Missing headers","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-17T04:17:17.816Z"}
{"headers":{"x-internal-service":true,"x-service-key":false},"ip":"::1","level":"warn","message":"Service authentication failed: Missing headers","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-17T04:18:32.995Z"}
{"headers":{"x-internal-service":true,"x-service-key":false},"ip":"::1","level":"warn","message":"Service authentication failed: Missing headers","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-17T04:28:45.491Z"}
{"headers":{"x-internal-service":true,"x-service-key":false},"ip":"::1","level":"warn","message":"Service authentication failed: Missing headers","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-17T04:29:44.468Z"}
{"headers":{"x-internal-service":true,"x-service-key":false},"ip":"::1","level":"warn","message":"Service authentication failed: Missing headers","path":"/analysis-complete","service":"notification-service","timestamp":"2025-07-17T04:30:28.849Z"}
{"level":"info","message":"Socket service initialized","service":"notification-service","timestamp":"2025-07-17T04:40:23.087Z"}
{"level":"info","message":"Notification Service running on port 3005","service":"notification-service","timestamp":"2025-07-17T04:40:23.094Z"}
{"level":"info","message":"Socket connected: HtKhp0iPXFf1aharAAAD","service":"notification-service","timestamp":"2025-07-17T04:40:34.940Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"HtKhp0iPXFf1aharAAAD","timestamp":"2025-07-17T04:40:34.955Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"HtKhp0iPXFf1aharAAAD","timestamp":"2025-07-17T04:40:35.101Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket connected: 65o1BIjvoG6xb0rfAAAG","service":"notification-service","timestamp":"2025-07-17T04:40:35.115Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"65o1BIjvoG6xb0rfAAAG","timestamp":"2025-07-17T04:40:35.123Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"65o1BIjvoG6xb0rfAAAG","timestamp":"2025-07-17T04:40:36.844Z","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"level":"info","message":"Socket connected: 0a63ORisAX52og3uAAAK","service":"notification-service","timestamp":"2025-07-17T04:41:15.206Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"0a63ORisAX52og3uAAAK","timestamp":"2025-07-17T04:41:15.222Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"0a63ORisAX52og3uAAAK","timestamp":"2025-07-17T04:41:15.294Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket connected: JTJaah-TsE5Gzy2lAAAN","service":"notification-service","timestamp":"2025-07-17T04:41:15.309Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"JTJaah-TsE5Gzy2lAAAN","timestamp":"2025-07-17T04:41:15.319Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"JTJaah-TsE5Gzy2lAAAN","timestamp":"2025-07-17T04:41:21.719Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket connected: xCp4sBbEQXI63uUaAAAQ","service":"notification-service","timestamp":"2025-07-17T05:00:42.787Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"xCp4sBbEQXI63uUaAAAQ","timestamp":"2025-07-17T05:00:42.794Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"xCp4sBbEQXI63uUaAAAQ","timestamp":"2025-07-17T05:00:42.877Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket connected: rfzH4EGJABwdktRcAAAT","service":"notification-service","timestamp":"2025-07-17T05:00:42.889Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"rfzH4EGJABwdktRcAAAT","timestamp":"2025-07-17T05:00:42.897Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"rfzH4EGJABwdktRcAAAT","timestamp":"2025-07-17T05:00:45.556Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket connected: HEptF52FsQ-S-eCVAAAX","service":"notification-service","timestamp":"2025-07-17T05:00:45.793Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"HEptF52FsQ-S-eCVAAAX","timestamp":"2025-07-17T05:00:45.808Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"HEptF52FsQ-S-eCVAAAX","timestamp":"2025-07-17T05:00:45.823Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket connected: -1XqOsQsqLE6qO6SAAAa","service":"notification-service","timestamp":"2025-07-17T05:00:45.838Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"-1XqOsQsqLE6qO6SAAAa","timestamp":"2025-07-17T05:00:45.846Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"-1XqOsQsqLE6qO6SAAAa","timestamp":"2025-07-17T05:00:47.426Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket connected: LLwIg_mqa_G0Uq4gAAAe","service":"notification-service","timestamp":"2025-07-17T05:00:47.583Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"LLwIg_mqa_G0Uq4gAAAe","timestamp":"2025-07-17T05:00:47.596Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"LLwIg_mqa_G0Uq4gAAAe","timestamp":"2025-07-17T05:00:47.619Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket connected: FM6gvXwF8C9qQcElAAAh","service":"notification-service","timestamp":"2025-07-17T05:00:47.637Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"FM6gvXwF8C9qQcElAAAh","timestamp":"2025-07-17T05:00:47.657Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket service initialized","service":"notification-service","timestamp":"2025-07-17T05:05:08.954Z"}
{"level":"info","message":"Notification Service running on port 3005","service":"notification-service","timestamp":"2025-07-17T05:05:08.960Z"}
{"level":"info","message":"Socket connected: yhoY8M3NDzm2PyIpAAAD","service":"notification-service","timestamp":"2025-07-17T05:07:40.537Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"yhoY8M3NDzm2PyIpAAAD","timestamp":"2025-07-17T05:07:40.631Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"yhoY8M3NDzm2PyIpAAAD","timestamp":"2025-07-17T05:07:40.726Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket connected: VOV8ZkcxyuquGb61AAAG","service":"notification-service","timestamp":"2025-07-17T05:07:40.802Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"VOV8ZkcxyuquGb61AAAG","timestamp":"2025-07-17T05:07:40.827Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"VOV8ZkcxyuquGb61AAAG","timestamp":"2025-07-17T05:07:42.665Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket connected: S_lcBRV4hcm8Lxv4AAAJ","service":"notification-service","timestamp":"2025-07-17T05:08:02.938Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"S_lcBRV4hcm8Lxv4AAAJ","timestamp":"2025-07-17T05:08:02.947Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"S_lcBRV4hcm8Lxv4AAAJ","timestamp":"2025-07-17T05:08:03.068Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket connected: PVZxinAkomnOimePAAAM","service":"notification-service","timestamp":"2025-07-17T05:08:03.130Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"PVZxinAkomnOimePAAAM","timestamp":"2025-07-17T05:08:03.154Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"PVZxinAkomnOimePAAAM","timestamp":"2025-07-17T05:10:08.108Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket connected: iBrlZFf8Bl1jrMv0AAAP","service":"notification-service","timestamp":"2025-07-17T05:10:08.194Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"iBrlZFf8Bl1jrMv0AAAP","timestamp":"2025-07-17T05:10:08.202Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket connected: 9V-d1mSFZNlzcnkZAAAT","service":"notification-service","timestamp":"2025-07-17T05:13:11.829Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"9V-d1mSFZNlzcnkZAAAT","timestamp":"2025-07-17T05:13:11.841Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"9V-d1mSFZNlzcnkZAAAT","timestamp":"2025-07-17T05:13:11.939Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket connected: vteis242m2BvTqxUAAAW","service":"notification-service","timestamp":"2025-07-17T05:13:11.955Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"vteis242m2BvTqxUAAAW","timestamp":"2025-07-17T05:13:11.961Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"vteis242m2BvTqxUAAAW","timestamp":"2025-07-17T05:13:13.457Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket connected: jG9oJPv9wRscuNtJAAAZ","service":"notification-service","timestamp":"2025-07-17T05:13:16.574Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"jG9oJPv9wRscuNtJAAAZ","timestamp":"2025-07-17T05:13:16.584Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"headers":{"x-internal-service":true,"x-service-key":false},"ip":"::1","level":"warn","message":"Service authentication failed: Missing headers","path":"/analysis-complete","service":"notification-service","timestamp":"2025-07-17T05:13:33.149Z"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"jG9oJPv9wRscuNtJAAAZ","timestamp":"2025-07-17T05:14:01.824Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket connected: WNU6aV1B65jxOz2TAAAc","service":"notification-service","timestamp":"2025-07-17T05:14:02.983Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"WNU6aV1B65jxOz2TAAAc","timestamp":"2025-07-17T05:14:02.993Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"WNU6aV1B65jxOz2TAAAc","timestamp":"2025-07-17T05:14:03.101Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket connected: C7sP3Od2jxYVMBiHAAAf","service":"notification-service","timestamp":"2025-07-17T05:14:03.125Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"C7sP3Od2jxYVMBiHAAAf","timestamp":"2025-07-17T05:14:03.132Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"C7sP3Od2jxYVMBiHAAAf","timestamp":"2025-07-17T05:14:05.594Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket connected: p0rSIwBjhHFcWkfOAAAi","service":"notification-service","timestamp":"2025-07-17T05:14:22.036Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"p0rSIwBjhHFcWkfOAAAi","timestamp":"2025-07-17T05:14:22.048Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"p0rSIwBjhHFcWkfOAAAi","timestamp":"2025-07-17T05:14:22.142Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket connected: TljX7VAX2jdLOIIUAAAl","service":"notification-service","timestamp":"2025-07-17T05:14:22.155Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"TljX7VAX2jdLOIIUAAAl","timestamp":"2025-07-17T05:14:22.165Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"body":{"jobId":"test-job","resultId":"test-result","status":"completed","userId":"test-user"},"error":"\"userId\" must be a valid GUID","level":"warn","message":"Notification validation failed","path":"/analysis-complete","service":"notification-service","timestamp":"2025-07-17T05:19:01.499Z"}
{"data":{"jobId":"3797105b-a4ac-436d-b2c8-9d812d06f52d","message":"Your analysis is ready!","resultId":"8326024e-8920-459a-9b81-cf1bfe5fcae7","status":"completed"},"event":"analysis-complete","level":"info","message":"Notification sent to user 332672ba-aa6e-43b4-80d2-5c5c95122e65","service":"notification-service","socketCount":2,"timestamp":"2025-07-17T05:19:11.310Z"}
{"jobId":"3797105b-a4ac-436d-b2c8-9d812d06f52d","level":"info","message":"Analysis complete notification processed","resultId":"8326024e-8920-459a-9b81-cf1bfe5fcae7","sent":true,"service":"notification-service","timestamp":"2025-07-17T05:19:11.310Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"ip":"::1","level":"warn","message":"Service authentication failed: Invalid service key","path":"/analysis-complete","service":"notification-service","timestamp":"2025-07-17T05:19:19.193Z"}
{"level":"info","message":"Socket service initialized","service":"notification-service","timestamp":"2025-07-17T05:22:14.001Z"}
{"level":"info","message":"Notification Service running on port 3005","service":"notification-service","timestamp":"2025-07-17T05:22:14.009Z"}
{"level":"info","message":"Socket connected: OERC-d6x_g2qjb-KAAAD","service":"notification-service","timestamp":"2025-07-17T05:22:22.140Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"OERC-d6x_g2qjb-KAAAD","timestamp":"2025-07-17T05:22:22.157Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"OERC-d6x_g2qjb-KAAAD","timestamp":"2025-07-17T05:22:22.317Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket connected: ThQQzpCgGvVTTbuQAAAG","service":"notification-service","timestamp":"2025-07-17T05:22:22.334Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"ThQQzpCgGvVTTbuQAAAG","timestamp":"2025-07-17T05:22:22.342Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"ThQQzpCgGvVTTbuQAAAG","timestamp":"2025-07-17T05:22:38.386Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket connected: KrWxJs2mpO-BPmfxAAAJ","service":"notification-service","timestamp":"2025-07-17T05:22:48.457Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"KrWxJs2mpO-BPmfxAAAJ","timestamp":"2025-07-17T05:22:48.467Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"data":{"jobId":"048dd4a0-f14b-4c7b-819e-853e946aeebb","message":"Your analysis is ready!","resultId":"a268504f-fdf4-4e1f-bac7-f3a4ae40bcd2","status":"completed"},"event":"analysis-complete","level":"info","message":"Notification sent to user 332672ba-aa6e-43b4-80d2-5c5c95122e65","service":"notification-service","socketCount":1,"timestamp":"2025-07-17T05:23:05.059Z"}
{"jobId":"048dd4a0-f14b-4c7b-819e-853e946aeebb","level":"info","message":"Analysis complete notification processed","resultId":"a268504f-fdf4-4e1f-bac7-f3a4ae40bcd2","sent":true,"service":"notification-service","timestamp":"2025-07-17T05:23:05.059Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"KrWxJs2mpO-BPmfxAAAJ","timestamp":"2025-07-17T05:25:06.087Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket connected: AH_5auAilxRvs79yAAAO","service":"notification-service","timestamp":"2025-07-17T05:25:24.102Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"AH_5auAilxRvs79yAAAO","timestamp":"2025-07-17T05:25:24.116Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"AH_5auAilxRvs79yAAAO","timestamp":"2025-07-17T05:28:00.333Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket connected: vaNkurxiNn7ZggkXAAAS","service":"notification-service","timestamp":"2025-07-17T05:28:01.537Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"vaNkurxiNn7ZggkXAAAS","timestamp":"2025-07-17T05:28:01.563Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"vaNkurxiNn7ZggkXAAAS","timestamp":"2025-07-17T05:28:10.957Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket connected: ZzfrgE5YEL_hnIn8AAAV","service":"notification-service","timestamp":"2025-07-17T05:28:10.987Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"ZzfrgE5YEL_hnIn8AAAV","timestamp":"2025-07-17T05:28:11.002Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"ZzfrgE5YEL_hnIn8AAAV","timestamp":"2025-07-17T05:28:11.167Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket connected: 99W_0rHm0FOnoU7OAAAY","service":"notification-service","timestamp":"2025-07-17T05:28:11.194Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"99W_0rHm0FOnoU7OAAAY","timestamp":"2025-07-17T05:28:11.205Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"99W_0rHm0FOnoU7OAAAY","timestamp":"2025-07-17T05:30:14.873Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket connected: dqsw50rXQOveDaOuAAAb","service":"notification-service","timestamp":"2025-07-17T05:30:15.081Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"dqsw50rXQOveDaOuAAAb","timestamp":"2025-07-17T05:30:15.086Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"dqsw50rXQOveDaOuAAAb","timestamp":"2025-07-17T05:30:17.284Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket connected: HYvTiT711wVQkQrnAAAe","service":"notification-service","timestamp":"2025-07-17T05:30:29.502Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"HYvTiT711wVQkQrnAAAe","timestamp":"2025-07-17T05:30:29.509Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"HYvTiT711wVQkQrnAAAe","timestamp":"2025-07-17T05:30:29.596Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket connected: bW9UF8LkOD37c-FtAAAh","service":"notification-service","timestamp":"2025-07-17T05:30:29.614Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"bW9UF8LkOD37c-FtAAAh","timestamp":"2025-07-17T05:30:29.621Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"bW9UF8LkOD37c-FtAAAh","timestamp":"2025-07-17T05:31:19.495Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket connected: NY9bm5uUdL64JcFWAAAk","service":"notification-service","timestamp":"2025-07-17T05:31:22.370Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"NY9bm5uUdL64JcFWAAAk","timestamp":"2025-07-17T05:31:22.384Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"data":{"jobId":"20e4119c-4ebe-4126-85d1-61c3ff723190","message":"Your analysis is ready!","resultId":"3307a622-c711-438e-be5a-9e2396119046","status":"completed"},"event":"analysis-complete","level":"info","message":"Notification sent to user 332672ba-aa6e-43b4-80d2-5c5c95122e65","service":"notification-service","socketCount":1,"timestamp":"2025-07-17T05:31:43.089Z"}
{"jobId":"20e4119c-4ebe-4126-85d1-61c3ff723190","level":"info","message":"Analysis complete notification processed","resultId":"3307a622-c711-438e-be5a-9e2396119046","sent":true,"service":"notification-service","timestamp":"2025-07-17T05:31:43.089Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"NY9bm5uUdL64JcFWAAAk","timestamp":"2025-07-17T05:31:50.432Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket connected: cdgGHZbq6LJ83j6SAAAn","service":"notification-service","timestamp":"2025-07-17T05:31:50.473Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"cdgGHZbq6LJ83j6SAAAn","timestamp":"2025-07-17T05:31:50.485Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"cdgGHZbq6LJ83j6SAAAn","timestamp":"2025-07-17T05:31:50.556Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket connected: wt0ry_p3-x_Wa_7VAAAq","service":"notification-service","timestamp":"2025-07-17T05:31:50.570Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"wt0ry_p3-x_Wa_7VAAAq","timestamp":"2025-07-17T05:31:50.581Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"wt0ry_p3-x_Wa_7VAAAq","timestamp":"2025-07-17T05:31:52.383Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket connected: vPixX-k4dvueDIgIAAAt","service":"notification-service","timestamp":"2025-07-17T05:31:55.195Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"vPixX-k4dvueDIgIAAAt","timestamp":"2025-07-17T05:31:55.210Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"vPixX-k4dvueDIgIAAAt","timestamp":"2025-07-17T05:31:55.222Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket connected: 6hDeA2eSHhroT5JZAAAw","service":"notification-service","timestamp":"2025-07-17T05:31:55.228Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"6hDeA2eSHhroT5JZAAAw","timestamp":"2025-07-17T05:31:55.237Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"6hDeA2eSHhroT5JZAAAw","timestamp":"2025-07-17T05:31:57.802Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket connected: PsUPamQLjG6wPgCJAAA0","service":"notification-service","timestamp":"2025-07-17T05:31:58.038Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"PsUPamQLjG6wPgCJAAA0","timestamp":"2025-07-17T05:31:58.058Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"PsUPamQLjG6wPgCJAAA0","timestamp":"2025-07-17T05:31:58.062Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket connected: xl1CSDhwN8VRj7MjAAA3","service":"notification-service","timestamp":"2025-07-17T05:31:58.075Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"xl1CSDhwN8VRj7MjAAA3","timestamp":"2025-07-17T05:31:58.082Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"xl1CSDhwN8VRj7MjAAA3","timestamp":"2025-07-17T05:32:15.207Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket connected: 4oy9Tlvup9_--kWaAAA7","service":"notification-service","timestamp":"2025-07-17T05:32:15.380Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"4oy9Tlvup9_--kWaAAA7","timestamp":"2025-07-17T05:32:15.402Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"4oy9Tlvup9_--kWaAAA7","timestamp":"2025-07-17T05:32:15.410Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket connected: nPV8899Wi2A76FDPAAA-","service":"notification-service","timestamp":"2025-07-17T05:32:15.411Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"nPV8899Wi2A76FDPAAA-","timestamp":"2025-07-17T05:32:15.419Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"nPV8899Wi2A76FDPAAA-","timestamp":"2025-07-17T05:32:17.074Z","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"level":"info","message":"Socket service initialized","service":"notification-service","timestamp":"2025-07-17T11:15:25.643Z"}
{"level":"info","message":"Notification Service running on port 3005","service":"notification-service","timestamp":"2025-07-17T11:15:25.656Z"}
{"level":"info","message":"Socket connected: Ok7Veyhq4oWaoLSaAAAD","service":"notification-service","timestamp":"2025-07-17T11:21:48.209Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"Ok7Veyhq4oWaoLSaAAAD","timestamp":"2025-07-17T11:21:48.228Z","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"Ok7Veyhq4oWaoLSaAAAD","timestamp":"2025-07-17T11:21:48.357Z","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"level":"info","message":"Socket connected: rDOqC1sm3LT8_-fgAAAG","service":"notification-service","timestamp":"2025-07-17T11:21:48.379Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"rDOqC1sm3LT8_-fgAAAG","timestamp":"2025-07-17T11:21:48.388Z","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"rDOqC1sm3LT8_-fgAAAG","timestamp":"2025-07-17T11:21:58.372Z","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"level":"info","message":"Socket connected: w5LaGEMpV9NBBnnnAAAJ","service":"notification-service","timestamp":"2025-07-17T11:23:12.208Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"w5LaGEMpV9NBBnnnAAAJ","timestamp":"2025-07-17T11:23:12.219Z","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"data":{"jobId":"3b303a96-5d4e-43b2-bc9a-98cbfe005d97","message":"Your analysis is ready!","resultId":"aa5dec84-37b4-4ff6-9b31-2b7efe6f4df9","status":"completed"},"event":"analysis-complete","level":"info","message":"Notification sent to user a8be340d-ff32-4113-999e-60f05d47c6b3","service":"notification-service","socketCount":1,"timestamp":"2025-07-17T11:23:34.079Z"}
{"jobId":"3b303a96-5d4e-43b2-bc9a-98cbfe005d97","level":"info","message":"Analysis complete notification processed","resultId":"aa5dec84-37b4-4ff6-9b31-2b7efe6f4df9","sent":true,"service":"notification-service","timestamp":"2025-07-17T11:23:34.080Z","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"w5LaGEMpV9NBBnnnAAAJ","timestamp":"2025-07-17T11:23:41.399Z","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"level":"info","message":"Socket connected: p51IkhQG_0bGYGoaAAAM","service":"notification-service","timestamp":"2025-07-17T11:23:41.447Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"p51IkhQG_0bGYGoaAAAM","timestamp":"2025-07-17T11:23:41.456Z","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"p51IkhQG_0bGYGoaAAAM","timestamp":"2025-07-17T11:23:41.536Z","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"level":"info","message":"Socket connected: 7TOJIebQlhPVGHEbAAAP","service":"notification-service","timestamp":"2025-07-17T11:23:41.557Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"7TOJIebQlhPVGHEbAAAP","timestamp":"2025-07-17T11:23:41.568Z","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"7TOJIebQlhPVGHEbAAAP","timestamp":"2025-07-17T11:23:43.772Z","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"level":"info","message":"Socket connected: FZjly4xrrLqzRhaCAAAS","service":"notification-service","timestamp":"2025-07-17T11:24:43.416Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"FZjly4xrrLqzRhaCAAAS","timestamp":"2025-07-17T11:24:43.430Z","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"FZjly4xrrLqzRhaCAAAS","timestamp":"2025-07-17T11:24:43.539Z","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"level":"info","message":"Socket connected: UwVDbU7TyVjJ3BdPAAAV","service":"notification-service","timestamp":"2025-07-17T11:24:43.560Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"UwVDbU7TyVjJ3BdPAAAV","timestamp":"2025-07-17T11:24:43.569Z","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"UwVDbU7TyVjJ3BdPAAAV","timestamp":"2025-07-17T11:24:46.651Z","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"level":"info","message":"Socket connected: XQnF5aWXVxoL-hXTAAAY","service":"notification-service","timestamp":"2025-07-17T11:26:39.332Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"XQnF5aWXVxoL-hXTAAAY","timestamp":"2025-07-17T11:26:39.345Z","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"XQnF5aWXVxoL-hXTAAAY","timestamp":"2025-07-17T11:26:41.310Z","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"level":"info","message":"Socket connected: XojFWi08m_7AHw6iAAAb","service":"notification-service","timestamp":"2025-07-17T11:26:41.365Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"XojFWi08m_7AHw6iAAAb","timestamp":"2025-07-17T11:26:41.373Z","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"XojFWi08m_7AHw6iAAAb","timestamp":"2025-07-17T11:26:41.466Z","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"level":"info","message":"Socket connected: 6W3b8kgSJUaVcynZAAAe","service":"notification-service","timestamp":"2025-07-17T11:26:41.486Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"6W3b8kgSJUaVcynZAAAe","timestamp":"2025-07-17T11:26:41.495Z","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"data":{"jobId":"f9d21443-18ed-4f00-ad1e-769e1dd0e7d5","message":"Your analysis is ready!","resultId":"56b54c5d-32d5-4ab6-b3fe-50dbf2d007f6","status":"completed"},"event":"analysis-complete","level":"info","message":"Notification sent to user a8be340d-ff32-4113-999e-60f05d47c6b3","service":"notification-service","socketCount":1,"timestamp":"2025-07-17T11:26:54.332Z"}
{"jobId":"f9d21443-18ed-4f00-ad1e-769e1dd0e7d5","level":"info","message":"Analysis complete notification processed","resultId":"56b54c5d-32d5-4ab6-b3fe-50dbf2d007f6","sent":true,"service":"notification-service","timestamp":"2025-07-17T11:26:54.332Z","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"6W3b8kgSJUaVcynZAAAe","timestamp":"2025-07-17T11:39:58.723Z","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"level":"info","message":"Socket connected: H3PXWV4ex22YZD4NAAAh","service":"notification-service","timestamp":"2025-07-17T11:39:58.875Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"H3PXWV4ex22YZD4NAAAh","timestamp":"2025-07-17T11:39:58.884Z","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"H3PXWV4ex22YZD4NAAAh","timestamp":"2025-07-17T12:29:09.951Z","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"level":"info","message":"Socket connected: aqbCQCPLi4gKa4pjAAAl","service":"notification-service","timestamp":"2025-07-17T12:29:37.288Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"aqbCQCPLi4gKa4pjAAAl","timestamp":"2025-07-17T12:29:37.305Z","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"aqbCQCPLi4gKa4pjAAAl","timestamp":"2025-07-17T12:29:37.543Z","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"level":"info","message":"Socket connected: _ulwQ6AtG2YRtG6aAAAo","service":"notification-service","timestamp":"2025-07-17T12:29:37.565Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"_ulwQ6AtG2YRtG6aAAAo","timestamp":"2025-07-17T12:29:37.574Z","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"_ulwQ6AtG2YRtG6aAAAo","timestamp":"2025-07-17T12:31:12.412Z","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"level":"info","message":"Socket connected: 1wu24h_pPShgIWwcAAAs","service":"notification-service","timestamp":"2025-07-17T12:31:23.148Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"1wu24h_pPShgIWwcAAAs","timestamp":"2025-07-17T12:31:23.168Z","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"1wu24h_pPShgIWwcAAAs","timestamp":"2025-07-17T12:31:23.266Z","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"level":"info","message":"Socket connected: RDMXheExQE_NhRwsAAAv","service":"notification-service","timestamp":"2025-07-17T12:31:23.284Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"RDMXheExQE_NhRwsAAAv","timestamp":"2025-07-17T12:31:23.294Z","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"RDMXheExQE_NhRwsAAAv","timestamp":"2025-07-17T12:31:24.299Z","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"level":"info","message":"Socket connected: wxcTybMJYAezfQDyAAAy","service":"notification-service","timestamp":"2025-07-17T12:31:25.660Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"wxcTybMJYAezfQDyAAAy","timestamp":"2025-07-17T12:31:25.683Z","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"wxcTybMJYAezfQDyAAAy","timestamp":"2025-07-17T12:31:25.704Z","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"level":"info","message":"Socket connected: WePxp9TfRl7gTqnIAAA1","service":"notification-service","timestamp":"2025-07-17T12:31:25.732Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"WePxp9TfRl7gTqnIAAA1","timestamp":"2025-07-17T12:31:25.740Z","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"WePxp9TfRl7gTqnIAAA1","timestamp":"2025-07-17T12:31:26.800Z","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"level":"info","message":"Socket connected: 5hAXhRkvDO3ewlrzAAA4","service":"notification-service","timestamp":"2025-07-17T12:31:29.163Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"5hAXhRkvDO3ewlrzAAA4","timestamp":"2025-07-17T12:31:29.172Z","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"data":{"jobId":"a606143b-1c09-4427-a471-8a5d16d6fcfe","message":"Your analysis is ready!","resultId":"b5de0be2-388c-419b-a234-9afadf21f727","status":"completed"},"event":"analysis-complete","level":"info","message":"Notification sent to user a8be340d-ff32-4113-999e-60f05d47c6b3","service":"notification-service","socketCount":1,"timestamp":"2025-07-17T12:31:47.040Z"}
{"jobId":"a606143b-1c09-4427-a471-8a5d16d6fcfe","level":"info","message":"Analysis complete notification processed","resultId":"b5de0be2-388c-419b-a234-9afadf21f727","sent":true,"service":"notification-service","timestamp":"2025-07-17T12:31:47.040Z","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"5hAXhRkvDO3ewlrzAAA4","timestamp":"2025-07-17T12:32:31.208Z","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"level":"info","message":"Socket connected: 0Ayw984pr0N2J-7cAAA7","service":"notification-service","timestamp":"2025-07-17T12:32:31.905Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"0Ayw984pr0N2J-7cAAA7","timestamp":"2025-07-17T12:32:31.919Z","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"0Ayw984pr0N2J-7cAAA7","timestamp":"2025-07-17T12:32:32.032Z","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"level":"info","message":"Socket connected: bPKDa-xlkY-c48FlAAA-","service":"notification-service","timestamp":"2025-07-17T12:32:32.056Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"bPKDa-xlkY-c48FlAAA-","timestamp":"2025-07-17T12:32:32.064Z","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"bPKDa-xlkY-c48FlAAA-","timestamp":"2025-07-17T12:32:44.032Z","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"level":"info","message":"Socket connected: MGzKUzYhrk7BH9wWAABC","service":"notification-service","timestamp":"2025-07-17T12:33:23.379Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"MGzKUzYhrk7BH9wWAABC","timestamp":"2025-07-17T12:33:23.395Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"MGzKUzYhrk7BH9wWAABC","timestamp":"2025-07-17T12:33:23.481Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket connected: ONrR2YVRjGuHFNZIAABF","service":"notification-service","timestamp":"2025-07-17T12:33:23.507Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"ONrR2YVRjGuHFNZIAABF","timestamp":"2025-07-17T12:33:23.515Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"ONrR2YVRjGuHFNZIAABF","timestamp":"2025-07-17T12:33:42.828Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket connected: aGw1RL1T_dvnziToAABI","service":"notification-service","timestamp":"2025-07-17T12:34:32.554Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"aGw1RL1T_dvnziToAABI","timestamp":"2025-07-17T12:34:32.568Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"data":{"jobId":"c15eb831-74ae-4906-9c20-cc6effa73c4a","message":"Your analysis is ready!","resultId":"1566a067-09e2-4973-a382-c8dc4a973299","status":"completed"},"event":"analysis-complete","level":"info","message":"Notification sent to user 4bca5142-e6b6-4b37-8d65-3020736087a8","service":"notification-service","socketCount":1,"timestamp":"2025-07-17T12:34:51.688Z"}
{"jobId":"c15eb831-74ae-4906-9c20-cc6effa73c4a","level":"info","message":"Analysis complete notification processed","resultId":"1566a067-09e2-4973-a382-c8dc4a973299","sent":true,"service":"notification-service","timestamp":"2025-07-17T12:34:51.689Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"aGw1RL1T_dvnziToAABI","timestamp":"2025-07-17T12:34:55.333Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket connected: rrxh2ZzBJ0JJE2itAABL","service":"notification-service","timestamp":"2025-07-17T12:34:55.395Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"rrxh2ZzBJ0JJE2itAABL","timestamp":"2025-07-17T12:34:55.407Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"rrxh2ZzBJ0JJE2itAABL","timestamp":"2025-07-17T12:34:55.461Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket connected: yt0RVeKUjExW3EcPAABO","service":"notification-service","timestamp":"2025-07-17T12:34:55.475Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"yt0RVeKUjExW3EcPAABO","timestamp":"2025-07-17T12:34:55.489Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"yt0RVeKUjExW3EcPAABO","timestamp":"2025-07-17T12:34:57.065Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket connected: QftjO-HkTgkOBb6QAABR","service":"notification-service","timestamp":"2025-07-17T12:37:12.983Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"QftjO-HkTgkOBb6QAABR","timestamp":"2025-07-17T12:37:12.995Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"QftjO-HkTgkOBb6QAABR","timestamp":"2025-07-17T12:37:13.116Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket connected: 8NW5VYPper7F0zBjAABU","service":"notification-service","timestamp":"2025-07-17T12:37:13.138Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"8NW5VYPper7F0zBjAABU","timestamp":"2025-07-17T12:37:13.148Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"8NW5VYPper7F0zBjAABU","timestamp":"2025-07-17T12:37:19.875Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket connected: hJTgtDli1mDSyCT5AABX","service":"notification-service","timestamp":"2025-07-17T12:49:59.992Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"hJTgtDli1mDSyCT5AABX","timestamp":"2025-07-17T12:50:00.006Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"hJTgtDli1mDSyCT5AABX","timestamp":"2025-07-17T12:50:00.150Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket connected: TlTrD2HOCYSLhAZ5AABa","service":"notification-service","timestamp":"2025-07-17T12:50:00.170Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"TlTrD2HOCYSLhAZ5AABa","timestamp":"2025-07-17T12:50:00.179Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"TlTrD2HOCYSLhAZ5AABa","timestamp":"2025-07-17T12:50:05.930Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket connected: WlTTVp0W5RVKptbnAABd","service":"notification-service","timestamp":"2025-07-17T12:51:29.113Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"WlTTVp0W5RVKptbnAABd","timestamp":"2025-07-17T12:51:29.123Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"WlTTVp0W5RVKptbnAABd","timestamp":"2025-07-17T12:51:29.235Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket connected: 3Kq9PP_psefjXn12AABg","service":"notification-service","timestamp":"2025-07-17T12:51:29.263Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"3Kq9PP_psefjXn12AABg","timestamp":"2025-07-17T12:51:29.272Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket connected: NBK0JpsB9cfOE9z7AABk","service":"notification-service","timestamp":"2025-07-17T13:27:25.810Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"NBK0JpsB9cfOE9z7AABk","timestamp":"2025-07-17T13:27:25.824Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"NBK0JpsB9cfOE9z7AABk","timestamp":"2025-07-17T13:27:26.041Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket connected: L02l5rUX60eq42ZvAABn","service":"notification-service","timestamp":"2025-07-17T13:27:26.055Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"L02l5rUX60eq42ZvAABn","timestamp":"2025-07-17T13:27:26.064Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"L02l5rUX60eq42ZvAABn","timestamp":"2025-07-17T13:27:28.591Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket connected: vHipgMCmVBb1RTvzAABq","service":"notification-service","timestamp":"2025-07-17T13:27:31.827Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"vHipgMCmVBb1RTvzAABq","timestamp":"2025-07-17T13:27:31.837Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"data":{"jobId":"77447349-73f4-4bfe-8241-8b0e54829cda","message":"Your analysis is ready!","resultId":"9a115137-36b0-4e9f-a9c4-805ff0b14110","status":"completed"},"event":"analysis-complete","level":"info","message":"Notification sent to user 4bca5142-e6b6-4b37-8d65-3020736087a8","service":"notification-service","socketCount":2,"timestamp":"2025-07-17T13:27:53.411Z"}
{"jobId":"77447349-73f4-4bfe-8241-8b0e54829cda","level":"info","message":"Analysis complete notification processed","resultId":"9a115137-36b0-4e9f-a9c4-805ff0b14110","sent":true,"service":"notification-service","timestamp":"2025-07-17T13:27:53.412Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"vHipgMCmVBb1RTvzAABq","timestamp":"2025-07-17T13:28:11.831Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket connected: LdFSxmeeX0q9fsuJAABt","service":"notification-service","timestamp":"2025-07-17T13:28:11.892Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"LdFSxmeeX0q9fsuJAABt","timestamp":"2025-07-17T13:28:11.907Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"LdFSxmeeX0q9fsuJAABt","timestamp":"2025-07-17T13:28:12.049Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket connected: c6-tCxXfhM3UqwfZAABw","service":"notification-service","timestamp":"2025-07-17T13:28:12.090Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"c6-tCxXfhM3UqwfZAABw","timestamp":"2025-07-17T13:28:12.095Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"c6-tCxXfhM3UqwfZAABw","timestamp":"2025-07-17T13:28:15.305Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket connected: k-bbWxHOaS0ZYPFXAABz","service":"notification-service","timestamp":"2025-07-17T13:28:16.750Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"k-bbWxHOaS0ZYPFXAABz","timestamp":"2025-07-17T13:28:16.765Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"k-bbWxHOaS0ZYPFXAABz","timestamp":"2025-07-17T13:28:16.815Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket connected: ztdGHePBUejiNcibAAB2","service":"notification-service","timestamp":"2025-07-17T13:28:16.854Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"ztdGHePBUejiNcibAAB2","timestamp":"2025-07-17T13:28:16.866Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"ztdGHePBUejiNcibAAB2","timestamp":"2025-07-17T13:28:17.564Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket connected: 3Y04n_vwHOhP7UCEAAB5","service":"notification-service","timestamp":"2025-07-17T13:28:18.873Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"3Y04n_vwHOhP7UCEAAB5","timestamp":"2025-07-17T13:28:18.887Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"3Y04n_vwHOhP7UCEAAB5","timestamp":"2025-07-17T13:28:18.934Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket connected: fFkBLFsKnDtoGOKbAAB8","service":"notification-service","timestamp":"2025-07-17T13:28:18.971Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"fFkBLFsKnDtoGOKbAAB8","timestamp":"2025-07-17T13:28:18.980Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"fFkBLFsKnDtoGOKbAAB8","timestamp":"2025-07-17T13:28:20.519Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket connected: dBjTlYUMOlXcSjbqAAB_","service":"notification-service","timestamp":"2025-07-17T13:28:21.650Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"dBjTlYUMOlXcSjbqAAB_","timestamp":"2025-07-17T13:28:21.677Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"dBjTlYUMOlXcSjbqAAB_","timestamp":"2025-07-17T13:28:21.703Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket connected: EgvfHj3AH0EDOgJvAACC","service":"notification-service","timestamp":"2025-07-17T13:28:21.738Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"EgvfHj3AH0EDOgJvAACC","timestamp":"2025-07-17T13:28:21.750Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"EgvfHj3AH0EDOgJvAACC","timestamp":"2025-07-17T13:28:25.079Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket connected: i9yHWtFuLlpqQbAAAACG","service":"notification-service","timestamp":"2025-07-17T13:28:25.577Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"i9yHWtFuLlpqQbAAAACG","timestamp":"2025-07-17T13:28:25.594Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"i9yHWtFuLlpqQbAAAACG","timestamp":"2025-07-17T13:28:25.657Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket connected: m3kiqvTUAtROgDO8AACJ","service":"notification-service","timestamp":"2025-07-17T13:28:25.705Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"m3kiqvTUAtROgDO8AACJ","timestamp":"2025-07-17T13:28:25.719Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"m3kiqvTUAtROgDO8AACJ","timestamp":"2025-07-17T13:28:30.426Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket connected: KpXlPFbpKvOPXW1VAACM","service":"notification-service","timestamp":"2025-07-17T13:28:33.514Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"KpXlPFbpKvOPXW1VAACM","timestamp":"2025-07-17T13:28:33.522Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"data":{"jobId":"b54cdc93-aa62-4532-aa66-6bed40fc822f","message":"Your analysis is ready!","resultId":"bde99b66-2894-467f-bb1d-4cae156e3dd8","status":"completed"},"event":"analysis-complete","level":"info","message":"Notification sent to user 4bca5142-e6b6-4b37-8d65-3020736087a8","service":"notification-service","socketCount":2,"timestamp":"2025-07-17T13:28:53.819Z"}
{"jobId":"b54cdc93-aa62-4532-aa66-6bed40fc822f","level":"info","message":"Analysis complete notification processed","resultId":"bde99b66-2894-467f-bb1d-4cae156e3dd8","sent":true,"service":"notification-service","timestamp":"2025-07-17T13:28:53.819Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"KpXlPFbpKvOPXW1VAACM","timestamp":"2025-07-17T13:29:52.373Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket connected: VR4WNwezpcBEtAcmAACO","service":"notification-service","timestamp":"2025-07-17T13:29:52.389Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"VR4WNwezpcBEtAcmAACO","timestamp":"2025-07-17T13:29:52.398Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"VR4WNwezpcBEtAcmAACO","timestamp":"2025-07-17T13:29:57.005Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket connected: KAZE4gZAIvlTWWC4AACR","service":"notification-service","timestamp":"2025-07-17T13:29:57.075Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"KAZE4gZAIvlTWWC4AACR","timestamp":"2025-07-17T13:29:57.087Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"KAZE4gZAIvlTWWC4AACR","timestamp":"2025-07-17T13:29:57.241Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket connected: XYQqX3KRvCT4YMXBAACU","service":"notification-service","timestamp":"2025-07-17T13:29:57.275Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"XYQqX3KRvCT4YMXBAACU","timestamp":"2025-07-17T13:29:57.285Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"XYQqX3KRvCT4YMXBAACU","timestamp":"2025-07-17T13:29:58.565Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket connected: 8X3ipbbN1rPrmeUbAACX","service":"notification-service","timestamp":"2025-07-17T13:30:00.870Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"8X3ipbbN1rPrmeUbAACX","timestamp":"2025-07-17T13:30:00.893Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"8X3ipbbN1rPrmeUbAACX","timestamp":"2025-07-17T13:30:00.958Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket connected: zW9jykbPwHT2VBlQAACa","service":"notification-service","timestamp":"2025-07-17T13:30:01.004Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"zW9jykbPwHT2VBlQAACa","timestamp":"2025-07-17T13:30:01.015Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"zW9jykbPwHT2VBlQAACa","timestamp":"2025-07-17T13:30:01.958Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket connected: HlbaQbzLE5xL8-V6AACd","service":"notification-service","timestamp":"2025-07-17T13:30:03.509Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"HlbaQbzLE5xL8-V6AACd","timestamp":"2025-07-17T13:30:03.538Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"HlbaQbzLE5xL8-V6AACd","timestamp":"2025-07-17T13:30:03.597Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket connected: 3NaHhKs-5p8KnRj3AACg","service":"notification-service","timestamp":"2025-07-17T13:30:03.652Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"3NaHhKs-5p8KnRj3AACg","timestamp":"2025-07-17T13:30:03.667Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"3NaHhKs-5p8KnRj3AACg","timestamp":"2025-07-17T13:30:04.202Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket connected: bkeroIM3qaOXq_oHAACj","service":"notification-service","timestamp":"2025-07-17T13:30:05.448Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"bkeroIM3qaOXq_oHAACj","timestamp":"2025-07-17T13:30:05.490Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"bkeroIM3qaOXq_oHAACj","timestamp":"2025-07-17T13:30:05.516Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket connected: s_GsG5AAmaQZeLQHAACm","service":"notification-service","timestamp":"2025-07-17T13:30:05.517Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"s_GsG5AAmaQZeLQHAACm","timestamp":"2025-07-17T13:30:05.529Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"s_GsG5AAmaQZeLQHAACm","timestamp":"2025-07-17T13:30:06.455Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket connected: U-cuaWmFnC1oLoO6AACp","service":"notification-service","timestamp":"2025-07-17T13:30:08.006Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"U-cuaWmFnC1oLoO6AACp","timestamp":"2025-07-17T13:30:08.035Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"U-cuaWmFnC1oLoO6AACp","timestamp":"2025-07-17T13:30:08.052Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket connected: Zr9rJCIHIFGa1nsIAACs","service":"notification-service","timestamp":"2025-07-17T13:30:08.053Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"Zr9rJCIHIFGa1nsIAACs","timestamp":"2025-07-17T13:30:08.065Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"Zr9rJCIHIFGa1nsIAACs","timestamp":"2025-07-17T13:30:09.295Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket connected: Ptbz9aaZoE0CZzbBAACv","service":"notification-service","timestamp":"2025-07-17T13:30:10.333Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"Ptbz9aaZoE0CZzbBAACv","timestamp":"2025-07-17T13:30:10.377Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"Ptbz9aaZoE0CZzbBAACv","timestamp":"2025-07-17T13:30:10.379Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket connected: pzzSV4ZWTsV3k5uOAACy","service":"notification-service","timestamp":"2025-07-17T13:30:10.412Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"pzzSV4ZWTsV3k5uOAACy","timestamp":"2025-07-17T13:30:10.426Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"pzzSV4ZWTsV3k5uOAACy","timestamp":"2025-07-17T13:32:38.318Z","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"level":"info","message":"Socket connected: njQVRcYJHLvK15gsAAC2","service":"notification-service","timestamp":"2025-07-17T13:32:56.172Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"njQVRcYJHLvK15gsAAC2","timestamp":"2025-07-17T13:32:56.187Z","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"njQVRcYJHLvK15gsAAC2","timestamp":"2025-07-17T13:32:56.279Z","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"level":"info","message":"Socket connected: _7e8rKBmce7dmNrbAAC5","service":"notification-service","timestamp":"2025-07-17T13:32:56.299Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"_7e8rKBmce7dmNrbAAC5","timestamp":"2025-07-17T13:32:56.304Z","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"_7e8rKBmce7dmNrbAAC5","timestamp":"2025-07-17T13:33:43.410Z","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"level":"info","message":"Socket connected: 7jl0aceV_5xxcqCpAAC8","service":"notification-service","timestamp":"2025-07-17T13:33:46.401Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"7jl0aceV_5xxcqCpAAC8","timestamp":"2025-07-17T13:33:46.411Z","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"data":{"jobId":"11565c0e-1580-47b1-82c0-c4a653a80db3","message":"Your analysis is ready!","resultId":"47dc3ba5-032f-4d28-9a5d-fb2c6d97429b","status":"completed"},"event":"analysis-complete","level":"info","message":"Notification sent to user 4edcdf45-2eb3-49a8-9f88-69316c852cfc","service":"notification-service","socketCount":1,"timestamp":"2025-07-17T13:34:05.215Z"}
{"jobId":"11565c0e-1580-47b1-82c0-c4a653a80db3","level":"info","message":"Analysis complete notification processed","resultId":"47dc3ba5-032f-4d28-9a5d-fb2c6d97429b","sent":true,"service":"notification-service","timestamp":"2025-07-17T13:34:05.215Z","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"7jl0aceV_5xxcqCpAAC8","timestamp":"2025-07-17T13:34:16.971Z","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"level":"info","message":"Socket connected: 0k1fO2gDBXFq0oCrAAC_","service":"notification-service","timestamp":"2025-07-17T13:34:17.038Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"0k1fO2gDBXFq0oCrAAC_","timestamp":"2025-07-17T13:34:17.051Z","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"0k1fO2gDBXFq0oCrAAC_","timestamp":"2025-07-17T13:34:17.182Z","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"level":"info","message":"Socket connected: 5YD4PUfmZRTacrE0AADC","service":"notification-service","timestamp":"2025-07-17T13:34:17.248Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"5YD4PUfmZRTacrE0AADC","timestamp":"2025-07-17T13:34:17.253Z","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"5YD4PUfmZRTacrE0AADC","timestamp":"2025-07-17T13:38:28.589Z","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"level":"info","message":"Socket connected: D_EEHN4LS1AUszenAADF","service":"notification-service","timestamp":"2025-07-17T13:38:31.359Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"D_EEHN4LS1AUszenAADF","timestamp":"2025-07-17T13:38:31.367Z","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"D_EEHN4LS1AUszenAADF","timestamp":"2025-07-17T13:39:22.967Z","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"level":"info","message":"Socket connected: hWzCsf_cDTdef-T4AADI","service":"notification-service","timestamp":"2025-07-17T13:39:23.031Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"hWzCsf_cDTdef-T4AADI","timestamp":"2025-07-17T13:39:23.044Z","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"hWzCsf_cDTdef-T4AADI","timestamp":"2025-07-17T13:39:23.209Z","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"level":"info","message":"Socket connected: qyWqJur_ZA0_aQOAAADL","service":"notification-service","timestamp":"2025-07-17T13:39:23.242Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"qyWqJur_ZA0_aQOAAADL","timestamp":"2025-07-17T13:39:23.251Z","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"qyWqJur_ZA0_aQOAAADL","timestamp":"2025-07-17T13:39:24.563Z","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"level":"info","message":"Socket connected: fk6Za8i8dsGtbIF_AADO","service":"notification-service","timestamp":"2025-07-17T13:39:28.221Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"fk6Za8i8dsGtbIF_AADO","timestamp":"2025-07-17T13:39:28.231Z","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"fk6Za8i8dsGtbIF_AADO","timestamp":"2025-07-17T13:39:28.298Z","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"level":"info","message":"Socket connected: DlNOyUdnK-DKEvtPAADR","service":"notification-service","timestamp":"2025-07-17T13:39:28.333Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"DlNOyUdnK-DKEvtPAADR","timestamp":"2025-07-17T13:39:28.341Z","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"DlNOyUdnK-DKEvtPAADR","timestamp":"2025-07-17T13:39:32.983Z","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"level":"info","message":"Socket service initialized","service":"notification-service","timestamp":"2025-07-17T13:46:24.683Z"}
{"level":"info","message":"Notification Service running on port 3005","service":"notification-service","timestamp":"2025-07-17T13:46:24.693Z"}
{"level":"info","message":"Socket connected: i1h8fByOP2QjwqhjAAAD","service":"notification-service","timestamp":"2025-07-17T13:46:46.990Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"i1h8fByOP2QjwqhjAAAD","timestamp":"2025-07-17T13:46:47.014Z","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"i1h8fByOP2QjwqhjAAAD","timestamp":"2025-07-17T13:46:47.277Z","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"level":"info","message":"Socket connected: WwrLI3B78aw87DHIAAAG","service":"notification-service","timestamp":"2025-07-17T13:46:47.296Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"WwrLI3B78aw87DHIAAAG","timestamp":"2025-07-17T13:46:47.307Z","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"WwrLI3B78aw87DHIAAAG","timestamp":"2025-07-17T13:46:53.454Z","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"level":"info","message":"Socket connected: bTJcSxkMBGBegmZBAAAJ","service":"notification-service","timestamp":"2025-07-17T13:46:59.911Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"bTJcSxkMBGBegmZBAAAJ","timestamp":"2025-07-17T13:46:59.922Z","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"data":{"jobId":"a7a35f0b-b8d6-4cc1-8b96-a4e1d372fc49","message":"Your analysis is ready!","resultId":"da7acc61-e242-405b-b189-fb02bf80b945","status":"completed"},"event":"analysis-complete","level":"info","message":"Notification sent to user 4edcdf45-2eb3-49a8-9f88-69316c852cfc","service":"notification-service","socketCount":1,"timestamp":"2025-07-17T13:47:20.865Z"}
{"jobId":"a7a35f0b-b8d6-4cc1-8b96-a4e1d372fc49","level":"info","message":"Analysis complete notification processed","resultId":"da7acc61-e242-405b-b189-fb02bf80b945","sent":true,"service":"notification-service","timestamp":"2025-07-17T13:47:20.866Z","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"bTJcSxkMBGBegmZBAAAJ","timestamp":"2025-07-17T13:47:32.905Z","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"level":"info","message":"Socket connected: wEUIWkAINxRN0qOUAAAM","service":"notification-service","timestamp":"2025-07-17T13:47:32.969Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"wEUIWkAINxRN0qOUAAAM","timestamp":"2025-07-17T13:47:32.981Z","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"wEUIWkAINxRN0qOUAAAM","timestamp":"2025-07-17T13:47:33.107Z","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"level":"info","message":"Socket connected: am_7W47QGeViC1QWAAAP","service":"notification-service","timestamp":"2025-07-17T13:47:33.142Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"am_7W47QGeViC1QWAAAP","timestamp":"2025-07-17T13:47:33.207Z","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"am_7W47QGeViC1QWAAAP","timestamp":"2025-07-17T13:47:36.222Z","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"level":"info","message":"Socket connected: urLksU2v8wWdy972AAAS","service":"notification-service","timestamp":"2025-07-17T13:47:39.747Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"urLksU2v8wWdy972AAAS","timestamp":"2025-07-17T13:47:39.759Z","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"urLksU2v8wWdy972AAAS","timestamp":"2025-07-17T13:47:39.798Z","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"level":"info","message":"Socket connected: YMSOBb5XA2UXBrGsAAAV","service":"notification-service","timestamp":"2025-07-17T13:47:39.816Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"YMSOBb5XA2UXBrGsAAAV","timestamp":"2025-07-17T13:47:39.826Z","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"YMSOBb5XA2UXBrGsAAAV","timestamp":"2025-07-17T13:47:40.675Z","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"level":"info","message":"Socket connected: Z9v-Qh9wYe5bLCjFAAAY","service":"notification-service","timestamp":"2025-07-17T13:47:41.625Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"Z9v-Qh9wYe5bLCjFAAAY","timestamp":"2025-07-17T13:47:41.644Z","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"Z9v-Qh9wYe5bLCjFAAAY","timestamp":"2025-07-17T13:47:41.676Z","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"level":"info","message":"Socket connected: MwSwsc43RH433RQ5AAAb","service":"notification-service","timestamp":"2025-07-17T13:47:41.699Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"MwSwsc43RH433RQ5AAAb","timestamp":"2025-07-17T13:47:41.709Z","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"MwSwsc43RH433RQ5AAAb","timestamp":"2025-07-17T13:47:42.938Z","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"level":"info","message":"Socket service initialized","service":"notification-service","timestamp":"2025-07-17T13:55:52.635Z"}
{"level":"info","message":"Notification Service running on port 3005","service":"notification-service","timestamp":"2025-07-17T13:55:52.642Z"}
{"level":"info","message":"Socket connected: 3WrOlrZ_ZvZkA_HrAAAD","service":"notification-service","timestamp":"2025-07-17T13:56:31.411Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"3WrOlrZ_ZvZkA_HrAAAD","timestamp":"2025-07-17T13:56:31.428Z","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"3WrOlrZ_ZvZkA_HrAAAD","timestamp":"2025-07-17T13:56:31.525Z","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"level":"info","message":"Socket connected: aln95nd6leGyEs3-AAAG","service":"notification-service","timestamp":"2025-07-17T13:56:31.543Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"aln95nd6leGyEs3-AAAG","timestamp":"2025-07-17T13:56:31.550Z","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"aln95nd6leGyEs3-AAAG","timestamp":"2025-07-17T13:57:18.685Z","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"level":"info","message":"Socket connected: 6opNvPP0OR2OtMy5AAAK","service":"notification-service","timestamp":"2025-07-17T13:57:40.219Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"6opNvPP0OR2OtMy5AAAK","timestamp":"2025-07-17T13:57:40.232Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"6opNvPP0OR2OtMy5AAAK","timestamp":"2025-07-17T13:57:40.348Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket connected: aHk5yBcqRL8olvLsAAAN","service":"notification-service","timestamp":"2025-07-17T13:57:40.352Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"aHk5yBcqRL8olvLsAAAN","timestamp":"2025-07-17T13:57:40.362Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"aHk5yBcqRL8olvLsAAAN","timestamp":"2025-07-17T13:57:43.331Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket connected: FSttEvmCM1EQjs8JAAAP","service":"notification-service","timestamp":"2025-07-17T13:58:22.117Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"FSttEvmCM1EQjs8JAAAP","timestamp":"2025-07-17T13:58:22.145Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"FSttEvmCM1EQjs8JAAAP","timestamp":"2025-07-17T13:58:39.948Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket connected: xXS9Yi9WquaHCLFpAAAS","service":"notification-service","timestamp":"2025-07-17T13:58:39.978Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"xXS9Yi9WquaHCLFpAAAS","timestamp":"2025-07-17T13:58:39.989Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"data":{"jobId":"fafd7290-7212-4066-b274-2da3388938ba","message":"Your analysis is ready!","resultId":"9f70db1e-9f41-4572-8c41-03a09f400144","status":"completed"},"event":"analysis-complete","level":"info","message":"Notification sent to user 7c99b87e-4b53-4040-8b29-980ba4e1321e","service":"notification-service","socketCount":1,"timestamp":"2025-07-17T13:59:01.342Z"}
{"jobId":"fafd7290-7212-4066-b274-2da3388938ba","level":"info","message":"Analysis complete notification processed","resultId":"9f70db1e-9f41-4572-8c41-03a09f400144","sent":true,"service":"notification-service","timestamp":"2025-07-17T13:59:01.343Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"xXS9Yi9WquaHCLFpAAAS","timestamp":"2025-07-17T13:59:03.351Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket connected: 9hXLCWEWRRnmsJoNAAAV","service":"notification-service","timestamp":"2025-07-17T14:05:25.644Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"9hXLCWEWRRnmsJoNAAAV","timestamp":"2025-07-17T14:05:25.655Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"9hXLCWEWRRnmsJoNAAAV","timestamp":"2025-07-17T14:05:25.817Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket connected: 3sreZsFn5UJc1Gc8AAAY","service":"notification-service","timestamp":"2025-07-17T14:05:25.852Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"3sreZsFn5UJc1Gc8AAAY","timestamp":"2025-07-17T14:05:25.857Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket connected: 0hzlJ0Un9Kvd86ArAAAc","service":"notification-service","timestamp":"2025-07-17T14:05:32.883Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"0hzlJ0Un9Kvd86ArAAAc","timestamp":"2025-07-17T14:05:32.892Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"0hzlJ0Un9Kvd86ArAAAc","timestamp":"2025-07-17T14:05:32.999Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket connected: ZFkNyeh132bOgqWpAAAf","service":"notification-service","timestamp":"2025-07-17T14:05:33.029Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"ZFkNyeh132bOgqWpAAAf","timestamp":"2025-07-17T14:05:33.048Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"3sreZsFn5UJc1Gc8AAAY","timestamp":"2025-07-17T14:06:39.516Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"ZFkNyeh132bOgqWpAAAf","timestamp":"2025-07-17T14:06:46.759Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket connected: q42UIe5HjDoQVXjFAAAi","service":"notification-service","timestamp":"2025-07-17T14:06:48.630Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"q42UIe5HjDoQVXjFAAAi","timestamp":"2025-07-17T14:06:48.638Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"data":{"jobId":"c03694f1-84f7-4a96-8169-a9b2858f1217","message":"Your analysis is ready!","resultId":"b3bc9708-3f51-43f3-9ba5-30ad50153870","status":"completed"},"event":"analysis-complete","level":"info","message":"Notification sent to user 7c99b87e-4b53-4040-8b29-980ba4e1321e","service":"notification-service","socketCount":1,"timestamp":"2025-07-17T14:07:06.701Z"}
{"jobId":"c03694f1-84f7-4a96-8169-a9b2858f1217","level":"info","message":"Analysis complete notification processed","resultId":"b3bc9708-3f51-43f3-9ba5-30ad50153870","sent":true,"service":"notification-service","timestamp":"2025-07-17T14:07:06.701Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"q42UIe5HjDoQVXjFAAAi","timestamp":"2025-07-17T14:07:08.706Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket connected: 9laCSVScLPw9zHscAAAl","service":"notification-service","timestamp":"2025-07-17T14:07:14.590Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"9laCSVScLPw9zHscAAAl","timestamp":"2025-07-17T14:07:14.601Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"9laCSVScLPw9zHscAAAl","timestamp":"2025-07-17T14:07:14.691Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket connected: UdAq5RME51j8CE-LAAAo","service":"notification-service","timestamp":"2025-07-17T14:07:14.711Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"UdAq5RME51j8CE-LAAAo","timestamp":"2025-07-17T14:07:14.721Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"UdAq5RME51j8CE-LAAAo","timestamp":"2025-07-17T14:07:17.761Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket connected: vln0UA2nk-lQEphqAAAr","service":"notification-service","timestamp":"2025-07-17T14:07:19.032Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"vln0UA2nk-lQEphqAAAr","timestamp":"2025-07-17T14:07:19.038Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"vln0UA2nk-lQEphqAAAr","timestamp":"2025-07-17T14:07:20.262Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket connected: TOOwgGyLhzQAYGZ7AAAu","service":"notification-service","timestamp":"2025-07-17T14:07:20.318Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"TOOwgGyLhzQAYGZ7AAAu","timestamp":"2025-07-17T14:07:20.330Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"TOOwgGyLhzQAYGZ7AAAu","timestamp":"2025-07-17T14:07:20.361Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket connected: nAanpfrOZiv7XjDnAAAx","service":"notification-service","timestamp":"2025-07-17T14:07:20.375Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"nAanpfrOZiv7XjDnAAAx","timestamp":"2025-07-17T14:07:20.382Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"nAanpfrOZiv7XjDnAAAx","timestamp":"2025-07-17T14:07:31.584Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket connected: qDuLVIW5ijVVWoGAAAA0","service":"notification-service","timestamp":"2025-07-17T14:07:31.715Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"qDuLVIW5ijVVWoGAAAA0","timestamp":"2025-07-17T14:07:31.724Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"qDuLVIW5ijVVWoGAAAA0","timestamp":"2025-07-17T14:07:32.741Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket connected: pIbt6AdC5c9hG06MAAA3","service":"notification-service","timestamp":"2025-07-17T14:07:32.798Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"pIbt6AdC5c9hG06MAAA3","timestamp":"2025-07-17T14:07:32.805Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"pIbt6AdC5c9hG06MAAA3","timestamp":"2025-07-17T14:07:38.240Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"event":"analysis-complete","level":"warn","message":"No active connections for user 7c99b87e-4b53-4040-8b29-980ba4e1321e","service":"notification-service","timestamp":"2025-07-17T14:07:39.435Z"}
{"jobId":"adf69211-2b12-4a93-8bc2-6aec278a2a4f","level":"info","message":"Analysis complete notification processed","resultId":"b9f98f4e-aafc-4a2b-87cb-f1f8b08b24dd","sent":false,"service":"notification-service","timestamp":"2025-07-17T14:07:39.437Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket connected: AEb8_Fs1REy80Fz2AAA6","service":"notification-service","timestamp":"2025-07-17T14:07:43.129Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"AEb8_Fs1REy80Fz2AAA6","timestamp":"2025-07-17T14:07:43.135Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"AEb8_Fs1REy80Fz2AAA6","timestamp":"2025-07-17T14:07:43.203Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket connected: B5O1qWnF5zMU6eO_AAA9","service":"notification-service","timestamp":"2025-07-17T14:07:43.218Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"B5O1qWnF5zMU6eO_AAA9","timestamp":"2025-07-17T14:07:43.224Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"B5O1qWnF5zMU6eO_AAA9","timestamp":"2025-07-17T14:07:46.906Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket connected: zDJo5n6jjbCbTQ7DAABA","service":"notification-service","timestamp":"2025-07-17T14:07:50.439Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"zDJo5n6jjbCbTQ7DAABA","timestamp":"2025-07-17T14:07:50.450Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"zDJo5n6jjbCbTQ7DAABA","timestamp":"2025-07-17T14:07:50.479Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket connected: a6SNbBrwLoLDF9TiAABD","service":"notification-service","timestamp":"2025-07-17T14:07:50.499Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"a6SNbBrwLoLDF9TiAABD","timestamp":"2025-07-17T14:07:50.508Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"a6SNbBrwLoLDF9TiAABD","timestamp":"2025-07-17T14:07:51.342Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket connected: FSLSYbr8t1Non5RgAABG","service":"notification-service","timestamp":"2025-07-17T14:07:53.505Z"}
{"level":"info","message":"Unauthenticated socket disconnected: FSLSYbr8t1Non5RgAABG","service":"notification-service","timestamp":"2025-07-17T14:07:53.570Z"}
{"level":"info","message":"Socket connected: PnaAlmkrzyUkM7pOAABJ","service":"notification-service","timestamp":"2025-07-17T14:07:53.580Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"PnaAlmkrzyUkM7pOAABJ","timestamp":"2025-07-17T14:07:53.587Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"PnaAlmkrzyUkM7pOAABJ","timestamp":"2025-07-17T14:07:55.835Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket connected: tD3C3PdTrPc9XCp8AABM","service":"notification-service","timestamp":"2025-07-17T14:07:56.407Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"tD3C3PdTrPc9XCp8AABM","timestamp":"2025-07-17T14:07:56.416Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"tD3C3PdTrPc9XCp8AABM","timestamp":"2025-07-17T14:07:56.435Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket connected: yXm8Drpfm22LcjcOAABP","service":"notification-service","timestamp":"2025-07-17T14:07:56.446Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"yXm8Drpfm22LcjcOAABP","timestamp":"2025-07-17T14:07:56.454Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"yXm8Drpfm22LcjcOAABP","timestamp":"2025-07-17T14:07:57.885Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket connected: u2GAE9su9FNkIvUzAABS","service":"notification-service","timestamp":"2025-07-17T14:07:57.918Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"u2GAE9su9FNkIvUzAABS","timestamp":"2025-07-17T14:07:57.927Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"u2GAE9su9FNkIvUzAABS","timestamp":"2025-07-17T14:08:02.702Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"warn","message":"Socket FSLSYbr8t1Non5RgAABG not authenticated within timeout","service":"notification-service","timestamp":"2025-07-17T14:08:03.506Z"}
{"level":"info","message":"Socket service initialized","service":"notification-service","timestamp":"2025-07-17T20:53:12.826Z"}
{"level":"info","message":"Notification Service running on port 3005","service":"notification-service","timestamp":"2025-07-17T20:53:12.832Z"}
{"level":"info","message":"Socket connected: VfHhFddzq6GAvWDCAAAD","service":"notification-service","timestamp":"2025-07-17T20:53:27.489Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"VfHhFddzq6GAvWDCAAAD","timestamp":"2025-07-17T20:53:27.517Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"VfHhFddzq6GAvWDCAAAD","timestamp":"2025-07-17T20:53:27.695Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket connected: s79E2PzAL7kp73oKAAAG","service":"notification-service","timestamp":"2025-07-17T20:53:27.732Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"s79E2PzAL7kp73oKAAAG","timestamp":"2025-07-17T20:53:27.751Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"s79E2PzAL7kp73oKAAAG","timestamp":"2025-07-17T20:53:30.982Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket connected: hQWeP9pUPNFubQ5KAAAK","service":"notification-service","timestamp":"2025-07-17T20:53:42.953Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"hQWeP9pUPNFubQ5KAAAK","timestamp":"2025-07-17T20:53:42.969Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"hQWeP9pUPNFubQ5KAAAK","timestamp":"2025-07-17T20:53:43.056Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: CHCbLqeIXlJ5bRgpAAAN","service":"notification-service","timestamp":"2025-07-17T20:53:43.070Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"CHCbLqeIXlJ5bRgpAAAN","timestamp":"2025-07-17T20:53:43.086Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"CHCbLqeIXlJ5bRgpAAAN","timestamp":"2025-07-17T20:53:45.849Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: e7M8ucPJ4PemSRndAAAQ","service":"notification-service","timestamp":"2025-07-17T20:53:50.508Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"e7M8ucPJ4PemSRndAAAQ","timestamp":"2025-07-17T20:53:50.527Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"data":{"jobId":"67abbbb1-0b2e-43e8-ad72-c05f7a3e4b75","message":"Your analysis is ready!","resultId":"d6cb4aec-7620-430f-8400-d6a5a46ea8b4","status":"completed"},"event":"analysis-complete","level":"info","message":"Notification sent to user b9016e17-3f5c-4849-874d-e5b6e9459f2b","service":"notification-service","socketCount":1,"timestamp":"2025-07-17T20:54:07.498Z"}
{"jobId":"67abbbb1-0b2e-43e8-ad72-c05f7a3e4b75","level":"info","message":"Analysis complete notification processed","resultId":"d6cb4aec-7620-430f-8400-d6a5a46ea8b4","sent":true,"service":"notification-service","timestamp":"2025-07-17T20:54:07.498Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"e7M8ucPJ4PemSRndAAAQ","timestamp":"2025-07-17T20:54:09.520Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: Zj3oBC-Q49Psow_yAAAT","service":"notification-service","timestamp":"2025-07-17T20:54:14.163Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"Zj3oBC-Q49Psow_yAAAT","timestamp":"2025-07-17T20:54:14.175Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"Zj3oBC-Q49Psow_yAAAT","timestamp":"2025-07-17T20:54:14.270Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: qH-s6qbLEct7GDVHAAAW","service":"notification-service","timestamp":"2025-07-17T20:54:14.285Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"qH-s6qbLEct7GDVHAAAW","timestamp":"2025-07-17T20:54:14.295Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"qH-s6qbLEct7GDVHAAAW","timestamp":"2025-07-17T20:55:37.605Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: I0HOytOWmP4Gnk7FAAAZ","service":"notification-service","timestamp":"2025-07-17T20:55:38.905Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"I0HOytOWmP4Gnk7FAAAZ","timestamp":"2025-07-17T20:55:38.914Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"data":{"jobId":"4ff4cd34-3ed9-4d46-b23c-263f7b4b37e2","message":"Your analysis is ready!","resultId":"3ffe0b09-a497-48fc-9edd-b7889bb63aa1","status":"completed"},"event":"analysis-complete","level":"info","message":"Notification sent to user b9016e17-3f5c-4849-874d-e5b6e9459f2b","service":"notification-service","socketCount":1,"timestamp":"2025-07-17T20:55:58.458Z"}
{"jobId":"4ff4cd34-3ed9-4d46-b23c-263f7b4b37e2","level":"info","message":"Analysis complete notification processed","resultId":"3ffe0b09-a497-48fc-9edd-b7889bb63aa1","sent":true,"service":"notification-service","timestamp":"2025-07-17T20:55:58.459Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"I0HOytOWmP4Gnk7FAAAZ","timestamp":"2025-07-17T20:56:00.464Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: zNdYZqciPes3MqxOAAAc","service":"notification-service","timestamp":"2025-07-17T20:56:17.293Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"zNdYZqciPes3MqxOAAAc","timestamp":"2025-07-17T20:56:17.302Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"zNdYZqciPes3MqxOAAAc","timestamp":"2025-07-17T20:56:17.396Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: Mrkn99HjK7bSeKxHAAAf","service":"notification-service","timestamp":"2025-07-17T20:56:17.417Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"Mrkn99HjK7bSeKxHAAAf","timestamp":"2025-07-17T20:56:17.428Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"Mrkn99HjK7bSeKxHAAAf","timestamp":"2025-07-17T20:56:18.027Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: 7QTR_i78C2ydKl1IAAAi","service":"notification-service","timestamp":"2025-07-17T20:56:19.587Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"7QTR_i78C2ydKl1IAAAi","timestamp":"2025-07-17T20:56:19.595Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"data":{"jobId":"4541ee73-4344-48c3-8e27-1738aa858cb0","message":"Your analysis is ready!","resultId":"64870042-696a-4c01-89a7-7001020efaaa","status":"completed"},"event":"analysis-complete","level":"info","message":"Notification sent to user b9016e17-3f5c-4849-874d-e5b6e9459f2b","service":"notification-service","socketCount":1,"timestamp":"2025-07-17T20:56:34.535Z"}
{"jobId":"4541ee73-4344-48c3-8e27-1738aa858cb0","level":"info","message":"Analysis complete notification processed","resultId":"64870042-696a-4c01-89a7-7001020efaaa","sent":true,"service":"notification-service","timestamp":"2025-07-17T20:56:34.535Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"7QTR_i78C2ydKl1IAAAi","timestamp":"2025-07-17T20:56:36.543Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: MmU1B1rxfnzT7nUhAAAm","service":"notification-service","timestamp":"2025-07-17T21:23:09.144Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"MmU1B1rxfnzT7nUhAAAm","timestamp":"2025-07-17T21:23:09.159Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"MmU1B1rxfnzT7nUhAAAm","timestamp":"2025-07-17T21:23:09.285Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: I4x0qi8QDGpaksLJAAAp","service":"notification-service","timestamp":"2025-07-17T21:23:09.301Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"I4x0qi8QDGpaksLJAAAp","timestamp":"2025-07-17T21:23:09.308Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"I4x0qi8QDGpaksLJAAAp","timestamp":"2025-07-17T21:23:12.995Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: lrg5dOeKPpxDFG2VAAAs","service":"notification-service","timestamp":"2025-07-17T21:23:13.046Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"lrg5dOeKPpxDFG2VAAAs","timestamp":"2025-07-17T21:23:13.054Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"lrg5dOeKPpxDFG2VAAAs","timestamp":"2025-07-17T21:23:13.921Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: vMs4fqXXgw0ux8UYAAAw","service":"notification-service","timestamp":"2025-07-17T21:23:17.703Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"vMs4fqXXgw0ux8UYAAAw","timestamp":"2025-07-17T21:23:17.717Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"vMs4fqXXgw0ux8UYAAAw","timestamp":"2025-07-17T21:23:17.743Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: S-yVafTBjWKYt-TDAAAz","service":"notification-service","timestamp":"2025-07-17T21:23:17.761Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"S-yVafTBjWKYt-TDAAAz","timestamp":"2025-07-17T21:23:17.767Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"S-yVafTBjWKYt-TDAAAz","timestamp":"2025-07-17T21:23:27.344Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket service initialized","service":"notification-service","timestamp":"2025-07-17T22:30:01.830Z"}
{"level":"info","message":"Notification Service running on port 3005","service":"notification-service","timestamp":"2025-07-17T22:30:01.837Z"}
{"level":"info","message":"Socket service initialized","service":"notification-service","timestamp":"2025-07-17T22:48:36.054Z"}
{"level":"info","message":"Notification Service running on port 3005","service":"notification-service","timestamp":"2025-07-17T22:48:36.062Z"}
{"level":"info","message":"Socket service initialized","service":"notification-service","timestamp":"2025-07-17T22:55:25.295Z"}
{"level":"info","message":"Notification Service running on port 3005","service":"notification-service","timestamp":"2025-07-17T22:55:25.303Z"}
{"level":"info","message":"Socket service initialized","service":"notification-service","timestamp":"2025-07-17T23:17:30.929Z"}
{"level":"info","message":"Notification Service running on port 3005","service":"notification-service","timestamp":"2025-07-17T23:17:30.936Z"}
{"level":"info","message":"Socket connected: jLZnRE1_Pzz19M8BAAAD","service":"notification-service","timestamp":"2025-07-17T23:17:54.134Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"jLZnRE1_Pzz19M8BAAAD","timestamp":"2025-07-17T23:17:54.157Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"jLZnRE1_Pzz19M8BAAAD","timestamp":"2025-07-17T23:17:54.347Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: R82kT7EEajyLlPrYAAAG","service":"notification-service","timestamp":"2025-07-17T23:17:54.379Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"R82kT7EEajyLlPrYAAAG","timestamp":"2025-07-17T23:17:54.387Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"R82kT7EEajyLlPrYAAAG","timestamp":"2025-07-17T23:17:59.496Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: 7_xsGEbBL7yyO3-qAAAI","service":"notification-service","timestamp":"2025-07-17T23:17:59.933Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"7_xsGEbBL7yyO3-qAAAI","timestamp":"2025-07-17T23:17:59.946Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"7_xsGEbBL7yyO3-qAAAI","timestamp":"2025-07-17T23:18:04.374Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket service initialized","service":"notification-service","timestamp":"2025-07-17T23:18:33.039Z"}
{"level":"info","message":"Notification Service running on port 3005","service":"notification-service","timestamp":"2025-07-17T23:18:33.046Z"}
{"level":"info","message":"Socket service initialized","service":"notification-service","timestamp":"2025-07-17T23:21:50.476Z"}
{"level":"info","message":"Notification Service running on port 3005","service":"notification-service","timestamp":"2025-07-17T23:21:50.483Z"}
{"level":"info","message":"Socket connected: kNbadWYCNydfQgY6AAAD","service":"notification-service","timestamp":"2025-07-18T00:20:29.033Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"kNbadWYCNydfQgY6AAAD","timestamp":"2025-07-18T00:20:29.073Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: wMR0KAu2QKf5EZJrAAAG","service":"notification-service","timestamp":"2025-07-18T00:20:29.110Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"wMR0KAu2QKf5EZJrAAAG","timestamp":"2025-07-18T00:20:29.125Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"kNbadWYCNydfQgY6AAAD","timestamp":"2025-07-18T00:20:29.199Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"wMR0KAu2QKf5EZJrAAAG","timestamp":"2025-07-18T00:20:30.231Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: DVHH1lUw0Z0TQyTvAAAJ","service":"notification-service","timestamp":"2025-07-18T00:20:31.811Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"DVHH1lUw0Z0TQyTvAAAJ","timestamp":"2025-07-18T00:20:31.823Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"DVHH1lUw0Z0TQyTvAAAJ","timestamp":"2025-07-18T00:20:31.845Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: wSfSw2PGdiy4I3WaAAAN","service":"notification-service","timestamp":"2025-07-18T00:20:38.507Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"wSfSw2PGdiy4I3WaAAAN","timestamp":"2025-07-18T00:20:38.515Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"data":{"jobId":"12fb4f33-53ce-40ad-a844-0ef39b283710","message":"Your analysis is ready!","resultId":"4a7bcc79-59c2-479a-9f2f-60bd42f24e1f","status":"completed"},"event":"analysis-complete","level":"info","message":"Notification sent to user b9016e17-3f5c-4849-874d-e5b6e9459f2b","service":"notification-service","socketCount":1,"timestamp":"2025-07-18T00:20:50.834Z"}
{"jobId":"12fb4f33-53ce-40ad-a844-0ef39b283710","level":"info","message":"Analysis complete notification processed","resultId":"4a7bcc79-59c2-479a-9f2f-60bd42f24e1f","sent":true,"service":"notification-service","timestamp":"2025-07-18T00:20:50.834Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"wSfSw2PGdiy4I3WaAAAN","timestamp":"2025-07-18T00:20:53.271Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: qryfNQmM-sI3cN51AAAQ","service":"notification-service","timestamp":"2025-07-18T00:21:00.386Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"qryfNQmM-sI3cN51AAAQ","timestamp":"2025-07-18T00:21:00.396Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"qryfNQmM-sI3cN51AAAQ","timestamp":"2025-07-18T00:29:38.650Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: 2JrKZpqcjHrj1FR2AAAT","service":"notification-service","timestamp":"2025-07-18T00:29:40.350Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"2JrKZpqcjHrj1FR2AAAT","timestamp":"2025-07-18T00:29:40.360Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"data":{"jobId":"e7589ae6-98fe-43b1-8c0b-c9c08c440463","message":"Your analysis is ready!","resultId":"acc9dbd5-7b11-4f5a-b32a-0d5eb8a684bb","status":"completed"},"event":"analysis-complete","level":"info","message":"Notification sent to user b9016e17-3f5c-4849-874d-e5b6e9459f2b","service":"notification-service","socketCount":1,"timestamp":"2025-07-18T00:29:57.851Z"}
{"jobId":"e7589ae6-98fe-43b1-8c0b-c9c08c440463","level":"info","message":"Analysis complete notification processed","resultId":"acc9dbd5-7b11-4f5a-b32a-0d5eb8a684bb","sent":true,"service":"notification-service","timestamp":"2025-07-18T00:29:57.866Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"2JrKZpqcjHrj1FR2AAAT","timestamp":"2025-07-18T00:29:59.864Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: xJw08t6txY6t27uLAAAW","service":"notification-service","timestamp":"2025-07-18T00:30:58.272Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"xJw08t6txY6t27uLAAAW","timestamp":"2025-07-18T00:30:58.281Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"xJw08t6txY6t27uLAAAW","timestamp":"2025-07-18T00:30:58.464Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: RaOLe-SPDIXBjOGPAAAZ","service":"notification-service","timestamp":"2025-07-18T00:30:58.483Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"RaOLe-SPDIXBjOGPAAAZ","timestamp":"2025-07-18T00:30:58.492Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"RaOLe-SPDIXBjOGPAAAZ","timestamp":"2025-07-18T00:31:01.225Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: 1WBHzqpSgIKOiHCnAAAc","service":"notification-service","timestamp":"2025-07-18T00:31:02.595Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"1WBHzqpSgIKOiHCnAAAc","timestamp":"2025-07-18T00:31:02.630Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"1WBHzqpSgIKOiHCnAAAc","timestamp":"2025-07-18T00:31:02.647Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: G3GgdF6niqe69IiyAAAf","service":"notification-service","timestamp":"2025-07-18T00:31:02.666Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"G3GgdF6niqe69IiyAAAf","timestamp":"2025-07-18T00:31:02.674Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"G3GgdF6niqe69IiyAAAf","timestamp":"2025-07-18T00:33:58.041Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: 4eNAMAUiFZ2vRtp6AAAi","service":"notification-service","timestamp":"2025-07-18T00:33:58.147Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"4eNAMAUiFZ2vRtp6AAAi","timestamp":"2025-07-18T00:33:58.153Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"4eNAMAUiFZ2vRtp6AAAi","timestamp":"2025-07-18T00:33:59.507Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: HQ6jS2B4as8xgvrYAAAl","service":"notification-service","timestamp":"2025-07-18T00:33:59.559Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"HQ6jS2B4as8xgvrYAAAl","timestamp":"2025-07-18T00:33:59.565Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"HQ6jS2B4as8xgvrYAAAl","timestamp":"2025-07-18T00:34:00.682Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: chryAwvQWCgF-bz2AAAo","service":"notification-service","timestamp":"2025-07-18T00:34:00.734Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"chryAwvQWCgF-bz2AAAo","timestamp":"2025-07-18T00:34:00.741Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"chryAwvQWCgF-bz2AAAo","timestamp":"2025-07-18T00:34:05.750Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: ssCM4PiuNnNtdnsFAAAr","service":"notification-service","timestamp":"2025-07-18T00:34:07.194Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"ssCM4PiuNnNtdnsFAAAr","timestamp":"2025-07-18T00:34:07.200Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"ssCM4PiuNnNtdnsFAAAr","timestamp":"2025-07-18T00:34:07.242Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: npKnlUiMMc8xZ5biAAAu","service":"notification-service","timestamp":"2025-07-18T00:34:07.261Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"npKnlUiMMc8xZ5biAAAu","timestamp":"2025-07-18T00:34:07.270Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"npKnlUiMMc8xZ5biAAAu","timestamp":"2025-07-18T00:34:07.869Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: pkCBqaXl28Y2ofR1AAAw","service":"notification-service","timestamp":"2025-07-18T00:34:14.844Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"pkCBqaXl28Y2ofR1AAAw","timestamp":"2025-07-18T00:34:14.869Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"pkCBqaXl28Y2ofR1AAAw","timestamp":"2025-07-18T00:34:16.572Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: 6JGeB04XAvwS4fFvAAAz","service":"notification-service","timestamp":"2025-07-18T00:34:16.592Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"6JGeB04XAvwS4fFvAAAz","timestamp":"2025-07-18T00:34:16.601Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"data":{"jobId":"9ab7c7f0-694b-4734-ac45-e9a574426df6","message":"Your analysis is ready!","resultId":"16a744c0-841c-4deb-9c3d-5a7e6f180c5a","status":"completed"},"event":"analysis-complete","level":"info","message":"Notification sent to user b9016e17-3f5c-4849-874d-e5b6e9459f2b","service":"notification-service","socketCount":1,"timestamp":"2025-07-18T00:34:37.733Z"}
{"jobId":"9ab7c7f0-694b-4734-ac45-e9a574426df6","level":"info","message":"Analysis complete notification processed","resultId":"16a744c0-841c-4deb-9c3d-5a7e6f180c5a","sent":true,"service":"notification-service","timestamp":"2025-07-18T00:34:37.734Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"6JGeB04XAvwS4fFvAAAz","timestamp":"2025-07-18T00:34:40.270Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: ET3fb0n3XJX8D1qjAAA2","service":"notification-service","timestamp":"2025-07-18T00:40:06.842Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"ET3fb0n3XJX8D1qjAAA2","timestamp":"2025-07-18T00:40:06.850Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"ET3fb0n3XJX8D1qjAAA2","timestamp":"2025-07-18T00:40:08.307Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: 2k-c9hM66wMuP8zEAAA5","service":"notification-service","timestamp":"2025-07-18T00:40:10.893Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"2k-c9hM66wMuP8zEAAA5","timestamp":"2025-07-18T00:40:10.900Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"data":{"jobId":"829865a1-9ebe-4b7d-9408-f4043da724cc","message":"Your analysis is ready!","resultId":"a61c731c-e94c-4abf-8e16-51d5e84c2450","status":"completed"},"event":"analysis-complete","level":"info","message":"Notification sent to user b9016e17-3f5c-4849-874d-e5b6e9459f2b","service":"notification-service","socketCount":1,"timestamp":"2025-07-18T00:40:32.653Z"}
{"jobId":"829865a1-9ebe-4b7d-9408-f4043da724cc","level":"info","message":"Analysis complete notification processed","resultId":"a61c731c-e94c-4abf-8e16-51d5e84c2450","sent":true,"service":"notification-service","timestamp":"2025-07-18T00:40:32.653Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"2k-c9hM66wMuP8zEAAA5","timestamp":"2025-07-18T00:40:34.659Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: BT4segGSrj4JQFzwAAA8","service":"notification-service","timestamp":"2025-07-18T00:41:02.090Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"BT4segGSrj4JQFzwAAA8","timestamp":"2025-07-18T00:41:02.100Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"BT4segGSrj4JQFzwAAA8","timestamp":"2025-07-18T00:41:02.198Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: o_cKUDGwTUrJaOzXAAA_","service":"notification-service","timestamp":"2025-07-18T00:41:02.221Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"o_cKUDGwTUrJaOzXAAA_","timestamp":"2025-07-18T00:41:02.233Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"o_cKUDGwTUrJaOzXAAA_","timestamp":"2025-07-18T00:41:03.514Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: EYZL60bgRRri7xi5AABC","service":"notification-service","timestamp":"2025-07-18T00:41:05.246Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"EYZL60bgRRri7xi5AABC","timestamp":"2025-07-18T00:41:05.271Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"EYZL60bgRRri7xi5AABC","timestamp":"2025-07-18T00:41:05.284Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: agfUGWN-asRp38hKAABF","service":"notification-service","timestamp":"2025-07-18T00:41:05.288Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"agfUGWN-asRp38hKAABF","timestamp":"2025-07-18T00:41:05.303Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"agfUGWN-asRp38hKAABF","timestamp":"2025-07-18T00:41:06.555Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: fCzi8gwWad23B1brAABI","service":"notification-service","timestamp":"2025-07-18T00:41:07.633Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"fCzi8gwWad23B1brAABI","timestamp":"2025-07-18T00:41:07.643Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"fCzi8gwWad23B1brAABI","timestamp":"2025-07-18T00:41:07.673Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: qmrzKzFX-VTLAaiGAABL","service":"notification-service","timestamp":"2025-07-18T00:41:07.692Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"qmrzKzFX-VTLAaiGAABL","timestamp":"2025-07-18T00:41:07.702Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"qmrzKzFX-VTLAaiGAABL","timestamp":"2025-07-18T00:41:08.092Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: PtDKPRvxDZkKMXf3AABO","service":"notification-service","timestamp":"2025-07-18T00:41:08.767Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"PtDKPRvxDZkKMXf3AABO","timestamp":"2025-07-18T00:41:08.789Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"PtDKPRvxDZkKMXf3AABO","timestamp":"2025-07-18T00:41:08.813Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: zDs1d6vORFlcMdVUAABR","service":"notification-service","timestamp":"2025-07-18T00:41:08.831Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"zDs1d6vORFlcMdVUAABR","timestamp":"2025-07-18T00:41:08.840Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"zDs1d6vORFlcMdVUAABR","timestamp":"2025-07-18T00:41:09.318Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: DPVZHnS90Ptny4ryAABV","service":"notification-service","timestamp":"2025-07-18T00:41:09.577Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"DPVZHnS90Ptny4ryAABV","timestamp":"2025-07-18T00:41:09.590Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"DPVZHnS90Ptny4ryAABV","timestamp":"2025-07-18T00:41:09.637Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: F5sAJ1nUoLmHg0eyAABY","service":"notification-service","timestamp":"2025-07-18T00:41:09.655Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"F5sAJ1nUoLmHg0eyAABY","timestamp":"2025-07-18T00:41:09.662Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"F5sAJ1nUoLmHg0eyAABY","timestamp":"2025-07-18T00:42:58.641Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: kFnx68A-HvjcQvrPAABb","service":"notification-service","timestamp":"2025-07-18T00:43:00.136Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"kFnx68A-HvjcQvrPAABb","timestamp":"2025-07-18T00:43:00.142Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"kFnx68A-HvjcQvrPAABb","timestamp":"2025-07-18T00:43:00.229Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: cV9ZEvS3ux2dtTlJAABe","service":"notification-service","timestamp":"2025-07-18T00:43:00.248Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"cV9ZEvS3ux2dtTlJAABe","timestamp":"2025-07-18T00:43:00.257Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"cV9ZEvS3ux2dtTlJAABe","timestamp":"2025-07-18T00:43:01.920Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: RtKk7ys-9OSznn01AABh","service":"notification-service","timestamp":"2025-07-18T00:43:03.766Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"RtKk7ys-9OSznn01AABh","timestamp":"2025-07-18T00:43:03.780Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"RtKk7ys-9OSznn01AABh","timestamp":"2025-07-18T00:43:03.817Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: VTAm7NzbvTvtcpkNAABk","service":"notification-service","timestamp":"2025-07-18T00:43:03.837Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"VTAm7NzbvTvtcpkNAABk","timestamp":"2025-07-18T00:43:03.844Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"VTAm7NzbvTvtcpkNAABk","timestamp":"2025-07-18T01:01:51.492Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: EzMu04_oRfICSOU4AABp","service":"notification-service","timestamp":"2025-07-18T01:01:54.645Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"EzMu04_oRfICSOU4AABp","timestamp":"2025-07-18T01:01:54.663Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"EzMu04_oRfICSOU4AABp","timestamp":"2025-07-18T01:01:57.176Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: iWhnuwHchr1KeeJXAABv","service":"notification-service","timestamp":"2025-07-18T01:01:57.476Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"iWhnuwHchr1KeeJXAABv","timestamp":"2025-07-18T01:01:57.492Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket service initialized","service":"notification-service","timestamp":"2025-07-18T01:02:31.589Z"}
{"level":"info","message":"Notification Service running on port 3005","service":"notification-service","timestamp":"2025-07-18T01:02:31.600Z"}
{"level":"info","message":"Socket connected: L1MjdwGWPi12QpUQAAAB","service":"notification-service","timestamp":"2025-07-18T01:02:32.297Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"L1MjdwGWPi12QpUQAAAB","timestamp":"2025-07-18T01:02:32.316Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket service initialized","service":"notification-service","timestamp":"2025-07-18T01:10:37.509Z"}
{"level":"info","message":"Notification Service running on port 3005","service":"notification-service","timestamp":"2025-07-18T01:10:37.516Z"}
{"level":"info","message":"Socket service initialized","service":"notification-service","timestamp":"2025-07-18T01:21:02.367Z"}
{"level":"info","message":"Notification Service running on port 3005","service":"notification-service","timestamp":"2025-07-18T01:21:02.374Z"}
{"level":"info","message":"Socket connected: 5qCRgCMKDUH9fxhJAAAK","service":"notification-service","timestamp":"2025-07-18T01:21:42.660Z"}
{"level":"info","message":"Socket connected: z1E9uBiJxbZuQfRNAAAL","service":"notification-service","timestamp":"2025-07-18T01:21:42.666Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"5qCRgCMKDUH9fxhJAAAK","timestamp":"2025-07-18T01:21:42.681Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"z1E9uBiJxbZuQfRNAAAL","timestamp":"2025-07-18T01:21:42.684Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"5qCRgCMKDUH9fxhJAAAK","timestamp":"2025-07-18T01:21:46.309Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"z1E9uBiJxbZuQfRNAAAL","timestamp":"2025-07-18T01:21:46.843Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: y1xkE9K8cuukRGhHAAAO","service":"notification-service","timestamp":"2025-07-18T01:21:48.968Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"y1xkE9K8cuukRGhHAAAO","timestamp":"2025-07-18T01:21:48.978Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"data":{"jobId":"3805c462-b97e-4898-9e1a-b2c1ddd71943","message":"Your analysis is ready!","resultId":"05c4128e-f48a-4524-b2e8-7acc271479ba","status":"completed"},"event":"analysis-complete","level":"info","message":"Notification sent to user b9016e17-3f5c-4849-874d-e5b6e9459f2b","service":"notification-service","socketCount":1,"timestamp":"2025-07-18T01:22:07.611Z"}
{"jobId":"3805c462-b97e-4898-9e1a-b2c1ddd71943","level":"info","message":"Analysis complete notification processed","resultId":"05c4128e-f48a-4524-b2e8-7acc271479ba","sent":true,"service":"notification-service","timestamp":"2025-07-18T01:22:07.612Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"y1xkE9K8cuukRGhHAAAO","timestamp":"2025-07-18T01:22:07.618Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: 2mhwWWDQdck_h_BhAAAT","service":"notification-service","timestamp":"2025-07-18T01:22:10.184Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"2mhwWWDQdck_h_BhAAAT","timestamp":"2025-07-18T01:22:10.190Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"2mhwWWDQdck_h_BhAAAT","timestamp":"2025-07-18T01:22:11.666Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: t3Y3cLprA5tM3kmCAAAW","service":"notification-service","timestamp":"2025-07-18T01:22:13.064Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"t3Y3cLprA5tM3kmCAAAW","timestamp":"2025-07-18T01:22:13.070Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"data":{"jobId":"43cfe473-b3a6-409f-acdb-7d19fceda93d","message":"Your analysis is ready!","resultId":"14159230-5f61-4cad-a6ac-9bed6ca8a55f","status":"completed"},"event":"analysis-complete","level":"info","message":"Notification sent to user b9016e17-3f5c-4849-874d-e5b6e9459f2b","service":"notification-service","socketCount":1,"timestamp":"2025-07-18T01:22:33.555Z"}
{"jobId":"43cfe473-b3a6-409f-acdb-7d19fceda93d","level":"info","message":"Analysis complete notification processed","resultId":"14159230-5f61-4cad-a6ac-9bed6ca8a55f","sent":true,"service":"notification-service","timestamp":"2025-07-18T01:22:33.555Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"t3Y3cLprA5tM3kmCAAAW","timestamp":"2025-07-18T01:22:33.564Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: qsaUpTDQJeMIwd-YAAAb","service":"notification-service","timestamp":"2025-07-18T01:22:36.324Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"qsaUpTDQJeMIwd-YAAAb","timestamp":"2025-07-18T01:22:36.329Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"qsaUpTDQJeMIwd-YAAAb","timestamp":"2025-07-18T01:23:01.666Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: XvmubLvXhGXyR7JIAAAe","service":"notification-service","timestamp":"2025-07-18T01:23:31.063Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"XvmubLvXhGXyR7JIAAAe","timestamp":"2025-07-18T01:23:31.071Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"data":{"jobId":"10008198-a659-4715-aeb0-ed3d194d3658","message":"Your analysis is ready!","resultId":"b61d95f6-847e-4ad4-bebb-c60413decdba","status":"completed"},"event":"analysis-complete","level":"info","message":"Notification sent to user b9016e17-3f5c-4849-874d-e5b6e9459f2b","service":"notification-service","socketCount":1,"timestamp":"2025-07-18T01:23:51.654Z"}
{"jobId":"10008198-a659-4715-aeb0-ed3d194d3658","level":"info","message":"Analysis complete notification processed","resultId":"b61d95f6-847e-4ad4-bebb-c60413decdba","sent":true,"service":"notification-service","timestamp":"2025-07-18T01:23:51.654Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"XvmubLvXhGXyR7JIAAAe","timestamp":"2025-07-18T01:23:51.659Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: dt80Ym5a0QMpSifpAAAj","service":"notification-service","timestamp":"2025-07-18T01:23:57.059Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"dt80Ym5a0QMpSifpAAAj","timestamp":"2025-07-18T01:23:57.068Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"dt80Ym5a0QMpSifpAAAj","timestamp":"2025-07-18T01:23:59.168Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: vGIJQQTORl4tkaroAAAm","service":"notification-service","timestamp":"2025-07-18T01:24:00.484Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"vGIJQQTORl4tkaroAAAm","timestamp":"2025-07-18T01:24:00.494Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"data":{"jobId":"79ad9488-b8ff-4936-b107-6820f5652537","message":"Your analysis is ready!","resultId":"a3afe2c0-cade-4733-aba2-ed537b0821e5","status":"completed"},"event":"analysis-complete","level":"info","message":"Notification sent to user b9016e17-3f5c-4849-874d-e5b6e9459f2b","service":"notification-service","socketCount":1,"timestamp":"2025-07-18T01:24:18.254Z"}
{"jobId":"79ad9488-b8ff-4936-b107-6820f5652537","level":"info","message":"Analysis complete notification processed","resultId":"a3afe2c0-cade-4733-aba2-ed537b0821e5","sent":true,"service":"notification-service","timestamp":"2025-07-18T01:24:18.255Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"vGIJQQTORl4tkaroAAAm","timestamp":"2025-07-18T01:24:18.259Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: wUKTOsE05mn1nZt7AAAr","service":"notification-service","timestamp":"2025-07-18T01:24:23.716Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"wUKTOsE05mn1nZt7AAAr","timestamp":"2025-07-18T01:24:23.725Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"wUKTOsE05mn1nZt7AAAr","timestamp":"2025-07-18T01:24:24.868Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: D1fj8RJYl3HfaZ1jAAAu","service":"notification-service","timestamp":"2025-07-18T01:24:25.373Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"D1fj8RJYl3HfaZ1jAAAu","timestamp":"2025-07-18T01:24:25.378Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: vsHHZ6LfcNTSXRLDAAAw","service":"notification-service","timestamp":"2025-07-18T01:32:33.384Z"}
{"level":"info","message":"Unauthenticated socket disconnected: vsHHZ6LfcNTSXRLDAAAw","service":"notification-service","timestamp":"2025-07-18T01:32:33.430Z"}
{"level":"info","message":"Socket connected: L7WKrHmDN_SdKjrHAAA1","service":"notification-service","timestamp":"2025-07-18T01:32:33.443Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"L7WKrHmDN_SdKjrHAAA1","timestamp":"2025-07-18T01:32:33.456Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"L7WKrHmDN_SdKjrHAAA1","timestamp":"2025-07-18T01:32:36.449Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"warn","message":"Socket vsHHZ6LfcNTSXRLDAAAw not authenticated within timeout","service":"notification-service","timestamp":"2025-07-18T01:32:43.388Z"}
{"data":{"jobId":"81aa73ea-fc32-43f0-a493-2ba665247d2a","message":"Your analysis is ready!","resultId":"b40813fe-fd78-4b5a-b803-5bf414412b5f","status":"completed"},"event":"analysis-complete","level":"info","message":"Notification sent to user b9016e17-3f5c-4849-874d-e5b6e9459f2b","service":"notification-service","socketCount":1,"timestamp":"2025-07-18T01:32:58.537Z"}
{"jobId":"81aa73ea-fc32-43f0-a493-2ba665247d2a","level":"info","message":"Analysis complete notification processed","resultId":"b40813fe-fd78-4b5a-b803-5bf414412b5f","sent":true,"service":"notification-service","timestamp":"2025-07-18T01:32:58.537Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"D1fj8RJYl3HfaZ1jAAAu","timestamp":"2025-07-18T01:38:32.442Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: IeFqa5wtgSqQ41xJAAA4","service":"notification-service","timestamp":"2025-07-18T01:38:34.431Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"IeFqa5wtgSqQ41xJAAA4","timestamp":"2025-07-18T01:38:34.442Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"IeFqa5wtgSqQ41xJAAA4","timestamp":"2025-07-18T01:38:54.746Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: zjuB1TKwt3XOpfNOAAA9","service":"notification-service","timestamp":"2025-07-18T01:38:55.155Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"zjuB1TKwt3XOpfNOAAA9","timestamp":"2025-07-18T01:38:55.163Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"data":{"jobId":"975f0cf9-b2b0-4ed6-a3d3-4fd660546d64","message":"Your analysis is ready!","resultId":"9ea5f7ef-2766-4e79-ad1f-c7753aca3622","status":"completed"},"event":"analysis-complete","level":"info","message":"Notification sent to user b9016e17-3f5c-4849-874d-e5b6e9459f2b","service":"notification-service","socketCount":1,"timestamp":"2025-07-18T01:38:56.107Z"}
{"jobId":"975f0cf9-b2b0-4ed6-a3d3-4fd660546d64","level":"info","message":"Analysis complete notification processed","resultId":"9ea5f7ef-2766-4e79-ad1f-c7753aca3622","sent":true,"service":"notification-service","timestamp":"2025-07-18T01:38:56.107Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"zjuB1TKwt3XOpfNOAAA9","timestamp":"2025-07-18T01:38:57.355Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: eFdHeZkIx7KqKuyzAABC","service":"notification-service","timestamp":"2025-07-18T01:38:58.705Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"eFdHeZkIx7KqKuyzAABC","timestamp":"2025-07-18T01:38:58.727Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"eFdHeZkIx7KqKuyzAABC","timestamp":"2025-07-18T01:39:05.960Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: g9sIxHVvVi3td2T9AABH","service":"notification-service","timestamp":"2025-07-18T01:39:07.314Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"g9sIxHVvVi3td2T9AABH","timestamp":"2025-07-18T01:39:07.324Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"g9sIxHVvVi3td2T9AABH","timestamp":"2025-07-18T01:39:08.256Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: w38FoEe6dkq9jairAABM","service":"notification-service","timestamp":"2025-07-18T01:40:10.628Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"w38FoEe6dkq9jairAABM","timestamp":"2025-07-18T01:40:10.636Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"w38FoEe6dkq9jairAABM","timestamp":"2025-07-18T01:40:13.302Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: XUAliaSpbE9_TBmSAABS","service":"notification-service","timestamp":"2025-07-18T01:40:13.617Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"XUAliaSpbE9_TBmSAABS","timestamp":"2025-07-18T01:40:13.635Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"XUAliaSpbE9_TBmSAABS","timestamp":"2025-07-18T01:40:32.933Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: -w0oORphqzy-B9JuAABY","service":"notification-service","timestamp":"2025-07-18T01:40:34.494Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"-w0oORphqzy-B9JuAABY","timestamp":"2025-07-18T01:40:34.502Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"-w0oORphqzy-B9JuAABY","timestamp":"2025-07-18T01:46:07.292Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: tROrvYm5FbGa4VeJAABe","service":"notification-service","timestamp":"2025-07-18T01:46:09.428Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"tROrvYm5FbGa4VeJAABe","timestamp":"2025-07-18T01:46:09.437Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"tROrvYm5FbGa4VeJAABe","timestamp":"2025-07-18T01:46:14.838Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: 6A3vegCzu93EfxJ_AABk","service":"notification-service","timestamp":"2025-07-18T01:46:15.106Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"6A3vegCzu93EfxJ_AABk","timestamp":"2025-07-18T01:46:15.125Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket service initialized","service":"notification-service","timestamp":"2025-07-18T01:46:35.698Z"}
{"level":"info","message":"Notification Service running on port 3005","service":"notification-service","timestamp":"2025-07-18T01:46:35.704Z"}
{"level":"info","message":"Socket connected: wmfv6XsdezfV9pn_AAAB","service":"notification-service","timestamp":"2025-07-18T01:46:38.283Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"wmfv6XsdezfV9pn_AAAB","timestamp":"2025-07-18T01:46:38.296Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"wmfv6XsdezfV9pn_AAAB","timestamp":"2025-07-18T01:46:40.992Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: f26T3xxW9IMYkAhcAAAH","service":"notification-service","timestamp":"2025-07-18T01:46:42.643Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"f26T3xxW9IMYkAhcAAAH","timestamp":"2025-07-18T01:46:42.650Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"f26T3xxW9IMYkAhcAAAH","timestamp":"2025-07-18T01:52:54.196Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: -CscZCUs7fn8oJSxAAAK","service":"notification-service","timestamp":"2025-07-18T01:52:55.888Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"-CscZCUs7fn8oJSxAAAK","timestamp":"2025-07-18T01:52:55.902Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"data":{"jobId":"8f78d6bd-c19b-4231-b969-48267edffd02","message":"Your analysis is ready!","resultId":"22835314-f95d-48c7-8169-8a1aa8d0e0ef","status":"completed"},"event":"analysis-complete","level":"info","message":"Notification sent to user b9016e17-3f5c-4849-874d-e5b6e9459f2b","service":"notification-service","socketCount":1,"timestamp":"2025-07-18T01:53:14.653Z"}
{"jobId":"8f78d6bd-c19b-4231-b969-48267edffd02","level":"info","message":"Analysis complete notification processed","resultId":"22835314-f95d-48c7-8169-8a1aa8d0e0ef","sent":true,"service":"notification-service","timestamp":"2025-07-18T01:53:14.654Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"-CscZCUs7fn8oJSxAAAK","timestamp":"2025-07-18T01:53:18.272Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: L842Wz1EtFexHjqQAAAP","service":"notification-service","timestamp":"2025-07-18T01:55:27.237Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"L842Wz1EtFexHjqQAAAP","timestamp":"2025-07-18T01:55:27.249Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"L842Wz1EtFexHjqQAAAP","timestamp":"2025-07-18T01:55:28.608Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: 2bOH3nt0CNy0weZbAAAS","service":"notification-service","timestamp":"2025-07-18T01:55:30.617Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"2bOH3nt0CNy0weZbAAAS","timestamp":"2025-07-18T01:55:30.629Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"data":{"jobId":"a628abdf-a9ea-4cb4-bbbd-d39b70b56b18","message":"Your analysis is ready!","resultId":"6062c766-fafa-4802-a352-159818be9600","status":"completed"},"event":"analysis-complete","level":"info","message":"Notification sent to user b9016e17-3f5c-4849-874d-e5b6e9459f2b","service":"notification-service","socketCount":1,"timestamp":"2025-07-18T01:55:50.321Z"}
{"jobId":"a628abdf-a9ea-4cb4-bbbd-d39b70b56b18","level":"info","message":"Analysis complete notification processed","resultId":"6062c766-fafa-4802-a352-159818be9600","sent":true,"service":"notification-service","timestamp":"2025-07-18T01:55:50.322Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"2bOH3nt0CNy0weZbAAAS","timestamp":"2025-07-18T01:55:53.327Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: LxwH3KARplRji9bSAAAX","service":"notification-service","timestamp":"2025-07-18T01:55:56.920Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"LxwH3KARplRji9bSAAAX","timestamp":"2025-07-18T01:55:56.933Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"LxwH3KARplRji9bSAAAX","timestamp":"2025-07-18T01:57:46.058Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: sBYIL3293v2-ZQewAAAd","service":"notification-service","timestamp":"2025-07-18T01:57:50.338Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"sBYIL3293v2-ZQewAAAd","timestamp":"2025-07-18T01:57:50.345Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"sBYIL3293v2-ZQewAAAd","timestamp":"2025-07-18T01:57:53.685Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket connected: 4btyfAvIST0DEgTFAAAj","service":"notification-service","timestamp":"2025-07-18T01:57:54.249Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"4btyfAvIST0DEgTFAAAj","timestamp":"2025-07-18T01:57:54.263Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"4btyfAvIST0DEgTFAAAj","timestamp":"2025-07-18T01:58:15.611Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket connected: TDAkZLq7IE-Nv0IpAAAo","service":"notification-service","timestamp":"2025-07-18T01:58:21.518Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"TDAkZLq7IE-Nv0IpAAAo","timestamp":"2025-07-18T01:58:21.531Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"TDAkZLq7IE-Nv0IpAAAo","timestamp":"2025-07-18T01:58:26.561Z","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"level":"info","message":"Socket connected: 5-cfW8SwMIzxzc0MAAAu","service":"notification-service","timestamp":"2025-07-18T01:58:29.246Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"5-cfW8SwMIzxzc0MAAAu","timestamp":"2025-07-18T01:58:29.253Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"5-cfW8SwMIzxzc0MAAAu","timestamp":"2025-07-18T01:58:31.326Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: m5R_CbHzj_7vFbkKAAAx","service":"notification-service","timestamp":"2025-07-18T01:58:33.201Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"m5R_CbHzj_7vFbkKAAAx","timestamp":"2025-07-18T01:58:33.207Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"data":{"jobId":"d8560735-557d-4c8f-97cc-c6ebe441a281","message":"Your analysis is ready!","resultId":"ca6a65a1-334d-4406-bf85-5b4aff0a6c73","status":"completed"},"event":"analysis-complete","level":"info","message":"Notification sent to user b9016e17-3f5c-4849-874d-e5b6e9459f2b","service":"notification-service","socketCount":1,"timestamp":"2025-07-18T01:58:52.682Z"}
{"jobId":"d8560735-557d-4c8f-97cc-c6ebe441a281","level":"info","message":"Analysis complete notification processed","resultId":"ca6a65a1-334d-4406-bf85-5b4aff0a6c73","sent":true,"service":"notification-service","timestamp":"2025-07-18T01:58:52.682Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"m5R_CbHzj_7vFbkKAAAx","timestamp":"2025-07-18T01:58:54.905Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: A5rPqk8m6MIHM66GAAA2","service":"notification-service","timestamp":"2025-07-18T01:58:55.670Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"A5rPqk8m6MIHM66GAAA2","timestamp":"2025-07-18T01:58:55.682Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"A5rPqk8m6MIHM66GAAA2","timestamp":"2025-07-18T01:58:55.703Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: 605iWbquWe_YCVeXAAA7","service":"notification-service","timestamp":"2025-07-18T01:58:57.422Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"605iWbquWe_YCVeXAAA7","timestamp":"2025-07-18T01:58:57.435Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"605iWbquWe_YCVeXAAA7","timestamp":"2025-07-18T02:00:12.506Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: 8tYgTJWaCigiXGE4AABB","service":"notification-service","timestamp":"2025-07-18T02:00:14.152Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"8tYgTJWaCigiXGE4AABB","timestamp":"2025-07-18T02:00:14.162Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"8tYgTJWaCigiXGE4AABB","timestamp":"2025-07-18T02:00:39.036Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: bfYJo0EMPcSEsqXMAABH","service":"notification-service","timestamp":"2025-07-18T02:00:39.576Z"}
{"level":"info","message":"Socket authenticated <NAME_EMAIL>","service":"notification-service","socketId":"bfYJo0EMPcSEsqXMAABH","timestamp":"2025-07-18T02:00:39.614Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket disconnected <NAME_EMAIL>","service":"notification-service","socketId":"bfYJo0EMPcSEsqXMAABH","timestamp":"2025-07-18T02:03:01.428Z","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"level":"info","message":"Socket connected: i14PIc6DBslIHLZQAABJ","service":"notification-service","timestamp":"2025-07-18T02:08:30.368Z"}
{"level":"warn","message":"Socket authentication failed: invalid signature","service":"notification-service","socketId":"i14PIc6DBslIHLZQAABJ","timestamp":"2025-07-18T02:08:30.370Z"}
{"level":"info","message":"Unauthenticated socket disconnected: i14PIc6DBslIHLZQAABJ","service":"notification-service","timestamp":"2025-07-18T02:08:30.371Z"}
{"level":"warn","message":"Socket i14PIc6DBslIHLZQAABJ not authenticated within timeout","service":"notification-service","timestamp":"2025-07-18T02:08:40.374Z"}
{"level":"info","message":"Socket service initialized","service":"notification-service","timestamp":"2025-07-18T03:09:53.341Z"}
{"level":"info","message":"Notification Service running on port 3005","service":"notification-service","timestamp":"2025-07-18T03:09:53.347Z"}
{"level":"info","message":"Socket service initialized","service":"notification-service","timestamp":"2025-07-18T03:12:20.483Z"}
{"level":"info","message":"Notification Service running on port 3005","service":"notification-service","timestamp":"2025-07-18T03:12:20.492Z"}
{"level":"info","message":"Socket service initialized","service":"notification-service","timestamp":"2025-07-18T03:15:33.136Z"}
{"level":"info","message":"Notification Service running on port 3005","service":"notification-service","timestamp":"2025-07-18T03:15:33.143Z"}
{"level":"info","message":"Socket service initialized","service":"notification-service","timestamp":"2025-07-18T03:18:00.346Z"}
{"level":"info","message":"Notification Service running on port 3005","service":"notification-service","timestamp":"2025-07-18T03:18:00.353Z"}
{"level":"info","message":"Socket service initialized","service":"notification-service","timestamp":"2025-07-18T03:18:54.115Z"}
{"level":"info","message":"Notification Service running on port 3005","service":"notification-service","timestamp":"2025-07-18T03:18:54.122Z"}
{"level":"info","message":"Socket service initialized","service":"notification-service","timestamp":"2025-07-18T03:25:04.049Z"}
{"level":"info","message":"Notification Service running on port 3005","service":"notification-service","timestamp":"2025-07-18T03:25:04.054Z"}
{"level":"info","message":"Socket service initialized","service":"notification-service","timestamp":"2025-07-18T05:41:01.794Z"}
{"level":"info","message":"Notification Service running on port 3005","service":"notification-service","timestamp":"2025-07-18T05:41:01.800Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"23e58e05-b38d-4cb5-bbc1-4ce7aba19abb","status":"failed","userId":"da6e7756-6da7-4762-9478-15989c524ba5"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:46:49.126Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"23e58e05-b38d-4cb5-bbc1-4ce7aba19abb","status":"failed","userId":"da6e7756-6da7-4762-9478-15989c524ba5"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:46:56.653Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"6dfd5389-9782-453c-870f-7e842822cd25","status":"failed","userId":"842827f5-e6a0-464c-adee-d849b9ffb009"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:46:58.141Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"6672a02d-2629-46dc-a4c9-07c6c1842be3","status":"failed","userId":"d9acc7c4-6671-4254-8f39-6034e3924c8b"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:46:58.384Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"953e4079-db1e-4961-83f7-92ca8ff520eb","status":"failed","userId":"15902e84-9592-4b67-b3c2-1ec79e1dd54d"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:46:58.402Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"f57adc7d-95c3-4cea-96af-de83780d0a20","status":"failed","userId":"bb269993-1022-43b2-8926-4fbf4043ba0c"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:46:58.619Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"a608b30c-a89d-41d7-b5b0-9b89efea16d0","status":"failed","userId":"6c14d02a-a968-495c-889c-d9a3398c74a0"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:46:58.712Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"b7a01cf7-90b3-487f-a046-3ba5f294263a","status":"failed","userId":"ff453c60-d4cc-4a3b-895b-58aea0d76994"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:46:58.861Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"706cb084-15da-40a5-827f-cda6dd355a7f","status":"failed","userId":"da6e7756-6da7-4762-9478-15989c524ba5"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:46:59.023Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"ffcd9e7f-9de6-4d32-99b4-56b82711b047","status":"failed","userId":"078de71f-122d-41ec-be55-b4b93c20d6b8"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:46:59.334Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"0ce3850f-28e8-43e8-98a3-8fbf07eb4920","status":"failed","userId":"b41051d1-550d-4d43-a899-ff6731e904ef"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:46:59.381Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"c736afcc-caf2-430a-9d81-a12a72b367a6","status":"failed","userId":"a95c638e-a5c5-4d5a-bac3-440783832e88"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:46:59.473Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"a608b30c-a89d-41d7-b5b0-9b89efea16d0","status":"failed","userId":"6c14d02a-a968-495c-889c-d9a3398c74a0"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:04.793Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"b7a01cf7-90b3-487f-a046-3ba5f294263a","status":"failed","userId":"ff453c60-d4cc-4a3b-895b-58aea0d76994"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:05.278Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"706cb084-15da-40a5-827f-cda6dd355a7f","status":"failed","userId":"da6e7756-6da7-4762-9478-15989c524ba5"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:05.324Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"6672a02d-2629-46dc-a4c9-07c6c1842be3","status":"failed","userId":"d9acc7c4-6671-4254-8f39-6034e3924c8b"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:05.491Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"f57adc7d-95c3-4cea-96af-de83780d0a20","status":"failed","userId":"bb269993-1022-43b2-8926-4fbf4043ba0c"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:05.761Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"0ce3850f-28e8-43e8-98a3-8fbf07eb4920","status":"failed","userId":"b41051d1-550d-4d43-a899-ff6731e904ef"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:05.768Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"6dfd5389-9782-453c-870f-7e842822cd25","status":"failed","userId":"842827f5-e6a0-464c-adee-d849b9ffb009"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:06.004Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"c736afcc-caf2-430a-9d81-a12a72b367a6","status":"failed","userId":"a95c638e-a5c5-4d5a-bac3-440783832e88"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:06.035Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"ffcd9e7f-9de6-4d32-99b4-56b82711b047","status":"failed","userId":"078de71f-122d-41ec-be55-b4b93c20d6b8"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:06.192Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"953e4079-db1e-4961-83f7-92ca8ff520eb","status":"failed","userId":"15902e84-9592-4b67-b3c2-1ec79e1dd54d"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:06.406Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"16d08e48-839e-47db-9403-1768e0cb5674","status":"failed","userId":"a95c638e-a5c5-4d5a-bac3-440783832e88"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:06.919Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"99f2873d-6e84-4c6d-9c1b-f26342b7741a","status":"failed","userId":"842827f5-e6a0-464c-adee-d849b9ffb009"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:07.071Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"8958ea65-5ea0-4ba9-b087-0ad18ba0552e","status":"failed","userId":"ff453c60-d4cc-4a3b-895b-58aea0d76994"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:07.085Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"95728b57-312a-4fa3-854f-6542a1aa88ee","status":"failed","userId":"da6e7756-6da7-4762-9478-15989c524ba5"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:07.318Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"0f5e5721-73b0-4a64-94a6-d372d1ed319e","status":"failed","userId":"078de71f-122d-41ec-be55-b4b93c20d6b8"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:07.672Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"f8c49fed-7ce2-40ba-ac23-e07e74119fb8","status":"failed","userId":"b41051d1-550d-4d43-a899-ff6731e904ef"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:07.842Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"4b547a7a-0847-421e-acb3-fee3f0987368","status":"failed","userId":"d9acc7c4-6671-4254-8f39-6034e3924c8b"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:08.017Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"c1e1db62-a1f5-46a0-9f11-ea780ddcb701","status":"failed","userId":"6c14d02a-a968-495c-889c-d9a3398c74a0"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:08.291Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"3be7010f-69f1-498e-9937-41543fd9b95b","status":"failed","userId":"15902e84-9592-4b67-b3c2-1ec79e1dd54d"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:08.518Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"f1cb8393-57b4-45a9-a177-ca1e6cc1c0df","status":"failed","userId":"bb269993-1022-43b2-8926-4fbf4043ba0c"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:08.564Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"23e58e05-b38d-4cb5-bbc1-4ce7aba19abb","status":"failed","userId":"da6e7756-6da7-4762-9478-15989c524ba5"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:09.089Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"8958ea65-5ea0-4ba9-b087-0ad18ba0552e","status":"failed","userId":"ff453c60-d4cc-4a3b-895b-58aea0d76994"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:13.120Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"99f2873d-6e84-4c6d-9c1b-f26342b7741a","status":"failed","userId":"842827f5-e6a0-464c-adee-d849b9ffb009"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:13.415Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"16d08e48-839e-47db-9403-1768e0cb5674","status":"failed","userId":"a95c638e-a5c5-4d5a-bac3-440783832e88"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:14.651Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"f8c49fed-7ce2-40ba-ac23-e07e74119fb8","status":"failed","userId":"b41051d1-550d-4d43-a899-ff6731e904ef"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:14.683Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"3be7010f-69f1-498e-9937-41543fd9b95b","status":"failed","userId":"15902e84-9592-4b67-b3c2-1ec79e1dd54d"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:14.930Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"c1e1db62-a1f5-46a0-9f11-ea780ddcb701","status":"failed","userId":"6c14d02a-a968-495c-889c-d9a3398c74a0"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:14.943Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"95728b57-312a-4fa3-854f-6542a1aa88ee","status":"failed","userId":"da6e7756-6da7-4762-9478-15989c524ba5"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:14.992Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"0f5e5721-73b0-4a64-94a6-d372d1ed319e","status":"failed","userId":"078de71f-122d-41ec-be55-b4b93c20d6b8"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:15.129Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"4b547a7a-0847-421e-acb3-fee3f0987368","status":"failed","userId":"d9acc7c4-6671-4254-8f39-6034e3924c8b"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:15.466Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"f1cb8393-57b4-45a9-a177-ca1e6cc1c0df","status":"failed","userId":"bb269993-1022-43b2-8926-4fbf4043ba0c"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:15.697Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"6672a02d-2629-46dc-a4c9-07c6c1842be3","status":"failed","userId":"d9acc7c4-6671-4254-8f39-6034e3924c8b"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:16.512Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"a608b30c-a89d-41d7-b5b0-9b89efea16d0","status":"failed","userId":"6c14d02a-a968-495c-889c-d9a3398c74a0"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:16.852Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"706cb084-15da-40a5-827f-cda6dd355a7f","status":"failed","userId":"da6e7756-6da7-4762-9478-15989c524ba5"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:16.913Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"ffcd9e7f-9de6-4d32-99b4-56b82711b047","status":"failed","userId":"078de71f-122d-41ec-be55-b4b93c20d6b8"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:17.613Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"f57adc7d-95c3-4cea-96af-de83780d0a20","status":"failed","userId":"bb269993-1022-43b2-8926-4fbf4043ba0c"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:17.907Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"c736afcc-caf2-430a-9d81-a12a72b367a6","status":"failed","userId":"a95c638e-a5c5-4d5a-bac3-440783832e88"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:17.966Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"b7a01cf7-90b3-487f-a046-3ba5f294263a","status":"failed","userId":"ff453c60-d4cc-4a3b-895b-58aea0d76994"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:17.998Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"0ce3850f-28e8-43e8-98a3-8fbf07eb4920","status":"failed","userId":"b41051d1-550d-4d43-a899-ff6731e904ef"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:18.291Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"6dfd5389-9782-453c-870f-7e842822cd25","status":"failed","userId":"842827f5-e6a0-464c-adee-d849b9ffb009"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:18.644Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"953e4079-db1e-4961-83f7-92ca8ff520eb","status":"failed","userId":"15902e84-9592-4b67-b3c2-1ec79e1dd54d"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:19.417Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"99f2873d-6e84-4c6d-9c1b-f26342b7741a","status":"failed","userId":"842827f5-e6a0-464c-adee-d849b9ffb009"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:25.149Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"8958ea65-5ea0-4ba9-b087-0ad18ba0552e","status":"failed","userId":"ff453c60-d4cc-4a3b-895b-58aea0d76994"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:25.749Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"f8c49fed-7ce2-40ba-ac23-e07e74119fb8","status":"failed","userId":"b41051d1-550d-4d43-a899-ff6731e904ef"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:26.778Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"4b547a7a-0847-421e-acb3-fee3f0987368","status":"failed","userId":"d9acc7c4-6671-4254-8f39-6034e3924c8b"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:27.007Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"16d08e48-839e-47db-9403-1768e0cb5674","status":"failed","userId":"a95c638e-a5c5-4d5a-bac3-440783832e88"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:27.283Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"3be7010f-69f1-498e-9937-41543fd9b95b","status":"failed","userId":"15902e84-9592-4b67-b3c2-1ec79e1dd54d"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:27.314Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"c1e1db62-a1f5-46a0-9f11-ea780ddcb701","status":"failed","userId":"6c14d02a-a968-495c-889c-d9a3398c74a0"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:27.654Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"0f5e5721-73b0-4a64-94a6-d372d1ed319e","status":"failed","userId":"078de71f-122d-41ec-be55-b4b93c20d6b8"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:27.669Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"95728b57-312a-4fa3-854f-6542a1aa88ee","status":"failed","userId":"da6e7756-6da7-4762-9478-15989c524ba5"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:27.716Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"f1cb8393-57b4-45a9-a177-ca1e6cc1c0df","status":"failed","userId":"bb269993-1022-43b2-8926-4fbf4043ba0c"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:28.406Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"23e58e05-b38d-4cb5-bbc1-4ce7aba19abb","status":"failed","userId":"da6e7756-6da7-4762-9478-15989c524ba5"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:31.248Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"a608b30c-a89d-41d7-b5b0-9b89efea16d0","status":"failed","userId":"6c14d02a-a968-495c-889c-d9a3398c74a0"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:38.101Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"6672a02d-2629-46dc-a4c9-07c6c1842be3","status":"failed","userId":"d9acc7c4-6671-4254-8f39-6034e3924c8b"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:39.122Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"706cb084-15da-40a5-827f-cda6dd355a7f","status":"failed","userId":"da6e7756-6da7-4762-9478-15989c524ba5"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:39.526Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"ffcd9e7f-9de6-4d32-99b4-56b82711b047","status":"failed","userId":"078de71f-122d-41ec-be55-b4b93c20d6b8"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:39.540Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"b7a01cf7-90b3-487f-a046-3ba5f294263a","status":"failed","userId":"ff453c60-d4cc-4a3b-895b-58aea0d76994"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:39.881Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"f57adc7d-95c3-4cea-96af-de83780d0a20","status":"failed","userId":"bb269993-1022-43b2-8926-4fbf4043ba0c"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:40.512Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"0ce3850f-28e8-43e8-98a3-8fbf07eb4920","status":"failed","userId":"b41051d1-550d-4d43-a899-ff6731e904ef"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:40.757Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"c736afcc-caf2-430a-9d81-a12a72b367a6","status":"failed","userId":"a95c638e-a5c5-4d5a-bac3-440783832e88"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:40.973Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"6dfd5389-9782-453c-870f-7e842822cd25","status":"failed","userId":"842827f5-e6a0-464c-adee-d849b9ffb009"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:41.201Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"953e4079-db1e-4961-83f7-92ca8ff520eb","status":"failed","userId":"15902e84-9592-4b67-b3c2-1ec79e1dd54d"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:42.282Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"e6e2a0b1-f712-415b-b838-843d3cf541a8","status":"failed","userId":"3dbea5fc-4925-4aa7-bfcd-318599a9e5b1"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:46.650Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"b3ba67e6-ad9d-407f-8e6c-1e2f422ad840","status":"failed","userId":"392fd33d-ab74-4da6-a91a-02c56656a57c"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:46.681Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"3e09ab89-6060-4e6a-b3c0-d7a08e2dc63e","status":"failed","userId":"3653691f-57cf-4782-8edf-157e46f19bd8"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:46.834Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"02e87e37-d55a-4a83-902d-0389dddab2fa","status":"failed","userId":"45f1e061-9c35-4aad-802e-6d1a58fbdaf0"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:46.880Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"be02616f-a58b-481e-b2aa-894bf4ea9404","status":"failed","userId":"009e09fc-311a-4851-949e-b76859139743"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:46.957Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"6c8f18fd-0dba-4a94-8bf0-b804667cc62b","status":"failed","userId":"8f72b4c2-3265-4ba0-8f52-6a1ff9d5e497"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:47.003Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"c643f010-a909-48da-bf1f-0bb678ca8eba","status":"failed","userId":"c52e498b-c989-4e27-b783-2fe3316085a3"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:47.034Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"99f2873d-6e84-4c6d-9c1b-f26342b7741a","status":"failed","userId":"842827f5-e6a0-464c-adee-d849b9ffb009"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:47.095Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"e91643f1-a51c-4c93-892c-d6dee18f9c14","status":"failed","userId":"80b44903-76e5-4d53-9831-574ecab7f147"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:47.096Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"fafda137-2dfb-4947-bedc-5a44f3421a1f","status":"failed","userId":"12e2ba85-6226-4701-80f5-f7831eedfd48"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:47.110Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"e871fb5e-1e2a-46c1-9a2a-04081585be3a","status":"failed","userId":"7e1bb330-f26a-4a0f-a0df-b16e75e63d36"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:47.236Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"29c86e36-8f56-4b6e-864d-e5ab9d9c0eb8","status":"failed","userId":"11556c6b-f185-42ce-a3b6-369dd56a6ac5"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:47.296Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"296471d2-b7eb-4324-a17a-78d362373419","status":"failed","userId":"cb7a1876-d50d-4ac0-b263-6790e313d4f8"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:47.358Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"2c6cceb3-6d75-45b0-8fd0-bda0b708105a","status":"failed","userId":"040790f4-eeb4-46d7-84e7-768e0f8c500b"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:47.373Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"bb463fb3-2f18-45d5-a2fc-40fce7c4a8e2","status":"failed","userId":"d7d7720a-b095-4f24-8dea-4b4fae817a5a"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:47.420Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"417116ed-7c8b-4913-aa56-00549a16a439","status":"failed","userId":"6bc0b5c1-151e-4b05-b743-4762063acbee"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:47.498Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"6f2fd8e4-b671-4eb2-a448-659faa922611","status":"failed","userId":"8f153039-6f2b-411e-af79-c636d53cc3f9"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:47.499Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"16fa9e36-9332-4595-bc6e-1e0c67173dae","status":"failed","userId":"4aa44128-345e-4ebe-b3eb-39676d2596ce"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:47.638Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"ed1114a3-aebc-419f-98d1-3a9a3b37c61a","status":"failed","userId":"bb269993-1022-43b2-8926-4fbf4043ba0c"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:47.652Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"fb1116b1-a5e1-4100-8ad5-1b99d2f5626f","status":"failed","userId":"b03799da-c6da-4925-87c6-901060f1ffc3"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:47.684Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"2b28451c-1940-4deb-b6c9-fb05b72d2eba","status":"failed","userId":"c540dde8-d7e7-4b31-946b-c44073078958"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:47.715Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"4eca1c5a-2ffb-4996-9416-fd359c63a8a6","status":"failed","userId":"68abe24d-9fd9-4a34-9538-81ebed775b4c"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:47.792Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"20cc46b5-f83d-4e2b-86ef-fed571528a50","status":"failed","userId":"ed022609-ebc6-41d0-a464-2dc10ca8773e"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:47.965Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"51d2d7f8-0216-46b9-9d68-19463f60cf73","status":"failed","userId":"bc754f4a-5ac0-4053-8bba-a2a42a31a746"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:47.979Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"efb0205a-4ac9-4a67-8e64-d512cd142df7","status":"failed","userId":"f1b2443e-1fd7-40a2-b26e-001251819505"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:48.011Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"90ab426b-e3af-4374-bc4d-96c7580b6e1b","status":"failed","userId":"950a1c16-ba41-4ac6-bea8-b052c6bd32fb"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:48.042Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"7870dc08-7870-4d93-bb9a-6b419a330294","status":"failed","userId":"24a91af5-f630-4d85-8a86-1863e3c977ec"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:48.211Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"e9668692-e8f9-4212-be35-c7b70906154a","status":"failed","userId":"8cd96f02-f438-4e30-b81d-df35181b1dd3"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:48.319Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"ec60db2c-393f-4432-b87d-f246f19161ce","status":"failed","userId":"849ecb09-081f-43d5-a71a-6bca85f6a8aa"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:48.396Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"aca2d0cc-dcf2-443d-8a32-a5bb4c608213","status":"failed","userId":"b713b871-2151-4908-82c7-e25043087388"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:48.582Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"4a9d0fce-f62a-4624-a8a6-9b41700464e9","status":"failed","userId":"49862644-6bd8-4161-bbf0-e0bb0565df48"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:48.678Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"fe714dbf-bea6-4d59-8b14-eecc1bbc1411","status":"failed","userId":"fd129d2c-39d3-404c-a103-21a379463fcb"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:48.691Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"992ee829-8114-489c-9c8e-40ece18550a3","status":"failed","userId":"b4bc5f99-ad7c-4687-83c5-9e9e21aeba64"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:48.754Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"f3111154-e2e7-4949-82f1-ea5776f97cab","status":"failed","userId":"a26a6dd4-23e9-46f7-b782-15f7859d8342"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:49.125Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"d4bede51-d3bd-4269-ac73-01f49f528d08","status":"failed","userId":"27046cf8-947c-460f-bcac-21f0770d22ec"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:49.202Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"00b2690b-3f04-4af6-82e7-4bf208b6eca2","status":"failed","userId":"37571634-9fe7-47c1-b50d-a0b5c5918171"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:49.296Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"fd3e8d9b-c646-4292-8a0b-ccfe32e6e8b2","status":"failed","userId":"b29a61ec-cda2-4ce1-9e63-a655109ba149"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:49.373Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"a5eb9a84-2a5e-412c-a829-0a76b1fa8cc1","status":"failed","userId":"15060bb5-db18-49b3-931e-9222c270e677"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:49.405Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"506efc46-0044-489c-8f5c-df7f522457dc","status":"failed","userId":"cc61ca36-c057-44e6-8dee-f275f24dc1c0"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:49.527Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"0531a284-96f0-4593-b0e4-76fc83203b8e","status":"failed","userId":"c760022b-5504-4436-b0df-12e523fb08cf"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:49.543Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"f2bb542a-ada9-4639-8347-e1d269b0a60d","status":"failed","userId":"31ffaa30-7eca-4caa-a144-e4d4184797f8"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:49.604Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"205fc9f9-8a40-4c1b-9996-5c5dc71694bc","status":"failed","userId":"36a5194c-32f2-4426-b5e4-76f19f18b956"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:49.681Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"472738dc-e3de-430e-8671-cdb3a4bc1ca3","status":"failed","userId":"d6d1f30e-be74-494c-8a9b-bb501e91a0e3"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:49.727Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"b66de2d6-4bb2-4b56-b703-23195898b34a","status":"failed","userId":"68b8a7f4-00cf-4787-9396-f1983ed53648"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:49.763Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"1418fbbe-d7be-43b7-93f4-bc05f3a0406c","status":"failed","userId":"072f69fa-6cbe-400b-b796-7e3271da936d"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:49.768Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"9d85c017-5dc3-4581-a34d-d336008fd414","status":"failed","userId":"b7572a8f-d3ba-410d-9871-a3439187285c"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:49.914Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"fe7c8560-8c4e-4a4a-9f67-3947e20b605b","status":"failed","userId":"f9b12420-847f-4968-b8b9-4153fdc8aae2"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:50.037Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"2eef24e2-9b02-4174-8550-e23e5de814d3","status":"failed","userId":"2db8eac1-765e-4011-bb8d-7b92904ac01b"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:50.160Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"77f7a0dc-b005-4e9d-a5c9-fc4b5548431d","status":"failed","userId":"c50c6b3a-274f-47ad-97e4-19a762a6aed6"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:50.206Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"e4b7fd1e-9f49-44e3-82d0-6f725f14d3a1","status":"failed","userId":"f47f861d-74bb-4198-a24d-8509841f9e80"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:50.298Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"bc08ec3c-681f-44f7-8c4f-2cf747cbca54","status":"failed","userId":"8644861e-dabc-4b8b-a627-a0ae38bc44fe"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:50.437Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"8156b2af-e634-4eb4-a633-f75da0487db1","status":"failed","userId":"9b4c5ed1-e673-4ee9-b696-5be44a561be0"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:50.452Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"83d1a7c4-fb36-485f-a29a-72d47c66ee67","status":"failed","userId":"2280d0b1-52dd-4ea1-af58-caf1a0dc1ccb"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:50.521Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"531eb1c6-36d4-46c2-975e-a182060ecd75","status":"failed","userId":"727601f0-6746-4c23-b6af-d4d8f27c36b3"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:50.550Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"a5b78f8a-e8ab-4232-964c-d2f964ce877e","status":"failed","userId":"da9eee0f-2f64-4776-943c-45d0496b69e0"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:50.658Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"6a546621-9c70-43a7-acd9-45e6b47c80b1","status":"failed","userId":"ce779b7f-d252-445b-99cf-776c8d253223"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:50.685Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"6b92acb0-8333-4ead-8cdd-56982fde8dce","status":"failed","userId":"25a10f5b-94bb-4e95-b143-09ead677e663"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:50.687Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"c2fc42ee-3232-4c64-9095-46b80854611f","status":"failed","userId":"5cc55727-a792-495d-ac51-0d0e844e5d53"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:50.721Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"464191fe-682e-4b40-8340-84f10634ada9","status":"failed","userId":"44bf17ee-fb4d-4a0a-ab8c-ea5ac863032d"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:50.975Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"37b217c7-1770-47a3-9496-648091e1cf7c","status":"failed","userId":"39ede2d4-b490-4c9a-b193-f3328fbd4857"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:51.023Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"e6392f8a-a825-48b3-8bac-13387e57d264","status":"failed","userId":"8eaf3b43-8867-4573-91ec-926d03d93a07"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:51.037Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"418f211b-e163-4ba8-b13f-74d7d53c7496","status":"failed","userId":"4f114070-2569-44d2-ae17-e0e84d825a51"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:51.129Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"c6c29de9-54c8-4497-b044-25e0e32acb56","status":"failed","userId":"77a4b43c-1906-4c6e-ab4b-7cf5ca9cc4fe"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:51.269Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"419efb9c-d674-4a0f-8265-d1b013d0e667","status":"failed","userId":"3de2f9e7-75b6-429a-90a4-b4366adf0874"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:51.377Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"f7f3d621-8ff9-4ef9-a6d8-eef46124cfd3","status":"failed","userId":"ae217bd8-d2f3-4a8b-aac3-b3516790c2e2"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:51.439Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"2945c4ec-edf8-4d6a-8b36-73991e0fc36a","status":"failed","userId":"4b9c8855-018c-44b3-89ef-8f0201454847"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:51.485Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"661ce4db-7ab4-4f33-80c9-54ddf85e53fa","status":"failed","userId":"ee3c2f77-9ce0-4e25-a239-0ccdb4b80c24"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:51.610Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"cfeb55e9-0840-4885-8da9-859e9d469036","status":"failed","userId":"50520da3-8855-4d53-bac8-711fc04cfd19"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:51.779Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"65929f8e-412d-4043-9dfe-117594362aa6","status":"failed","userId":"c0a5909b-84fd-4460-8b7d-76601a8a73e2"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:51.810Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"92e49306-edb7-4129-9e75-a1eb9d3653aa","status":"failed","userId":"c48a95d5-717b-487b-b2ce-61d7e09e5b43"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:51.901Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"6a3d6e80-dd87-45df-8de0-40ae2c149148","status":"failed","userId":"9ae3d55e-01fd-4000-9698-d17c23b6e39c"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:51.916Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"757d91fc-405d-4909-93ac-4028971e7398","status":"failed","userId":"6761c652-3586-4274-8e32-44b45eb0ab1b"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:51.947Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"931a64f5-7a25-4c4f-a818-3a501738b98c","status":"failed","userId":"8c044fe4-4f97-4ace-8360-641d1fe1e52c"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:52.147Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"9d2ca6c4-9eb6-4dfb-9331-90e7af086166","status":"failed","userId":"e5084c4d-a410-4bed-aaae-8b1e791b1180"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:52.162Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"a0f8654b-b285-4d67-87bb-c95c4a743c7f","status":"failed","userId":"69ae9167-80c7-4164-8f08-14fc7e5f96b8"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:52.179Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"83e24814-87e3-46f4-8381-52b0aa24c3dc","status":"failed","userId":"7f361f12-3132-4693-8a61-f2ad8a8c6b0c"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:52.181Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"3302a620-d718-4dc6-a3b2-d4eefd934be6","status":"failed","userId":"4bcc4e0f-367a-4ecd-a233-ec3e9d48de38"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:52.210Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"f8c49fed-7ce2-40ba-ac23-e07e74119fb8","status":"failed","userId":"b41051d1-550d-4d43-a899-ff6731e904ef"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:52.271Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"cab94508-e557-47b1-9642-b4c0dba95480","status":"failed","userId":"829d69b3-2fdf-44eb-86a6-1d04b90cc581"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:52.318Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"6b09de7b-051f-4526-8aee-126bb4e8a62f","status":"failed","userId":"0d81b437-2a5b-45bb-87e3-b22638c79d2d"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:52.519Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"4b547a7a-0847-421e-acb3-fee3f0987368","status":"failed","userId":"d9acc7c4-6671-4254-8f39-6034e3924c8b"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:52.583Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"be1703cb-15ec-4d17-9327-c71353b0f823","status":"failed","userId":"778981a0-fa31-4a3a-9d23-e9cbb9c0c952"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:52.690Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"c3ac7294-fab3-40bd-b4af-a94b1a2523dc","status":"failed","userId":"63a9574c-b28b-4dc2-b0a6-0e009a92c1ae"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:52.811Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"9e3b9846-09e6-4286-beaf-147f9db0f27b","status":"failed","userId":"7dde7770-1e0e-481e-af32-84bc96240188"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:52.825Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"4a610299-e43f-4ac3-b3b2-ac92563e5560","status":"failed","userId":"1beefbcc-ded3-4675-85df-6c1468d16c02"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:52.995Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"8958ea65-5ea0-4ba9-b087-0ad18ba0552e","status":"failed","userId":"ff453c60-d4cc-4a3b-895b-58aea0d76994"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:53.026Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"f1cb8393-57b4-45a9-a177-ca1e6cc1c0df","status":"failed","userId":"bb269993-1022-43b2-8926-4fbf4043ba0c"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:53.040Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"e6e2a0b1-f712-415b-b838-843d3cf541a8","status":"failed","userId":"3dbea5fc-4925-4aa7-bfcd-318599a9e5b1"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:53.137Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"c643f010-a909-48da-bf1f-0bb678ca8eba","status":"failed","userId":"c52e498b-c989-4e27-b783-2fe3316085a3"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:53.208Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"2e6cf6b3-8b56-48d8-8ac1-cbe7e0beff55","status":"failed","userId":"62a862d9-1280-4b90-b469-db2e46dd6941"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:53.286Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"c1e1db62-a1f5-46a0-9f11-ea780ddcb701","status":"failed","userId":"6c14d02a-a968-495c-889c-d9a3398c74a0"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:53.347Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"3be7010f-69f1-498e-9937-41543fd9b95b","status":"failed","userId":"15902e84-9592-4b67-b3c2-1ec79e1dd54d"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:53.378Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"296471d2-b7eb-4324-a17a-78d362373419","status":"failed","userId":"cb7a1876-d50d-4ac0-b263-6790e313d4f8"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:53.615Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"fafda137-2dfb-4947-bedc-5a44f3421a1f","status":"failed","userId":"12e2ba85-6226-4701-80f5-f7831eedfd48"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:53.667Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"16d08e48-839e-47db-9403-1768e0cb5674","status":"failed","userId":"a95c638e-a5c5-4d5a-bac3-440783832e88"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:53.668Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"95728b57-312a-4fa3-854f-6542a1aa88ee","status":"failed","userId":"da6e7756-6da7-4762-9478-15989c524ba5"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:53.727Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"0f5e5721-73b0-4a64-94a6-d372d1ed319e","status":"failed","userId":"078de71f-122d-41ec-be55-b4b93c20d6b8"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:53.728Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"417116ed-7c8b-4913-aa56-00549a16a439","status":"failed","userId":"6bc0b5c1-151e-4b05-b743-4762063acbee"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:53.895Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"be02616f-a58b-481e-b2aa-894bf4ea9404","status":"failed","userId":"009e09fc-311a-4851-949e-b76859139743"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:54.083Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"bb463fb3-2f18-45d5-a2fc-40fce7c4a8e2","status":"failed","userId":"d7d7720a-b095-4f24-8dea-4b4fae817a5a"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:54.084Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"16fa9e36-9332-4595-bc6e-1e0c67173dae","status":"failed","userId":"4aa44128-345e-4ebe-b3eb-39676d2596ce"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:54.301Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"2c6cceb3-6d75-45b0-8fd0-bda0b708105a","status":"failed","userId":"040790f4-eeb4-46d7-84e7-768e0f8c500b"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:54.315Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"6c8f18fd-0dba-4a94-8bf0-b804667cc62b","status":"failed","userId":"8f72b4c2-3265-4ba0-8f52-6a1ff9d5e497"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:54.319Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"ed1114a3-aebc-419f-98d1-3a9a3b37c61a","status":"failed","userId":"bb269993-1022-43b2-8926-4fbf4043ba0c"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:54.407Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"02e87e37-d55a-4a83-902d-0389dddab2fa","status":"failed","userId":"45f1e061-9c35-4aad-802e-6d1a58fbdaf0"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:54.503Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"3e09ab89-6060-4e6a-b3c0-d7a08e2dc63e","status":"failed","userId":"3653691f-57cf-4782-8edf-157e46f19bd8"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:54.513Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"b3ba67e6-ad9d-407f-8e6c-1e2f422ad840","status":"failed","userId":"392fd33d-ab74-4da6-a91a-02c56656a57c"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:54.714Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"fb1116b1-a5e1-4100-8ad5-1b99d2f5626f","status":"failed","userId":"b03799da-c6da-4925-87c6-901060f1ffc3"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:54.777Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"2b28451c-1940-4deb-b6c9-fb05b72d2eba","status":"failed","userId":"c540dde8-d7e7-4b31-946b-c44073078958"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:54.791Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"29c86e36-8f56-4b6e-864d-e5ab9d9c0eb8","status":"failed","userId":"11556c6b-f185-42ce-a3b6-369dd56a6ac5"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:54.901Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"aca2d0cc-dcf2-443d-8a32-a5bb4c608213","status":"failed","userId":"b713b871-2151-4908-82c7-e25043087388"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:54.946Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"6f2fd8e4-b671-4eb2-a448-659faa922611","status":"failed","userId":"8f153039-6f2b-411e-af79-c636d53cc3f9"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:55.009Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"e91643f1-a51c-4c93-892c-d6dee18f9c14","status":"failed","userId":"80b44903-76e5-4d53-9831-574ecab7f147"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:55.056Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"e871fb5e-1e2a-46c1-9a2a-04081585be3a","status":"failed","userId":"7e1bb330-f26a-4a0f-a0df-b16e75e63d36"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:55.225Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"4eca1c5a-2ffb-4996-9416-fd359c63a8a6","status":"failed","userId":"68abe24d-9fd9-4a34-9538-81ebed775b4c"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:55.286Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"00b2690b-3f04-4af6-82e7-4bf208b6eca2","status":"failed","userId":"37571634-9fe7-47c1-b50d-a0b5c5918171"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:55.426Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"f3111154-e2e7-4949-82f1-ea5776f97cab","status":"failed","userId":"a26a6dd4-23e9-46f7-b782-15f7859d8342"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:55.598Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"20cc46b5-f83d-4e2b-86ef-fed571528a50","status":"failed","userId":"ed022609-ebc6-41d0-a464-2dc10ca8773e"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:55.628Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"506efc46-0044-489c-8f5c-df7f522457dc","status":"failed","userId":"cc61ca36-c057-44e6-8dee-f275f24dc1c0"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:55.737Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"7870dc08-7870-4d93-bb9a-6b419a330294","status":"failed","userId":"24a91af5-f630-4d85-8a86-1863e3c977ec"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:55.828Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"fd3e8d9b-c646-4292-8a0b-ccfe32e6e8b2","status":"failed","userId":"b29a61ec-cda2-4ce1-9e63-a655109ba149"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:55.951Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"e9668692-e8f9-4212-be35-c7b70906154a","status":"failed","userId":"8cd96f02-f438-4e30-b81d-df35181b1dd3"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:55.997Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"51d2d7f8-0216-46b9-9d68-19463f60cf73","status":"failed","userId":"bc754f4a-5ac0-4053-8bba-a2a42a31a746"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:56.044Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"efb0205a-4ac9-4a67-8e64-d512cd142df7","status":"failed","userId":"f1b2443e-1fd7-40a2-b26e-001251819505"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:56.076Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"fe7c8560-8c4e-4a4a-9f67-3947e20b605b","status":"failed","userId":"f9b12420-847f-4968-b8b9-4153fdc8aae2"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:56.307Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"ec60db2c-393f-4432-b87d-f246f19161ce","status":"failed","userId":"849ecb09-081f-43d5-a71a-6bca85f6a8aa"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:56.308Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"992ee829-8114-489c-9c8e-40ece18550a3","status":"failed","userId":"b4bc5f99-ad7c-4687-83c5-9e9e21aeba64"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:56.368Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"d4bede51-d3bd-4269-ac73-01f49f528d08","status":"failed","userId":"27046cf8-947c-460f-bcac-21f0770d22ec"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:56.390Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"fe714dbf-bea6-4d59-8b14-eecc1bbc1411","status":"failed","userId":"fd129d2c-39d3-404c-a103-21a379463fcb"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:56.400Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"90ab426b-e3af-4374-bc4d-96c7580b6e1b","status":"failed","userId":"950a1c16-ba41-4ac6-bea8-b052c6bd32fb"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:56.601Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"4a9d0fce-f62a-4624-a8a6-9b41700464e9","status":"failed","userId":"49862644-6bd8-4161-bbf0-e0bb0565df48"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:56.829Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"a5eb9a84-2a5e-412c-a829-0a76b1fa8cc1","status":"failed","userId":"15060bb5-db18-49b3-931e-9222c270e677"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:56.837Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"0531a284-96f0-4593-b0e4-76fc83203b8e","status":"failed","userId":"c760022b-5504-4436-b0df-12e523fb08cf"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:56.868Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"e4b7fd1e-9f49-44e3-82d0-6f725f14d3a1","status":"failed","userId":"f47f861d-74bb-4198-a24d-8509841f9e80"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:56.897Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"1418fbbe-d7be-43b7-93f4-bc05f3a0406c","status":"failed","userId":"072f69fa-6cbe-400b-b796-7e3271da936d"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:57.051Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"472738dc-e3de-430e-8671-cdb3a4bc1ca3","status":"failed","userId":"d6d1f30e-be74-494c-8a9b-bb501e91a0e3"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:57.143Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"b66de2d6-4bb2-4b56-b703-23195898b34a","status":"failed","userId":"68b8a7f4-00cf-4787-9396-f1983ed53648"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:57.283Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"9d85c017-5dc3-4581-a34d-d336008fd414","status":"failed","userId":"b7572a8f-d3ba-410d-9871-a3439187285c"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:57.298Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"f2bb542a-ada9-4639-8347-e1d269b0a60d","status":"failed","userId":"31ffaa30-7eca-4caa-a144-e4d4184797f8"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:57.482Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"464191fe-682e-4b40-8340-84f10634ada9","status":"failed","userId":"44bf17ee-fb4d-4a0a-ab8c-ea5ac863032d"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:57.513Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"6b92acb0-8333-4ead-8cdd-56982fde8dce","status":"failed","userId":"25a10f5b-94bb-4e95-b143-09ead677e663"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:57.545Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"77f7a0dc-b005-4e9d-a5c9-fc4b5548431d","status":"failed","userId":"c50c6b3a-274f-47ad-97e4-19a762a6aed6"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:57.546Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"a5b78f8a-e8ab-4232-964c-d2f964ce877e","status":"failed","userId":"da9eee0f-2f64-4776-943c-45d0496b69e0"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:57.577Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"6a546621-9c70-43a7-acd9-45e6b47c80b1","status":"failed","userId":"ce779b7f-d252-445b-99cf-776c8d253223"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:57.578Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"418f211b-e163-4ba8-b13f-74d7d53c7496","status":"failed","userId":"4f114070-2569-44d2-ae17-e0e84d825a51"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:57.607Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"205fc9f9-8a40-4c1b-9996-5c5dc71694bc","status":"failed","userId":"36a5194c-32f2-4426-b5e4-76f19f18b956"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:57.669Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"bc08ec3c-681f-44f7-8c4f-2cf747cbca54","status":"failed","userId":"8644861e-dabc-4b8b-a627-a0ae38bc44fe"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:57.718Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"83d1a7c4-fb36-485f-a29a-72d47c66ee67","status":"failed","userId":"2280d0b1-52dd-4ea1-af58-caf1a0dc1ccb"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:57.779Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"e6392f8a-a825-48b3-8bac-13387e57d264","status":"failed","userId":"8eaf3b43-8867-4573-91ec-926d03d93a07"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:57.999Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"2eef24e2-9b02-4174-8550-e23e5de814d3","status":"failed","userId":"2db8eac1-765e-4011-bb8d-7b92904ac01b"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:58.059Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"8156b2af-e634-4eb4-a633-f75da0487db1","status":"failed","userId":"9b4c5ed1-e673-4ee9-b696-5be44a561be0"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:58.150Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"419efb9c-d674-4a0f-8265-d1b013d0e667","status":"failed","userId":"3de2f9e7-75b6-429a-90a4-b4366adf0874"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:58.196Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"c2fc42ee-3232-4c64-9095-46b80854611f","status":"failed","userId":"5cc55727-a792-495d-ac51-0d0e844e5d53"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:58.215Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"f7f3d621-8ff9-4ef9-a6d8-eef46124cfd3","status":"failed","userId":"ae217bd8-d2f3-4a8b-aac3-b3516790c2e2"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:58.274Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"c6c29de9-54c8-4497-b044-25e0e32acb56","status":"failed","userId":"77a4b43c-1906-4c6e-ab4b-7cf5ca9cc4fe"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:58.397Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"2945c4ec-edf8-4d6a-8b36-73991e0fc36a","status":"failed","userId":"4b9c8855-018c-44b3-89ef-8f0201454847"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:58.412Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"37b217c7-1770-47a3-9496-648091e1cf7c","status":"failed","userId":"39ede2d4-b490-4c9a-b193-f3328fbd4857"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:58.415Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"531eb1c6-36d4-46c2-975e-a182060ecd75","status":"failed","userId":"727601f0-6746-4c23-b6af-d4d8f27c36b3"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:58.646Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"9d2ca6c4-9eb6-4dfb-9331-90e7af086166","status":"failed","userId":"e5084c4d-a410-4bed-aaae-8b1e791b1180"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:58.647Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"65929f8e-412d-4043-9dfe-117594362aa6","status":"failed","userId":"c0a5909b-84fd-4460-8b7d-76601a8a73e2"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:59.098Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"931a64f5-7a25-4c4f-a818-3a501738b98c","status":"failed","userId":"8c044fe4-4f97-4ace-8360-641d1fe1e52c"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:59.266Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"4a610299-e43f-4ac3-b3b2-ac92563e5560","status":"failed","userId":"1beefbcc-ded3-4675-85df-6c1468d16c02"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:59.298Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"cfeb55e9-0840-4885-8da9-859e9d469036","status":"failed","userId":"50520da3-8855-4d53-bac8-711fc04cfd19"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:59.314Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"661ce4db-7ab4-4f33-80c9-54ddf85e53fa","status":"failed","userId":"ee3c2f77-9ce0-4e25-a239-0ccdb4b80c24"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:59.518Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"6b09de7b-051f-4526-8aee-126bb4e8a62f","status":"failed","userId":"0d81b437-2a5b-45bb-87e3-b22638c79d2d"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:59.766Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"cab94508-e557-47b1-9642-b4c0dba95480","status":"failed","userId":"829d69b3-2fdf-44eb-86a6-1d04b90cc581"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:59.858Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"6a3d6e80-dd87-45df-8de0-40ae2c149148","status":"failed","userId":"9ae3d55e-01fd-4000-9698-d17c23b6e39c"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:59.873Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"a0f8654b-b285-4d67-87bb-c95c4a743c7f","status":"failed","userId":"69ae9167-80c7-4164-8f08-14fc7e5f96b8"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:47:59.937Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"757d91fc-405d-4909-93ac-4028971e7398","status":"failed","userId":"6761c652-3586-4274-8e32-44b45eb0ab1b"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:00.027Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"2e6cf6b3-8b56-48d8-8ac1-cbe7e0beff55","status":"failed","userId":"62a862d9-1280-4b90-b469-db2e46dd6941"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:00.073Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"92e49306-edb7-4129-9e75-a1eb9d3653aa","status":"failed","userId":"c48a95d5-717b-487b-b2ce-61d7e09e5b43"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:00.133Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"be1703cb-15ec-4d17-9327-c71353b0f823","status":"failed","userId":"778981a0-fa31-4a3a-9d23-e9cbb9c0c952"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:00.274Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"3302a620-d718-4dc6-a3b2-d4eefd934be6","status":"failed","userId":"4bcc4e0f-367a-4ecd-a233-ec3e9d48de38"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:00.275Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"9e3b9846-09e6-4286-beaf-147f9db0f27b","status":"failed","userId":"7dde7770-1e0e-481e-af32-84bc96240188"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:00.305Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"83e24814-87e3-46f4-8381-52b0aa24c3dc","status":"failed","userId":"7f361f12-3132-4693-8a61-f2ad8a8c6b0c"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:00.383Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"c3ac7294-fab3-40bd-b4af-a94b1a2523dc","status":"failed","userId":"63a9574c-b28b-4dc2-b0a6-0e009a92c1ae"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:00.757Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"c643f010-a909-48da-bf1f-0bb678ca8eba","status":"failed","userId":"c52e498b-c989-4e27-b783-2fe3316085a3"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:04.394Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"fafda137-2dfb-4947-bedc-5a44f3421a1f","status":"failed","userId":"12e2ba85-6226-4701-80f5-f7831eedfd48"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:04.850Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"6c8f18fd-0dba-4a94-8bf0-b804667cc62b","status":"failed","userId":"8f72b4c2-3265-4ba0-8f52-6a1ff9d5e497"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:05.749Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"2c6cceb3-6d75-45b0-8fd0-bda0b708105a","status":"failed","userId":"040790f4-eeb4-46d7-84e7-768e0f8c500b"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:05.762Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"3e09ab89-6060-4e6a-b3c0-d7a08e2dc63e","status":"failed","userId":"3653691f-57cf-4782-8edf-157e46f19bd8"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:05.890Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"e6e2a0b1-f712-415b-b838-843d3cf541a8","status":"failed","userId":"3dbea5fc-4925-4aa7-bfcd-318599a9e5b1"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:05.905Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"be02616f-a58b-481e-b2aa-894bf4ea9404","status":"failed","userId":"009e09fc-311a-4851-949e-b76859139743"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:05.906Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"2b28451c-1940-4deb-b6c9-fb05b72d2eba","status":"failed","userId":"c540dde8-d7e7-4b31-946b-c44073078958"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:05.968Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"296471d2-b7eb-4324-a17a-78d362373419","status":"failed","userId":"cb7a1876-d50d-4ac0-b263-6790e313d4f8"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:06.013Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"ed1114a3-aebc-419f-98d1-3a9a3b37c61a","status":"failed","userId":"bb269993-1022-43b2-8926-4fbf4043ba0c"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:06.119Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"fb1116b1-a5e1-4100-8ad5-1b99d2f5626f","status":"failed","userId":"b03799da-c6da-4925-87c6-901060f1ffc3"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:06.351Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"bb463fb3-2f18-45d5-a2fc-40fce7c4a8e2","status":"failed","userId":"d7d7720a-b095-4f24-8dea-4b4fae817a5a"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:06.416Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"16fa9e36-9332-4595-bc6e-1e0c67173dae","status":"failed","userId":"4aa44128-345e-4ebe-b3eb-39676d2596ce"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:06.598Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"417116ed-7c8b-4913-aa56-00549a16a439","status":"failed","userId":"6bc0b5c1-151e-4b05-b743-4762063acbee"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:06.738Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"00b2690b-3f04-4af6-82e7-4bf208b6eca2","status":"failed","userId":"37571634-9fe7-47c1-b50d-a0b5c5918171"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:06.882Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"b3ba67e6-ad9d-407f-8e6c-1e2f422ad840","status":"failed","userId":"392fd33d-ab74-4da6-a91a-02c56656a57c"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:07.007Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"506efc46-0044-489c-8f5c-df7f522457dc","status":"failed","userId":"cc61ca36-c057-44e6-8dee-f275f24dc1c0"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:07.010Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"29c86e36-8f56-4b6e-864d-e5ab9d9c0eb8","status":"failed","userId":"11556c6b-f185-42ce-a3b6-369dd56a6ac5"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:07.016Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"f3111154-e2e7-4949-82f1-ea5776f97cab","status":"failed","userId":"a26a6dd4-23e9-46f7-b782-15f7859d8342"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:07.180Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"fd3e8d9b-c646-4292-8a0b-ccfe32e6e8b2","status":"failed","userId":"b29a61ec-cda2-4ce1-9e63-a655109ba149"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:07.309Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"20cc46b5-f83d-4e2b-86ef-fed571528a50","status":"failed","userId":"ed022609-ebc6-41d0-a464-2dc10ca8773e"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:07.349Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"fe7c8560-8c4e-4a4a-9f67-3947e20b605b","status":"failed","userId":"f9b12420-847f-4968-b8b9-4153fdc8aae2"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:07.426Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"02e87e37-d55a-4a83-902d-0389dddab2fa","status":"failed","userId":"45f1e061-9c35-4aad-802e-6d1a58fbdaf0"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:07.473Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"6f2fd8e4-b671-4eb2-a448-659faa922611","status":"failed","userId":"8f153039-6f2b-411e-af79-c636d53cc3f9"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:07.487Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"51d2d7f8-0216-46b9-9d68-19463f60cf73","status":"failed","userId":"bc754f4a-5ac0-4053-8bba-a2a42a31a746"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:07.596Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"d4bede51-d3bd-4269-ac73-01f49f528d08","status":"failed","userId":"27046cf8-947c-460f-bcac-21f0770d22ec"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:07.677Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"e91643f1-a51c-4c93-892c-d6dee18f9c14","status":"failed","userId":"80b44903-76e5-4d53-9831-574ecab7f147"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:07.842Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"e871fb5e-1e2a-46c1-9a2a-04081585be3a","status":"failed","userId":"7e1bb330-f26a-4a0f-a0df-b16e75e63d36"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:07.934Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"aca2d0cc-dcf2-443d-8a32-a5bb4c608213","status":"failed","userId":"b713b871-2151-4908-82c7-e25043087388"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:07.949Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"fe714dbf-bea6-4d59-8b14-eecc1bbc1411","status":"failed","userId":"fd129d2c-39d3-404c-a103-21a379463fcb"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:08.137Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"4eca1c5a-2ffb-4996-9416-fd359c63a8a6","status":"failed","userId":"68abe24d-9fd9-4a34-9538-81ebed775b4c"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:08.307Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"7870dc08-7870-4d93-bb9a-6b419a330294","status":"failed","userId":"24a91af5-f630-4d85-8a86-1863e3c977ec"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:08.556Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"efb0205a-4ac9-4a67-8e64-d512cd142df7","status":"failed","userId":"f1b2443e-1fd7-40a2-b26e-001251819505"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:08.573Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"e9668692-e8f9-4212-be35-c7b70906154a","status":"failed","userId":"8cd96f02-f438-4e30-b81d-df35181b1dd3"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:08.702Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"e4b7fd1e-9f49-44e3-82d0-6f725f14d3a1","status":"failed","userId":"f47f861d-74bb-4198-a24d-8509841f9e80"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:08.718Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"472738dc-e3de-430e-8671-cdb3a4bc1ca3","status":"failed","userId":"d6d1f30e-be74-494c-8a9b-bb501e91a0e3"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:08.719Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"ec60db2c-393f-4432-b87d-f246f19161ce","status":"failed","userId":"849ecb09-081f-43d5-a71a-6bca85f6a8aa"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:08.719Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"6b92acb0-8333-4ead-8cdd-56982fde8dce","status":"failed","userId":"25a10f5b-94bb-4e95-b143-09ead677e663"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:08.872Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"f2bb542a-ada9-4639-8347-e1d269b0a60d","status":"failed","userId":"31ffaa30-7eca-4caa-a144-e4d4184797f8"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:08.888Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"90ab426b-e3af-4374-bc4d-96c7580b6e1b","status":"failed","userId":"950a1c16-ba41-4ac6-bea8-b052c6bd32fb"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:09.071Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"464191fe-682e-4b40-8340-84f10634ada9","status":"failed","userId":"44bf17ee-fb4d-4a0a-ab8c-ea5ac863032d"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:09.120Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"9d85c017-5dc3-4581-a34d-d336008fd414","status":"failed","userId":"b7572a8f-d3ba-410d-9871-a3439187285c"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:09.149Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"a5eb9a84-2a5e-412c-a829-0a76b1fa8cc1","status":"failed","userId":"15060bb5-db18-49b3-931e-9222c270e677"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:09.211Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"0531a284-96f0-4593-b0e4-76fc83203b8e","status":"failed","userId":"c760022b-5504-4436-b0df-12e523fb08cf"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:09.287Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"a5b78f8a-e8ab-4232-964c-d2f964ce877e","status":"failed","userId":"da9eee0f-2f64-4776-943c-45d0496b69e0"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:09.319Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"992ee829-8114-489c-9c8e-40ece18550a3","status":"failed","userId":"b4bc5f99-ad7c-4687-83c5-9e9e21aeba64"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:09.334Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"418f211b-e163-4ba8-b13f-74d7d53c7496","status":"failed","userId":"4f114070-2569-44d2-ae17-e0e84d825a51"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:09.411Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"1418fbbe-d7be-43b7-93f4-bc05f3a0406c","status":"failed","userId":"072f69fa-6cbe-400b-b796-7e3271da936d"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:09.520Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"77f7a0dc-b005-4e9d-a5c9-fc4b5548431d","status":"failed","userId":"c50c6b3a-274f-47ad-97e4-19a762a6aed6"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:09.583Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"4a9d0fce-f62a-4624-a8a6-9b41700464e9","status":"failed","userId":"49862644-6bd8-4161-bbf0-e0bb0565df48"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:09.614Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"bc08ec3c-681f-44f7-8c4f-2cf747cbca54","status":"failed","userId":"8644861e-dabc-4b8b-a627-a0ae38bc44fe"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:09.645Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"6a546621-9c70-43a7-acd9-45e6b47c80b1","status":"failed","userId":"ce779b7f-d252-445b-99cf-776c8d253223"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:09.707Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"2eef24e2-9b02-4174-8550-e23e5de814d3","status":"failed","userId":"2db8eac1-765e-4011-bb8d-7b92904ac01b"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:09.846Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"b66de2d6-4bb2-4b56-b703-23195898b34a","status":"failed","userId":"68b8a7f4-00cf-4787-9396-f1983ed53648"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:09.955Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"e6392f8a-a825-48b3-8bac-13387e57d264","status":"failed","userId":"8eaf3b43-8867-4573-91ec-926d03d93a07"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:09.969Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"531eb1c6-36d4-46c2-975e-a182060ecd75","status":"failed","userId":"727601f0-6746-4c23-b6af-d4d8f27c36b3"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:10.233Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"cfeb55e9-0840-4885-8da9-859e9d469036","status":"failed","userId":"50520da3-8855-4d53-bac8-711fc04cfd19"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:10.487Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"c2fc42ee-3232-4c64-9095-46b80854611f","status":"failed","userId":"5cc55727-a792-495d-ac51-0d0e844e5d53"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:10.495Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"37b217c7-1770-47a3-9496-648091e1cf7c","status":"failed","userId":"39ede2d4-b490-4c9a-b193-f3328fbd4857"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:10.511Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"2945c4ec-edf8-4d6a-8b36-73991e0fc36a","status":"failed","userId":"4b9c8855-018c-44b3-89ef-8f0201454847"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:10.526Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"419efb9c-d674-4a0f-8265-d1b013d0e667","status":"failed","userId":"3de2f9e7-75b6-429a-90a4-b4366adf0874"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:10.711Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"f7f3d621-8ff9-4ef9-a6d8-eef46124cfd3","status":"failed","userId":"ae217bd8-d2f3-4a8b-aac3-b3516790c2e2"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:10.790Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"9d2ca6c4-9eb6-4dfb-9331-90e7af086166","status":"failed","userId":"e5084c4d-a410-4bed-aaae-8b1e791b1180"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:10.867Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"205fc9f9-8a40-4c1b-9996-5c5dc71694bc","status":"failed","userId":"36a5194c-32f2-4426-b5e4-76f19f18b956"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:10.899Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"6b09de7b-051f-4526-8aee-126bb4e8a62f","status":"failed","userId":"0d81b437-2a5b-45bb-87e3-b22638c79d2d"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:10.961Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"661ce4db-7ab4-4f33-80c9-54ddf85e53fa","status":"failed","userId":"ee3c2f77-9ce0-4e25-a239-0ccdb4b80c24"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:10.975Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"83d1a7c4-fb36-485f-a29a-72d47c66ee67","status":"failed","userId":"2280d0b1-52dd-4ea1-af58-caf1a0dc1ccb"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:11.036Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"931a64f5-7a25-4c4f-a818-3a501738b98c","status":"failed","userId":"8c044fe4-4f97-4ace-8360-641d1fe1e52c"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:11.253Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"3302a620-d718-4dc6-a3b2-d4eefd934be6","status":"failed","userId":"4bcc4e0f-367a-4ecd-a233-ec3e9d48de38"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:11.405Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"c6c29de9-54c8-4497-b044-25e0e32acb56","status":"failed","userId":"77a4b43c-1906-4c6e-ab4b-7cf5ca9cc4fe"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:11.500Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"6a3d6e80-dd87-45df-8de0-40ae2c149148","status":"failed","userId":"9ae3d55e-01fd-4000-9698-d17c23b6e39c"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:11.580Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"a0f8654b-b285-4d67-87bb-c95c4a743c7f","status":"failed","userId":"69ae9167-80c7-4164-8f08-14fc7e5f96b8"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:11.590Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"8156b2af-e634-4eb4-a633-f75da0487db1","status":"failed","userId":"9b4c5ed1-e673-4ee9-b696-5be44a561be0"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:11.591Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"92e49306-edb7-4129-9e75-a1eb9d3653aa","status":"failed","userId":"c48a95d5-717b-487b-b2ce-61d7e09e5b43"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:11.636Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"65929f8e-412d-4043-9dfe-117594362aa6","status":"failed","userId":"c0a5909b-84fd-4460-8b7d-76601a8a73e2"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:11.791Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"9e3b9846-09e6-4286-beaf-147f9db0f27b","status":"failed","userId":"7dde7770-1e0e-481e-af32-84bc96240188"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:11.930Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"cab94508-e557-47b1-9642-b4c0dba95480","status":"failed","userId":"829d69b3-2fdf-44eb-86a6-1d04b90cc581"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:12.070Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"4a610299-e43f-4ac3-b3b2-ac92563e5560","status":"failed","userId":"1beefbcc-ded3-4675-85df-6c1468d16c02"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:12.071Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"83e24814-87e3-46f4-8381-52b0aa24c3dc","status":"failed","userId":"7f361f12-3132-4693-8a61-f2ad8a8c6b0c"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:12.210Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"757d91fc-405d-4909-93ac-4028971e7398","status":"failed","userId":"6761c652-3586-4274-8e32-44b45eb0ab1b"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:12.252Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"2e6cf6b3-8b56-48d8-8ac1-cbe7e0beff55","status":"failed","userId":"62a862d9-1280-4b90-b469-db2e46dd6941"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:13.023Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"be1703cb-15ec-4d17-9327-c71353b0f823","status":"failed","userId":"778981a0-fa31-4a3a-9d23-e9cbb9c0c952"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:13.204Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"c3ac7294-fab3-40bd-b4af-a94b1a2523dc","status":"failed","userId":"63a9574c-b28b-4dc2-b0a6-0e009a92c1ae"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:13.757Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"fafda137-2dfb-4947-bedc-5a44f3421a1f","status":"failed","userId":"12e2ba85-6226-4701-80f5-f7831eedfd48"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:25.927Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"c643f010-a909-48da-bf1f-0bb678ca8eba","status":"failed","userId":"c52e498b-c989-4e27-b783-2fe3316085a3"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:26.926Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"2c6cceb3-6d75-45b0-8fd0-bda0b708105a","status":"failed","userId":"040790f4-eeb4-46d7-84e7-768e0f8c500b"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:27.252Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"be02616f-a58b-481e-b2aa-894bf4ea9404","status":"failed","userId":"009e09fc-311a-4851-949e-b76859139743"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:27.344Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"6c8f18fd-0dba-4a94-8bf0-b804667cc62b","status":"failed","userId":"8f72b4c2-3265-4ba0-8f52-6a1ff9d5e497"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:27.651Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"417116ed-7c8b-4913-aa56-00549a16a439","status":"failed","userId":"6bc0b5c1-151e-4b05-b743-4762063acbee"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:27.867Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"16fa9e36-9332-4595-bc6e-1e0c67173dae","status":"failed","userId":"4aa44128-345e-4ebe-b3eb-39676d2596ce"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:27.869Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"bb463fb3-2f18-45d5-a2fc-40fce7c4a8e2","status":"failed","userId":"d7d7720a-b095-4f24-8dea-4b4fae817a5a"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:27.883Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"ed1114a3-aebc-419f-98d1-3a9a3b37c61a","status":"failed","userId":"bb269993-1022-43b2-8926-4fbf4043ba0c"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:27.885Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"296471d2-b7eb-4324-a17a-78d362373419","status":"failed","userId":"cb7a1876-d50d-4ac0-b263-6790e313d4f8"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:28.100Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"fb1116b1-a5e1-4100-8ad5-1b99d2f5626f","status":"failed","userId":"b03799da-c6da-4925-87c6-901060f1ffc3"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:28.102Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"2b28451c-1940-4deb-b6c9-fb05b72d2eba","status":"failed","userId":"c540dde8-d7e7-4b31-946b-c44073078958"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:28.673Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"e6e2a0b1-f712-415b-b838-843d3cf541a8","status":"failed","userId":"3dbea5fc-4925-4aa7-bfcd-318599a9e5b1"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:28.721Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"3e09ab89-6060-4e6a-b3c0-d7a08e2dc63e","status":"failed","userId":"3653691f-57cf-4782-8edf-157e46f19bd8"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:28.799Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"fe7c8560-8c4e-4a4a-9f67-3947e20b605b","status":"failed","userId":"f9b12420-847f-4968-b8b9-4153fdc8aae2"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:28.811Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"00b2690b-3f04-4af6-82e7-4bf208b6eca2","status":"failed","userId":"37571634-9fe7-47c1-b50d-a0b5c5918171"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:28.858Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"b3ba67e6-ad9d-407f-8e6c-1e2f422ad840","status":"failed","userId":"392fd33d-ab74-4da6-a91a-02c56656a57c"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:28.859Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"29c86e36-8f56-4b6e-864d-e5ab9d9c0eb8","status":"failed","userId":"11556c6b-f185-42ce-a3b6-369dd56a6ac5"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:28.998Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"e871fb5e-1e2a-46c1-9a2a-04081585be3a","status":"failed","userId":"7e1bb330-f26a-4a0f-a0df-b16e75e63d36"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:29.097Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"fd3e8d9b-c646-4292-8a0b-ccfe32e6e8b2","status":"failed","userId":"b29a61ec-cda2-4ce1-9e63-a655109ba149"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:29.243Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"fe714dbf-bea6-4d59-8b14-eecc1bbc1411","status":"failed","userId":"fd129d2c-39d3-404c-a103-21a379463fcb"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:29.289Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"02e87e37-d55a-4a83-902d-0389dddab2fa","status":"failed","userId":"45f1e061-9c35-4aad-802e-6d1a58fbdaf0"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:29.504Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"aca2d0cc-dcf2-443d-8a32-a5bb4c608213","status":"failed","userId":"b713b871-2151-4908-82c7-e25043087388"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:29.505Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"506efc46-0044-489c-8f5c-df7f522457dc","status":"failed","userId":"cc61ca36-c057-44e6-8dee-f275f24dc1c0"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:29.634Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"f3111154-e2e7-4949-82f1-ea5776f97cab","status":"failed","userId":"a26a6dd4-23e9-46f7-b782-15f7859d8342"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:29.782Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"20cc46b5-f83d-4e2b-86ef-fed571528a50","status":"failed","userId":"ed022609-ebc6-41d0-a464-2dc10ca8773e"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:29.828Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"4eca1c5a-2ffb-4996-9416-fd359c63a8a6","status":"failed","userId":"68abe24d-9fd9-4a34-9538-81ebed775b4c"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:29.831Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"ec60db2c-393f-4432-b87d-f246f19161ce","status":"failed","userId":"849ecb09-081f-43d5-a71a-6bca85f6a8aa"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:29.951Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"6f2fd8e4-b671-4eb2-a448-659faa922611","status":"failed","userId":"8f153039-6f2b-411e-af79-c636d53cc3f9"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:30.001Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"6b92acb0-8333-4ead-8cdd-56982fde8dce","status":"failed","userId":"25a10f5b-94bb-4e95-b143-09ead677e663"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:30.049Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"51d2d7f8-0216-46b9-9d68-19463f60cf73","status":"failed","userId":"bc754f4a-5ac0-4053-8bba-a2a42a31a746"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:30.169Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"d4bede51-d3bd-4269-ac73-01f49f528d08","status":"failed","userId":"27046cf8-947c-460f-bcac-21f0770d22ec"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:30.500Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"e4b7fd1e-9f49-44e3-82d0-6f725f14d3a1","status":"failed","userId":"f47f861d-74bb-4198-a24d-8509841f9e80"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:30.709Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"7870dc08-7870-4d93-bb9a-6b419a330294","status":"failed","userId":"24a91af5-f630-4d85-8a86-1863e3c977ec"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:30.756Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"e91643f1-a51c-4c93-892c-d6dee18f9c14","status":"failed","userId":"80b44903-76e5-4d53-9831-574ecab7f147"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:30.787Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"992ee829-8114-489c-9c8e-40ece18550a3","status":"failed","userId":"b4bc5f99-ad7c-4687-83c5-9e9e21aeba64"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:30.852Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"472738dc-e3de-430e-8671-cdb3a4bc1ca3","status":"failed","userId":"d6d1f30e-be74-494c-8a9b-bb501e91a0e3"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:30.949Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"f2bb542a-ada9-4639-8347-e1d269b0a60d","status":"failed","userId":"31ffaa30-7eca-4caa-a144-e4d4184797f8"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:31.036Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"1418fbbe-d7be-43b7-93f4-bc05f3a0406c","status":"failed","userId":"072f69fa-6cbe-400b-b796-7e3271da936d"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:31.081Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"464191fe-682e-4b40-8340-84f10634ada9","status":"failed","userId":"44bf17ee-fb4d-4a0a-ab8c-ea5ac863032d"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:31.095Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"9d85c017-5dc3-4581-a34d-d336008fd414","status":"failed","userId":"b7572a8f-d3ba-410d-9871-a3439187285c"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:31.127Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"0531a284-96f0-4593-b0e4-76fc83203b8e","status":"failed","userId":"c760022b-5504-4436-b0df-12e523fb08cf"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:31.307Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"bc08ec3c-681f-44f7-8c4f-2cf747cbca54","status":"failed","userId":"8644861e-dabc-4b8b-a627-a0ae38bc44fe"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:31.316Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"4a9d0fce-f62a-4624-a8a6-9b41700464e9","status":"failed","userId":"49862644-6bd8-4161-bbf0-e0bb0565df48"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:31.378Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"efb0205a-4ac9-4a67-8e64-d512cd142df7","status":"failed","userId":"f1b2443e-1fd7-40a2-b26e-001251819505"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:31.405Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"e6392f8a-a825-48b3-8bac-13387e57d264","status":"failed","userId":"8eaf3b43-8867-4573-91ec-926d03d93a07"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:31.638Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"e9668692-e8f9-4212-be35-c7b70906154a","status":"failed","userId":"8cd96f02-f438-4e30-b81d-df35181b1dd3"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:31.641Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"a5eb9a84-2a5e-412c-a829-0a76b1fa8cc1","status":"failed","userId":"15060bb5-db18-49b3-931e-9222c270e677"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:31.734Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"77f7a0dc-b005-4e9d-a5c9-fc4b5548431d","status":"failed","userId":"c50c6b3a-274f-47ad-97e4-19a762a6aed6"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:31.806Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"90ab426b-e3af-4374-bc4d-96c7580b6e1b","status":"failed","userId":"950a1c16-ba41-4ac6-bea8-b052c6bd32fb"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:31.809Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"2eef24e2-9b02-4174-8550-e23e5de814d3","status":"failed","userId":"2db8eac1-765e-4011-bb8d-7b92904ac01b"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:31.852Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"a5b78f8a-e8ab-4232-964c-d2f964ce877e","status":"failed","userId":"da9eee0f-2f64-4776-943c-45d0496b69e0"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:32.048Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"418f211b-e163-4ba8-b13f-74d7d53c7496","status":"failed","userId":"4f114070-2569-44d2-ae17-e0e84d825a51"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:32.381Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"b66de2d6-4bb2-4b56-b703-23195898b34a","status":"failed","userId":"68b8a7f4-00cf-4787-9396-f1983ed53648"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:32.406Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"2945c4ec-edf8-4d6a-8b36-73991e0fc36a","status":"failed","userId":"4b9c8855-018c-44b3-89ef-8f0201454847"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:32.411Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"6a546621-9c70-43a7-acd9-45e6b47c80b1","status":"failed","userId":"ce779b7f-d252-445b-99cf-776c8d253223"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:32.530Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"cfeb55e9-0840-4885-8da9-859e9d469036","status":"failed","userId":"50520da3-8855-4d53-bac8-711fc04cfd19"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:32.545Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"3302a620-d718-4dc6-a3b2-d4eefd934be6","status":"failed","userId":"4bcc4e0f-367a-4ecd-a233-ec3e9d48de38"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:32.577Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"6a3d6e80-dd87-45df-8de0-40ae2c149148","status":"failed","userId":"9ae3d55e-01fd-4000-9698-d17c23b6e39c"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:32.702Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"661ce4db-7ab4-4f33-80c9-54ddf85e53fa","status":"failed","userId":"ee3c2f77-9ce0-4e25-a239-0ccdb4b80c24"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:32.747Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"931a64f5-7a25-4c4f-a818-3a501738b98c","status":"failed","userId":"8c044fe4-4f97-4ace-8360-641d1fe1e52c"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:32.795Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"c2fc42ee-3232-4c64-9095-46b80854611f","status":"failed","userId":"5cc55727-a792-495d-ac51-0d0e844e5d53"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:32.856Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"531eb1c6-36d4-46c2-975e-a182060ecd75","status":"failed","userId":"727601f0-6746-4c23-b6af-d4d8f27c36b3"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:33.141Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"c6c29de9-54c8-4497-b044-25e0e32acb56","status":"failed","userId":"77a4b43c-1906-4c6e-ab4b-7cf5ca9cc4fe"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:33.358Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"9d2ca6c4-9eb6-4dfb-9331-90e7af086166","status":"failed","userId":"e5084c4d-a410-4bed-aaae-8b1e791b1180"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:33.403Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"37b217c7-1770-47a3-9496-648091e1cf7c","status":"failed","userId":"39ede2d4-b490-4c9a-b193-f3328fbd4857"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:33.542Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"9e3b9846-09e6-4286-beaf-147f9db0f27b","status":"failed","userId":"7dde7770-1e0e-481e-af32-84bc96240188"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:33.695Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"83d1a7c4-fb36-485f-a29a-72d47c66ee67","status":"failed","userId":"2280d0b1-52dd-4ea1-af58-caf1a0dc1ccb"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:33.728Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"92e49306-edb7-4129-9e75-a1eb9d3653aa","status":"failed","userId":"c48a95d5-717b-487b-b2ce-61d7e09e5b43"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:33.821Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"419efb9c-d674-4a0f-8265-d1b013d0e667","status":"failed","userId":"3de2f9e7-75b6-429a-90a4-b4366adf0874"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:33.836Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"f7f3d621-8ff9-4ef9-a6d8-eef46124cfd3","status":"failed","userId":"ae217bd8-d2f3-4a8b-aac3-b3516790c2e2"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:33.938Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"6b09de7b-051f-4526-8aee-126bb4e8a62f","status":"failed","userId":"0d81b437-2a5b-45bb-87e3-b22638c79d2d"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:33.947Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"65929f8e-412d-4043-9dfe-117594362aa6","status":"failed","userId":"c0a5909b-84fd-4460-8b7d-76601a8a73e2"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:33.977Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"205fc9f9-8a40-4c1b-9996-5c5dc71694bc","status":"failed","userId":"36a5194c-32f2-4426-b5e4-76f19f18b956"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:34.010Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"a0f8654b-b285-4d67-87bb-c95c4a743c7f","status":"failed","userId":"69ae9167-80c7-4164-8f08-14fc7e5f96b8"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:34.151Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"8156b2af-e634-4eb4-a633-f75da0487db1","status":"failed","userId":"9b4c5ed1-e673-4ee9-b696-5be44a561be0"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:34.257Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"83e24814-87e3-46f4-8381-52b0aa24c3dc","status":"failed","userId":"7f361f12-3132-4693-8a61-f2ad8a8c6b0c"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:34.364Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"cab94508-e557-47b1-9642-b4c0dba95480","status":"failed","userId":"829d69b3-2fdf-44eb-86a6-1d04b90cc581"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:34.513Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"4a610299-e43f-4ac3-b3b2-ac92563e5560","status":"failed","userId":"1beefbcc-ded3-4675-85df-6c1468d16c02"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:34.548Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"be1703cb-15ec-4d17-9327-c71353b0f823","status":"failed","userId":"778981a0-fa31-4a3a-9d23-e9cbb9c0c952"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:34.687Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"757d91fc-405d-4909-93ac-4028971e7398","status":"failed","userId":"6761c652-3586-4274-8e32-44b45eb0ab1b"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:34.914Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"c3ac7294-fab3-40bd-b4af-a94b1a2523dc","status":"failed","userId":"63a9574c-b28b-4dc2-b0a6-0e009a92c1ae"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:35.052Z"}
{"body":{"errorMessage":"Request failed with status code 401","jobId":"2e6cf6b3-8b56-48d8-8ac1-cbe7e0beff55","status":"failed","userId":"62a862d9-1280-4b90-b469-db2e46dd6941"},"error":"\"error\" is required","level":"warn","message":"Notification validation failed","path":"/analysis-failed","service":"notification-service","timestamp":"2025-07-18T05:48:35.101Z"}
{"level":"info","message":"Socket service initialized","service":"notification-service","timestamp":"2025-07-18T05:52:17.355Z"}
{"level":"info","message":"Notification Service running on port 3005","service":"notification-service","timestamp":"2025-07-18T05:52:17.360Z"}
