/**
 * Load Testing Script - 100 Concurrent Users
 * 
 * Script untuk mensimulasikan 100 user yang login dan submit assessment bersamaan
 * dalam 1 jaringan yang sama
 */

const axios = require('axios');
const { v4: uuidv4 } = require('uuid');

// Configuration
const CONFIG = {
  API_GATEWAY_URL: process.env.API_GATEWAY_URL || 'http://localhost:3000',
  CONCURRENT_USERS: parseInt(process.env.CONCURRENT_USERS || '100'),
  ASSESSMENT_DELAY: parseInt(process.env.ASSESSMENT_DELAY || '0'), // ms delay between submissions
  GEMINI_TIER: process.env.GEMINI_TIER || 'free', // 'free' or 'paid'
  NETWORK_SIMULATION: {
    // Simulasi jaringan yang sama - IP range yang sama
    baseIP: '192.168.1.',
    startIP: 100,
    endIP: 199
  },
  TIMEOUT: 30000, // 30 seconds timeout
  RETRY_ATTEMPTS: 3,
  // Rate limiting aware configuration
  RATE_LIMIT_AWARE: true
};

// Sample assessment data
const SAMPLE_ASSESSMENT = {
  riasec: {
    realistic: Math.floor(Math.random() * 100),
    investigative: Math.floor(Math.random() * 100),
    artistic: Math.floor(Math.random() * 100),
    social: Math.floor(Math.random() * 100),
    enterprising: Math.floor(Math.random() * 100),
    conventional: Math.floor(Math.random() * 100)
  },
  ocean: {
    openness: Math.floor(Math.random() * 100),
    conscientiousness: Math.floor(Math.random() * 100),
    extraversion: Math.floor(Math.random() * 100),
    agreeableness: Math.floor(Math.random() * 100),
    neuroticism: Math.floor(Math.random() * 100)
  },
  viaIs: {
    creativity: Math.floor(Math.random() * 100),
    curiosity: Math.floor(Math.random() * 100),
    judgment: Math.floor(Math.random() * 100),
    loveOfLearning: Math.floor(Math.random() * 100),
    perspective: Math.floor(Math.random() * 100),
    bravery: Math.floor(Math.random() * 100),
    perseverance: Math.floor(Math.random() * 100),
    honesty: Math.floor(Math.random() * 100),
    zest: Math.floor(Math.random() * 100),
    love: Math.floor(Math.random() * 100),
    kindness: Math.floor(Math.random() * 100),
    socialIntelligence: Math.floor(Math.random() * 100),
    teamwork: Math.floor(Math.random() * 100),
    fairness: Math.floor(Math.random() * 100),
    leadership: Math.floor(Math.random() * 100),
    forgiveness: Math.floor(Math.random() * 100),
    humility: Math.floor(Math.random() * 100),
    prudence: Math.floor(Math.random() * 100),
    selfRegulation: Math.floor(Math.random() * 100),
    appreciationOfBeauty: Math.floor(Math.random() * 100),
    gratitude: Math.floor(Math.random() * 100),
    hope: Math.floor(Math.random() * 100),
    humor: Math.floor(Math.random() * 100),
    spirituality: Math.floor(Math.random() * 100)
  }
};

// Statistics tracking
const stats = {
  totalUsers: 0,
  successfulLogins: 0,
  failedLogins: 0,
  successfulSubmissions: 0,
  failedSubmissions: 0,
  totalResponseTime: 0,
  minResponseTime: Infinity,
  maxResponseTime: 0,
  errors: [],
  startTime: null,
  endTime: null
};

/**
 * Generate random IP address in the same network
 */
function generateNetworkIP(userIndex) {
  const { baseIP, startIP, endIP } = CONFIG.NETWORK_SIMULATION;
  const ipSuffix = startIP + (userIndex % (endIP - startIP + 1));
  return `${baseIP}${ipSuffix}`;
}

/**
 * Create test user credentials
 */
function createTestUser(index) {
  // Use timestamp to ensure unique users for each test run
  const timestamp = Date.now();
  return {
    email: `loadtest_${timestamp}_${index}@example.com`,
    password: 'LoadTest123!',
    name: `Load Test User ${index}`,
    ip: generateNetworkIP(index)
  };
}

/**
 * Register a test user
 */
async function registerUser(userData) {
  try {
    const response = await axios.post(`${CONFIG.API_GATEWAY_URL}/auth/register`, {
      email: userData.email,
      password: userData.password
    }, {
      timeout: CONFIG.TIMEOUT,
      headers: {
        'X-Forwarded-For': userData.ip,
        'X-Real-IP': userData.ip
      }
    });
    
    return response.data;
  } catch (error) {
    if (error.response?.status === 409 ||
        (error.response?.status === 400 && error.response?.data?.error?.code === 'EMAIL_EXISTS')) {
      // User already exists, that's okay for load testing
      return { success: true, message: 'User already exists' };
    }
    throw error;
  }
}

/**
 * Login user and get JWT token
 */
async function loginUser(userData) {
  const startTime = Date.now();
  
  try {
    const response = await axios.post(`${CONFIG.API_GATEWAY_URL}/auth/login`, {
      email: userData.email,
      password: userData.password
    }, {
      timeout: CONFIG.TIMEOUT,
      headers: {
        'X-Forwarded-For': userData.ip,
        'X-Real-IP': userData.ip
      }
    });
    
    const responseTime = Date.now() - startTime;
    updateResponseTimeStats(responseTime);
    
    return {
      success: true,
      token: response.data.data.token,
      user: response.data.data.user,
      responseTime
    };
  } catch (error) {
    const responseTime = Date.now() - startTime;
    updateResponseTimeStats(responseTime);
    
    throw {
      ...error,
      responseTime,
      userData: userData.email
    };
  }
}

/**
 * Submit assessment data
 */
async function submitAssessment(token, userData, assessmentData) {
  const startTime = Date.now();
  
  try {
    const response = await axios.post(`${CONFIG.API_GATEWAY_URL}/assessments/submit`, 
      assessmentData, 
      {
        timeout: CONFIG.TIMEOUT,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          'X-Forwarded-For': userData.ip,
          'X-Real-IP': userData.ip
        }
      }
    );
    
    const responseTime = Date.now() - startTime;
    updateResponseTimeStats(responseTime);
    
    return {
      success: true,
      jobId: response.data.data.jobId,
      queuePosition: response.data.data.queuePosition,
      responseTime
    };
  } catch (error) {
    const responseTime = Date.now() - startTime;
    updateResponseTimeStats(responseTime);
    
    throw {
      ...error,
      responseTime,
      userData: userData.email
    };
  }
}

/**
 * Update response time statistics
 */
function updateResponseTimeStats(responseTime) {
  stats.totalResponseTime += responseTime;
  stats.minResponseTime = Math.min(stats.minResponseTime, responseTime);
  stats.maxResponseTime = Math.max(stats.maxResponseTime, responseTime);
}

/**
 * Generate random assessment data for each user
 */
function generateRandomAssessment() {
  const assessment = JSON.parse(JSON.stringify(SAMPLE_ASSESSMENT));

  // Randomize all values
  Object.keys(assessment).forEach(category => {
    Object.keys(assessment[category]).forEach(trait => {
      assessment[category][trait] = Math.floor(Math.random() * 100);
    });
  });

  return assessment;
}

/**
 * Simulate single user journey
 */
async function simulateUser(userIndex) {
  const userData = createTestUser(userIndex);
  let token = null;
  
  try {
    // Step 1: Register user (if not exists)
    await registerUser(userData);
    
    // Step 2: Login
    const loginResult = await loginUser(userData);
    token = loginResult.token;
    stats.successfulLogins++;
    
    // Step 3: Submit assessment with delay
    if (CONFIG.ASSESSMENT_DELAY > 0) {
      await new Promise(resolve => setTimeout(resolve, Math.random() * CONFIG.ASSESSMENT_DELAY));
    }
    
    const assessmentData = generateRandomAssessment();
    const submitResult = await submitAssessment(token, userData, assessmentData);
    stats.successfulSubmissions++;
    
    console.log(`✅ User ${userIndex}: Login ${loginResult.responseTime}ms, Submit ${submitResult.responseTime}ms, Queue: ${submitResult.queuePosition}`);
    
    return {
      userIndex,
      success: true,
      loginTime: loginResult.responseTime,
      submitTime: submitResult.responseTime,
      queuePosition: submitResult.queuePosition,
      jobId: submitResult.jobId
    };
    
  } catch (error) {
    if (!token) {
      stats.failedLogins++;
    } else {
      stats.failedSubmissions++;
    }
    
    const errorInfo = {
      userIndex,
      email: userData.email,
      error: error.message || error.toString(),
      responseTime: error.responseTime || 0,
      status: error.response?.status || 'TIMEOUT',
      responseData: error.response?.data || null
    };

    stats.errors.push(errorInfo);
    console.log(`❌ User ${userIndex}: ${errorInfo.error} (${errorInfo.status})`);
    if (errorInfo.responseData) {
      console.log(`   Response: ${JSON.stringify(errorInfo.responseData)}`);
    }
    
    return {
      userIndex,
      success: false,
      error: errorInfo
    };
  }
}

/**
 * Check rate limiting configuration and warn user
 */
function checkRateLimitingConfig() {
  console.log('\n🔍 RATE LIMITING ANALYSIS:');

  if (CONFIG.GEMINI_TIER === 'free') {
    console.log('⚠️  WARNING: Using Gemini API Free Tier');
    console.log('   - Limit: 15 requests/minute, 1,500 requests/day');
    console.log('   - Recommended max concurrent users: 10-15');

    if (CONFIG.CONCURRENT_USERS > 15) {
      console.log('❌ CRITICAL: Too many concurrent users for free tier!');
      console.log(`   Current: ${CONFIG.CONCURRENT_USERS} users`);
      console.log('   Expected result: High failure rate due to rate limits');
      console.log('   Recommendation: Upgrade to paid tier or reduce users to 10');

      const recommendedDelay = Math.ceil((CONFIG.CONCURRENT_USERS / 15) * 60000);
      console.log(`   Alternative: Add ${recommendedDelay}ms delay between submissions`);
    }
  } else if (CONFIG.GEMINI_TIER === 'paid') {
    console.log('✅ Using Gemini API Paid Tier');
    console.log('   - Limit: 1,000 requests/minute');
    console.log('   - Should handle 100 concurrent users');
  }

  // API Gateway rate limits
  console.log('\n📊 API Gateway Rate Limits:');
  console.log('   - General: 200 requests/15min per IP ✅');
  console.log('   - Auth: 200 requests/15min per IP ✅');
  console.log('   - Assessment: 500 requests/hour per user ✅');
}

/**
 * Run load test with concurrent users
 */
async function runLoadTest() {
  console.log(`🚀 Starting load test with ${CONFIG.CONCURRENT_USERS} concurrent users`);
  console.log(`📡 API Gateway: ${CONFIG.API_GATEWAY_URL}`);
  console.log(`🌐 Network simulation: ${CONFIG.NETWORK_SIMULATION.baseIP}${CONFIG.NETWORK_SIMULATION.startIP}-${CONFIG.NETWORK_SIMULATION.endIP}`);
  console.log(`⏱️  Assessment delay: ${CONFIG.ASSESSMENT_DELAY}ms`);
  console.log(`🔑 Gemini API Tier: ${CONFIG.GEMINI_TIER.toUpperCase()}`);

  // Check rate limiting configuration
  checkRateLimitingConfig();

  // Wait for user confirmation if using free tier with too many users
  if (CONFIG.GEMINI_TIER === 'free' && CONFIG.CONCURRENT_USERS > 15) {
    console.log('\n⚠️  Press Ctrl+C to cancel, or any key to continue anyway...');
    // In a real scenario, you'd wait for user input here
    await new Promise(resolve => setTimeout(resolve, 5000));
  }

  console.log('');

  stats.startTime = Date.now();
  stats.totalUsers = CONFIG.CONCURRENT_USERS;

  // Create array of user simulation promises
  const userPromises = [];
  for (let i = 1; i <= CONFIG.CONCURRENT_USERS; i++) {
    userPromises.push(simulateUser(i));
  }

  // Execute all user simulations concurrently
  console.log('⏳ Executing concurrent user simulations...');
  const results = await Promise.allSettled(userPromises);

  stats.endTime = Date.now();

  // Process results
  const successfulResults = results.filter(r => r.status === 'fulfilled' && r.value.success);
  const failedResults = results.filter(r => r.status === 'rejected' || (r.status === 'fulfilled' && !r.value.success));

  // Calculate statistics
  const totalTime = stats.endTime - stats.startTime;
  const avgResponseTime = stats.totalResponseTime / (stats.successfulLogins + stats.successfulSubmissions);
  const throughput = (stats.successfulLogins + stats.successfulSubmissions) / (totalTime / 1000);

  // Print detailed results
  console.log('\n' + '='.repeat(80));
  console.log('📊 LOAD TEST RESULTS');
  console.log('='.repeat(80));

  console.log('\n📈 OVERALL STATISTICS:');
  console.log(`Total Users: ${stats.totalUsers}`);
  console.log(`Total Time: ${(totalTime / 1000).toFixed(2)} seconds`);
  console.log(`Throughput: ${throughput.toFixed(2)} requests/second`);

  console.log('\n🔐 LOGIN STATISTICS:');
  console.log(`Successful Logins: ${stats.successfulLogins}/${stats.totalUsers} (${((stats.successfulLogins/stats.totalUsers)*100).toFixed(1)}%)`);
  console.log(`Failed Logins: ${stats.failedLogins}`);

  console.log('\n📝 ASSESSMENT SUBMISSION STATISTICS:');
  console.log(`Successful Submissions: ${stats.successfulSubmissions}/${stats.successfulLogins} (${stats.successfulLogins > 0 ? ((stats.successfulSubmissions/stats.successfulLogins)*100).toFixed(1) : 0}%)`);
  console.log(`Failed Submissions: ${stats.failedSubmissions}`);

  console.log('\n⏱️  RESPONSE TIME STATISTICS:');
  console.log(`Average Response Time: ${avgResponseTime.toFixed(2)}ms`);
  console.log(`Min Response Time: ${stats.minResponseTime === Infinity ? 'N/A' : stats.minResponseTime + 'ms'}`);
  console.log(`Max Response Time: ${stats.maxResponseTime}ms`);

  // Queue analysis
  if (successfulResults.length > 0) {
    const queuePositions = successfulResults.map(r => r.value.queuePosition).filter(q => q !== undefined);
    if (queuePositions.length > 0) {
      const avgQueuePosition = queuePositions.reduce((a, b) => a + b, 0) / queuePositions.length;
      const maxQueuePosition = Math.max(...queuePositions);

      console.log('\n📋 QUEUE STATISTICS:');
      console.log(`Average Queue Position: ${avgQueuePosition.toFixed(1)}`);
      console.log(`Max Queue Position: ${maxQueuePosition}`);
      console.log(`Queue Buildup: ${maxQueuePosition > CONFIG.CONCURRENT_USERS ? 'YES - Queue is backing up' : 'NO - Queue is handling load'}`);
    }
  }

  // Error analysis
  if (stats.errors.length > 0) {
    console.log('\n❌ ERROR ANALYSIS:');
    const errorsByType = {};
    stats.errors.forEach(error => {
      const key = `${error.status}: ${error.error}`;
      errorsByType[key] = (errorsByType[key] || 0) + 1;
    });

    Object.entries(errorsByType).forEach(([error, count]) => {
      console.log(`  ${error}: ${count} occurrences`);
    });

    console.log('\n🔍 FIRST 5 ERRORS:');
    stats.errors.slice(0, 5).forEach((error, index) => {
      console.log(`  ${index + 1}. User ${error.userIndex} (${error.email}): ${error.error}`);
    });
  }

  // Recommendations
  console.log('\n💡 RECOMMENDATIONS:');

  if (stats.failedLogins > stats.totalUsers * 0.1) {
    console.log('  ⚠️  High login failure rate - Check auth service capacity');
  }

  if (stats.failedSubmissions > stats.successfulLogins * 0.1) {
    console.log('  ⚠️  High submission failure rate - Check assessment service capacity');

    // Check if it might be rate limiting
    if (CONFIG.GEMINI_TIER === 'free' && CONFIG.CONCURRENT_USERS > 15) {
      console.log('     🔑 Likely cause: Gemini API free tier rate limits');
      console.log('     💡 Solution: Upgrade to paid tier or reduce concurrent users');
    }
  }

  if (avgResponseTime > 5000) {
    console.log('  ⚠️  High response times - Consider scaling services');
  }

  if (successfulResults.length > 0) {
    const maxQueue = Math.max(...successfulResults.map(r => r.value.queuePosition || 0));
    if (maxQueue > CONFIG.CONCURRENT_USERS) {
      console.log('  ⚠️  Queue backing up - Increase analysis worker instances');
      console.log(`     Recommended workers: ${Math.ceil(maxQueue / 10)} instances`);
    }
  }

  if (throughput < 10) {
    console.log('  ⚠️  Low throughput - Check database connection pools');
  }

  // Rate limiting specific recommendations
  if (CONFIG.GEMINI_TIER === 'free') {
    console.log('  🔑 Gemini API Free Tier Recommendations:');
    console.log('     - Upgrade to paid tier for production load testing');
    console.log('     - Current free tier: 15 requests/minute, 1,500/day');
    console.log('     - Paid tier: 1,000 requests/minute, unlimited daily');
    console.log('     - Cost estimate: ~$1.65 per assessment');
  }

  console.log('\n' + '='.repeat(80));

  return {
    stats,
    results: successfulResults.map(r => r.value),
    errors: failedResults.map(r => r.reason || r.value.error)
  };
}

/**
 * Main execution
 */
async function main() {
  try {
    const results = await runLoadTest();

    // Save results to file
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const resultsFile = `load-test-results-${timestamp}.json`;

    require('fs').writeFileSync(resultsFile, JSON.stringify({
      config: CONFIG,
      timestamp: new Date().toISOString(),
      ...results
    }, null, 2));

    console.log(`\n💾 Results saved to: ${resultsFile}`);

    // Exit with appropriate code
    process.exit(stats.errors.length > 0 ? 1 : 0);

  } catch (error) {
    console.error('❌ Load test failed:', error.message);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n⏹️  Load test interrupted by user');
  process.exit(1);
});

process.on('SIGTERM', () => {
  console.log('\n⏹️  Load test terminated');
  process.exit(1);
});

// Run the load test
if (require.main === module) {
  main();
}

module.exports = {
  runLoadTest,
  simulateUser,
  CONFIG,
  stats
};
