{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-15 17:24:36"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:24:36"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-15 17:26:05"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:26:05"}
{"error":"password authentication failed for user \"postgres\"","level":"error","message":"Health check failed","service":"auth-service","status":"unhealthy","timestamp":"2025-07-15 17:28:18"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-15 17:28:50"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:28:50"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:31:26"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-15 17:31:57"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:31:57"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-15 17:32:27"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:32:28"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:32:46"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:32:46"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-15 17:32:58"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:32:58"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:33:26"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:33:26"}
{"email":"<EMAIL>","error":"password authentication failed for user \"postgres\"","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 17:33:27"}
{"error":"password authentication failed for user \"postgres\"","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"3bd0a84f-2c03-4720-8c3b-f53fc6426413","service":"auth-service","stack":"SequelizeConnectionError: password authentication failed for user \"postgres\"\n    at Client._connectionCallback (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\connection-manager.js:145:24)\n    at Client._handleErrorWhileConnecting (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\pg\\lib\\client.js:336:19)\n    at Client._handleErrorMessage (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\pg\\lib\\client.js:356:19)\n    at Connection.emit (node:events:518:28)\n    at D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\pg\\lib\\connection.js:116:12\n    at Parser.parse (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\pg-protocol\\dist\\parser.js:36:17)\n    at Socket.<anonymous> (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\pg-protocol\\dist\\index.js:11:42)\n    at Socket.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)","timestamp":"2025-07-15 17:33:27","userAgent":"axios/1.10.0"}
{"errors":["Email must be a valid email address","Password must be at least 8 characters long","Password must contain at least one letter and one number"],"level":"warn","message":"Request validation failed","method":"POST","path":"/register","service":"auth-service","timestamp":"2025-07-15 17:33:27"}
{"error":"jwt malformed","level":"warn","message":"JWT token verification failed","service":"auth-service","timestamp":"2025-07-15 17:33:27","tokenType":"JsonWebTokenError"}
{"error":"Invalid token format","ip":"::1","level":"warn","message":"Token verification failed","service":"auth-service","timestamp":"2025-07-15 17:33:27"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-15 17:34:43"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:34:43"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-15 18:47:58"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 18:47:58"}
{"level":"info","message":"SIGINT received, shutting down gracefully","service":"auth-service","timestamp":"2025-07-15 18:48:02"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-15 19:02:15"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-15 19:02:16"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-15 19:03:52"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-15 19:03:52"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-16 03:55:31"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 03:55:31"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-16 03:55:57"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 03:55:57"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 03:57:28"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-16 03:57:28","tokenBalance":3,"userId":"eaa2cb86-2f6e-453a-82ba-385ef0a906a0"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-16 03:57:28","userId":"eaa2cb86-2f6e-453a-82ba-385ef0a906a0"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 03:57:29","tokenBalance":3,"userId":"eaa2cb86-2f6e-453a-82ba-385ef0a906a0"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 03:57:29","userId":"eaa2cb86-2f6e-453a-82ba-385ef0a906a0"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-15 22:26:57"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-15 22:26:57"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-15 22:31:59"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-15 22:31:59"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-15 22:32:33","tokenBalance":5,"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-15 22:32:33","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 22:32:33","tokenBalance":5,"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 22:32:33","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:08:19"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"b5a42651-a7c9-452a-bc44-f3254f28bbec","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:08:19","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:08:41"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"cd5dc5d1-b2c6-41b0-9714-ab5590cac3fa","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:08:41","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:08:41","tokenBalance":5,"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:08:41","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:10:34"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"e6432726-eb9a-4f2c-b73b-fb407665aa5c","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:10:34","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:10:34","tokenBalance":5,"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:10:34","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:11:17"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"bbebde79-15d3-4866-9c4f-f6d844bce977","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:11:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:11:17","tokenBalance":5,"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:11:17","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:13:02"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"91f4529c-1757-4be0-bbaf-aa837095bd42","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:13:02","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:13:02","tokenBalance":5,"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:13:02","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"ip":"::ffff:**********","level":"warn","message":"Internal service authentication failed: Invalid service key","service":"auth-service","timestamp":"2025-07-15 23:13:02","url":"/auth/token-balance"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:13:51"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"92ca9ade-1823-4b90-9b3d-9dca38f46107","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:13:51","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:13:51","tokenBalance":5,"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:13:51","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"ip":"::ffff:**********","level":"warn","message":"Internal service authentication failed: Invalid service key","service":"auth-service","timestamp":"2025-07-15 23:13:51","url":"/auth/token-balance"}
{"level":"info","message":"SIGTERM received, shutting down gracefully","service":"auth-service","timestamp":"2025-07-15 23:14:36"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-15 23:14:38"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-15 23:14:38"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:14:45"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"0aeb1e9d-7a9b-442b-8295-502a04eaa0fd","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:14:45","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:14:45","tokenBalance":5,"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:14:45","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"ip":"::ffff:**********","level":"warn","message":"Internal service authentication failed: Invalid service key","service":"auth-service","timestamp":"2025-07-15 23:14:45","url":"/auth/token-balance"}
{"level":"info","message":"SIGTERM received, shutting down gracefully","service":"auth-service","timestamp":"2025-07-15 23:15:56"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-15 23:15:58"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-15 23:15:58"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:16:17"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"c9239e33-6180-4569-8e83-7a9ec1c2b6f9","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:16:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:16:17","tokenBalance":5,"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:16:17","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":4,"oldBalance":4,"operation":"subtract","service":"auth-service","timestamp":"2025-07-15 23:16:17","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"amount":1,"ip":"::ffff:**********","level":"info","message":"Token balance updated via internal service","newBalance":4,"operation":"subtract","service":"auth-service","timestamp":"2025-07-15 23:16:17","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:20:58"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"d0a44d01-d8de-491a-9b62-55017c0da149","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:20:58","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:20:59","tokenBalance":4,"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:20:59","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":3,"oldBalance":3,"operation":"subtract","service":"auth-service","timestamp":"2025-07-15 23:20:59","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"amount":1,"ip":"::ffff:**********","level":"info","message":"Token balance updated via internal service","newBalance":3,"operation":"subtract","service":"auth-service","timestamp":"2025-07-15 23:20:59","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:27:52"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"612c9f01-812d-4b57-b089-5e71452dd96a","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:27:52","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:27:53","tokenBalance":3,"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:27:53","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-15 23:27:53","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"amount":1,"ip":"::ffff:**********","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-15 23:27:53","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"level":"info","message":"SIGTERM received, shutting down gracefully","service":"auth-service","timestamp":"2025-07-15 23:31:43"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-15 23:32:38"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-15 23:32:38"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-15 23:32:49","tokenBalance":5,"userId":"69686ed9-2ada-4641-ae9e-81747647701d"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-15 23:32:49","userId":"69686ed9-2ada-4641-ae9e-81747647701d"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:32:49","tokenBalance":5,"userId":"69686ed9-2ada-4641-ae9e-81747647701d"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:32:49","userId":"69686ed9-2ada-4641-ae9e-81747647701d"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:34:09"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"192d655f-d6fa-4cb1-a07f-8cf98cb6ec5b","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:34:09","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:34:09","tokenBalance":5,"userId":"69686ed9-2ada-4641-ae9e-81747647701d"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:34:09","userId":"69686ed9-2ada-4641-ae9e-81747647701d"}
{"environment":"test","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-16 07:52:06"}
{"errors":["Password is required"],"level":"warn","message":"Request validation failed","method":"POST","path":"/register","service":"auth-service","timestamp":"2025-07-16 07:52:06"}
{"level":"info","message":"::ffff:127.0.0.1 - - [16/Jul/2025:00:52:06 +0000] \"POST /auth/register HTTP/1.1\" 400 118 \"-\" \"-\"","service":"auth-service","timestamp":"2025-07-16 07:52:06"}
{"level":"info","message":"::ffff:127.0.0.1 - - [16/Jul/2025:00:52:06 +0000] \"GET /auth/profile HTTP/1.1\" 200 117 \"-\" \"-\"","service":"auth-service","timestamp":"2025-07-16 07:52:06"}
{"ip":"::ffff:127.0.0.1","level":"warn","message":"Authentication failed: No token provided","service":"auth-service","timestamp":"2025-07-16 07:52:06","url":"/auth/profile"}
{"level":"info","message":"::ffff:127.0.0.1 - - [16/Jul/2025:00:52:06 +0000] \"GET /auth/profile HTTP/1.1\" 401 86 \"-\" \"-\"","service":"auth-service","timestamp":"2025-07-16 07:52:06"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 07:52:06"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 07:52:06"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 07:52:06"}
{"environment":"test","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-16 07:56:26"}
{"email":"<EMAIL>","ip":"::ffff:127.0.0.1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-16 07:56:26","userId":"user-123"}
{"level":"info","message":"::ffff:127.0.0.1 - - [16/Jul/2025:00:56:26 +0000] \"POST /auth/register HTTP/1.1\" 201 184 \"-\" \"-\"","service":"auth-service","timestamp":"2025-07-16 07:56:26"}
{"error":"Email already exists","ip":"::ffff:127.0.0.1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"fe9ffc0e-5be5-430a-ae54-b439a18f2e55","service":"auth-service","stack":"Error: Email already exists\n    at Object.<anonymous> (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\tests\\auth.test.js:97:50)\n    at Promise.then.completed (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-runner\\build\\runTest.js:444:34)","timestamp":"2025-07-16 07:56:26"}
{"level":"info","message":"::ffff:127.0.0.1 - - [16/Jul/2025:00:56:26 +0000] \"POST /auth/register HTTP/1.1\" 400 82 \"-\" \"-\"","service":"auth-service","timestamp":"2025-07-16 07:56:26"}
{"errors":["Password is required"],"level":"warn","message":"Request validation failed","method":"POST","path":"/register","service":"auth-service","timestamp":"2025-07-16 07:56:26"}
{"level":"info","message":"::ffff:127.0.0.1 - - [16/Jul/2025:00:56:26 +0000] \"POST /auth/register HTTP/1.1\" 400 118 \"-\" \"-\"","service":"auth-service","timestamp":"2025-07-16 07:56:26"}
{"email":"<EMAIL>","ip":"::ffff:127.0.0.1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 07:56:26","userId":"user-123"}
{"level":"info","message":"::ffff:127.0.0.1 - - [16/Jul/2025:00:56:26 +0000] \"POST /auth/login HTTP/1.1\" 200 172 \"-\" \"-\"","service":"auth-service","timestamp":"2025-07-16 07:56:26"}
{"error":"Invalid email or password","ip":"::ffff:127.0.0.1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"5c6d045a-d3a4-4d2e-9715-efa3d18df1e2","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.<anonymous> (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\tests\\auth.test.js:177:47)\n    at Promise.then.completed (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-runner\\build\\runTest.js:444:34)","timestamp":"2025-07-16 07:56:26"}
{"level":"info","message":"::ffff:127.0.0.1 - - [16/Jul/2025:00:56:26 +0000] \"POST /auth/login HTTP/1.1\" 401 94 \"-\" \"-\"","service":"auth-service","timestamp":"2025-07-16 07:56:26"}
{"error":"Cannot read properties of undefined (reading 'id')","ip":"::ffff:127.0.0.1","level":"warn","message":"Authentication failed","service":"auth-service","timestamp":"2025-07-16 07:56:26","url":"/auth/profile"}
{"level":"info","message":"::ffff:127.0.0.1 - - [16/Jul/2025:00:56:26 +0000] \"GET /auth/profile HTTP/1.1\" 401 86 \"-\" \"-\"","service":"auth-service","timestamp":"2025-07-16 07:56:26"}
{"ip":"::ffff:127.0.0.1","level":"warn","message":"Authentication failed: No token provided","service":"auth-service","timestamp":"2025-07-16 07:56:26","url":"/auth/profile"}
{"level":"info","message":"::ffff:127.0.0.1 - - [16/Jul/2025:00:56:26 +0000] \"GET /auth/profile HTTP/1.1\" 401 86 \"-\" \"-\"","service":"auth-service","timestamp":"2025-07-16 07:56:26"}
{"errors":["\"userId\" must be a valid GUID","Amount is required","Operation is required"],"level":"warn","message":"Request validation failed","method":"PUT","path":"/token-balance","service":"auth-service","timestamp":"2025-07-16 07:56:26"}
{"level":"info","message":"::ffff:127.0.0.1 - - [16/Jul/2025:00:56:26 +0000] \"PUT /auth/token-balance HTTP/1.1\" 400 174 \"-\" \"-\"","service":"auth-service","timestamp":"2025-07-16 07:56:26"}
{"ip":"::ffff:127.0.0.1","level":"warn","message":"Internal service authentication failed: Missing internal service header","service":"auth-service","timestamp":"2025-07-16 07:56:26","url":"/auth/token-balance"}
{"level":"info","message":"::ffff:127.0.0.1 - - [16/Jul/2025:00:56:26 +0000] \"PUT /auth/token-balance HTTP/1.1\" 401 94 \"-\" \"-\"","service":"auth-service","timestamp":"2025-07-16 07:56:26"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 07:56:26"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-16 08:17:59"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-16 08:19:48"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 08:19:48"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-16 08:21:05","tokenBalance":3,"userId":"209181db-f300-4301-a955-139ca333b703"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-16 08:21:05","userId":"209181db-f300-4301-a955-139ca333b703"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 08:21:05","tokenBalance":3,"userId":"209181db-f300-4301-a955-139ca333b703"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 08:21:05","userId":"209181db-f300-4301-a955-139ca333b703"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-16 08:40:15","tokenBalance":3,"userId":"77596f49-a72e-449f-ba71-e40da23d09c4"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-16 08:40:15","userId":"77596f49-a72e-449f-ba71-e40da23d09c4"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-16 13:16:18"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 13:16:18"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-16 13:18:04"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 13:18:04"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-16 13:18:47","tokenBalance":3,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-16 13:18:47","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 13:19:05","tokenBalance":3,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 13:19:05","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 13:19:12","tokenBalance":3,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 13:19:12","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-16 13:20:59"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 13:20:59"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-16 13:26:05"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 13:26:05"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-16 14:41:41"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 14:41:41"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 14:43:01"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 14:43:01"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 14:43:01"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 14:43:01"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 14:43:08"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 14:43:08"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-16 15:05:24"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 15:05:24"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 15:20:30"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 15:20:30"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 15:20:30"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 15:20:30"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 15:20:52"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 15:20:52"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 15:20:55"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 15:20:55"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 15:20:56"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 15:20:56"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 15:20:56"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 15:20:56"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 15:20:56"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 15:20:56"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-16 15:58:03","tokenBalance":3,"userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-16 15:58:03","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-16 15:58:15"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"6720fadf-736c-430b-8e35-f0e466e5cf9e","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-16 15:58:15","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 15:58:27","tokenBalance":3,"userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 15:58:27","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 15:58:31","tokenBalance":3,"userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 15:58:31","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 16:15:14"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 16:15:14"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 16:15:14"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 16:15:14"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 16:15:14"}
{"email":"<EMAIL>","error":"Invalid email or password","level":"error","message":"User login failed","service":"auth-service","timestamp":"2025-07-16 16:15:17"}
{"error":"Invalid email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"84d3593c-9cca-4f53-b7b5-6520b0290890","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.loginUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:71:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:46:20)","timestamp":"2025-07-16 16:15:17","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-16 16:15:30","tokenBalance":3,"userId":"b0f4c98f-f14c-4364-8d5f-c53f0f2acf1e"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-16 16:15:30","userId":"b0f4c98f-f14c-4364-8d5f-c53f0f2acf1e"}
{"email":"<EMAIL>","error":"Invalid email or password","level":"error","message":"User login failed","service":"auth-service","timestamp":"2025-07-16 16:15:50"}
{"error":"Invalid email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"3dcace34-f05e-4a7e-996b-8fa371059b56","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.loginUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:71:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:46:20)","timestamp":"2025-07-16 16:15:50","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"email":"<EMAIL>","error":"Invalid email or password","level":"error","message":"User login failed","service":"auth-service","timestamp":"2025-07-16 16:15:53"}
{"error":"Invalid email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"23247aa0-b791-40cc-a778-ee9c583f69f7","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.loginUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:71:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:46:20)","timestamp":"2025-07-16 16:15:53","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"email":"<EMAIL>","error":"Invalid email or password","level":"error","message":"User login failed","service":"auth-service","timestamp":"2025-07-16 16:18:32"}
{"error":"Invalid email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"bfaaaab6-c7ae-45d7-845b-31d2a4514395","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.loginUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:71:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:46:20)","timestamp":"2025-07-16 16:18:32","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"email":"<EMAIL>","error":"Invalid email or password","level":"error","message":"User login failed","service":"auth-service","timestamp":"2025-07-16 16:18:36"}
{"error":"Invalid email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"2040f2b4-99c4-42f0-9f0d-aedd02646eba","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.loginUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:71:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:46:20)","timestamp":"2025-07-16 16:18:36","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"email":"<EMAIL>","error":"Invalid email or password","level":"error","message":"User login failed","service":"auth-service","timestamp":"2025-07-16 16:18:49"}
{"error":"Invalid email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"ee122e38-7816-40f7-a693-8e3d47486bf4","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.loginUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:71:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:46:20)","timestamp":"2025-07-16 16:18:49","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 16:24:42"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 16:24:42"}
{"email":"<EMAIL>","error":"Invalid email or password","level":"error","message":"User login failed","service":"auth-service","timestamp":"2025-07-16 16:24:43"}
{"error":"Invalid email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"e3c3b757-d658-4824-a6d6-5f02c0d3ec4f","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.loginUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:71:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:46:20)","timestamp":"2025-07-16 16:24:43","userAgent":"axios/1.10.0"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 16:25:24"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 16:25:24"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 16:25:24","tokenBalance":3,"userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 16:25:24","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 16:28:11"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 16:28:11"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 16:28:12","tokenBalance":3,"userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 16:28:12","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 16:30:43"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 16:30:43"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 16:30:43","tokenBalance":3,"userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 16:30:43","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-16 16:30:44","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-16 16:30:44","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 16:33:50","tokenBalance":2,"userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 16:33:50","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-16 16:33:51","tokenBalance":3,"userId":"3036cd79-b8bb-427c-8015-dda9b4cbf7f8"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-16 16:33:51","userId":"3036cd79-b8bb-427c-8015-dda9b4cbf7f8"}
{"email":"<EMAIL>","level":"info","message":"User profile updated successfully","service":"auth-service","timestamp":"2025-07-16 16:33:53","updatedFields":[],"userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User profile updated","service":"auth-service","timestamp":"2025-07-16 16:33:53","updatedFields":[],"userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":1,"oldBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-16 16:33:55","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-16 16:33:55","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 16:34:02"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 16:34:02"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 16:34:02"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 16:34:02"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 16:34:02"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 18:58:42","tokenBalance":1,"userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 18:58:42","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-16 18:58:44","tokenBalance":3,"userId":"0f50959d-c221-4360-97f4-7a45c8c19678"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-16 18:58:44","userId":"0f50959d-c221-4360-97f4-7a45c8c19678"}
{"email":"<EMAIL>","level":"info","message":"User profile updated successfully","service":"auth-service","timestamp":"2025-07-16 18:58:46","updatedFields":[],"userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User profile updated","service":"auth-service","timestamp":"2025-07-16 18:58:46","updatedFields":[],"userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":0,"oldBalance":0,"operation":"subtract","service":"auth-service","timestamp":"2025-07-16 18:58:48","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":0,"operation":"subtract","service":"auth-service","timestamp":"2025-07-16 18:58:48","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 18:58:54"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 18:58:54"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 18:58:54"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 18:58:54"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 18:58:54"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-16 23:26:06"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 23:26:06"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-16 23:26:46"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 23:26:46"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-17 05:44:46"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-17 05:44:46"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-17 05:45:54","tokenBalance":3,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-17 05:45:54","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-17 06:00:12"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-17 06:00:13"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-17 06:00:13"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-17 06:00:13"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-17 06:00:14"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-17 06:00:14"}
{"email":"<EMAIL>","error":"Invalid email or password","level":"error","message":"User login failed","service":"auth-service","timestamp":"2025-07-17 06:00:23"}
{"error":"Invalid email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"0404b631-1e62-4c16-87ce-5ee22f30db72","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.loginUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:71:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:46:20)","timestamp":"2025-07-17 06:00:23","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-17 06:00:33","tokenBalance":3,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-17 06:00:33","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-17 06:00:41","tokenBalance":3,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-17 06:00:41","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"ip":"::1","level":"info","message":"User logged out","service":"auth-service","timestamp":"2025-07-17 07:42:16","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-17 07:42:22","tokenBalance":3,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-17 07:42:22","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 08:01:55","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 08:01:55","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-17 08:31:32"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-17 08:31:33"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-17 10:04:33"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-17 10:04:33"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-17 10:42:59"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-17 10:42:59"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-17 11:12:34"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-17 11:12:35"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-17 11:40:23"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-17 11:40:23"}
{"ip":"::1","level":"info","message":"User logged out","service":"auth-service","timestamp":"2025-07-17 11:40:36","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-17 11:41:15","tokenBalance":3,"userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-17 11:41:15","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-17 12:05:08"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-17 12:05:08"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 12:13:16","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 12:13:16","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-17 12:22:14"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-17 12:22:14"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":1,"oldBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 12:22:48","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 12:22:48","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":0,"oldBalance":0,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 12:31:22","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":0,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 12:31:22","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-17 18:15:30"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-17 18:15:30"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-17 18:21:48","tokenBalance":3,"userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-17 18:21:48","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 18:23:12","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 18:23:12","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":1,"oldBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 18:26:39","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 18:26:39","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"ip":"::1","level":"info","message":"User logged out","service":"auth-service","timestamp":"2025-07-17 19:31:12","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-17 19:31:23","tokenBalance":1,"userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-17 19:31:23","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":0,"oldBalance":0,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 19:31:29","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":0,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 19:31:29","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"ip":"::1","level":"info","message":"User logged out","service":"auth-service","timestamp":"2025-07-17 19:32:44","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-17 19:33:23","tokenBalance":3,"userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-17 19:33:23","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 19:34:32","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 19:34:32","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":1,"oldBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 20:27:31","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 20:27:31","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":0,"oldBalance":0,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 20:28:33","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":0,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 20:28:33","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"ip":"::1","level":"info","message":"User logged out","service":"auth-service","timestamp":"2025-07-17 20:32:38","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-17 20:32:56","tokenBalance":3,"userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-17 20:32:56","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 20:33:46","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 20:33:46","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-17 20:36:15"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-17 20:36:15"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":1,"oldBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 20:38:31","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 20:38:31","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-17 20:46:28"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-17 20:46:29"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":0,"oldBalance":0,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 20:46:59","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":0,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 20:46:59","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"ip":"::1","level":"info","message":"User logged out","service":"auth-service","timestamp":"2025-07-17 20:47:42","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-17 20:55:52"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-17 20:55:52"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-17 20:56:31","tokenBalance":0,"userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-17 20:56:31","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"ip":"::1","level":"info","message":"User logged out","service":"auth-service","timestamp":"2025-07-17 20:57:18","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-17 20:57:40","tokenBalance":3,"userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-17 20:57:40","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 20:58:39","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 20:58:39","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":1,"oldBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 21:06:48","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 21:06:48","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":0,"oldBalance":0,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 21:07:18","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":0,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 21:07:18","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-18 03:53:12"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-18 03:53:13"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 03:53:27","tokenBalance":0,"userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 03:53:27","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"ip":"::1","level":"info","message":"User logged out","service":"auth-service","timestamp":"2025-07-18 03:53:30","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 03:53:42","tokenBalance":3,"userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 03:53:42","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 03:53:50","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 03:53:50","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":1,"oldBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 03:55:38","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 03:55:38","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":0,"oldBalance":0,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 03:56:19","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":0,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 03:56:19","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"ip":"::1","level":"info","message":"User logged out","service":"auth-service","timestamp":"2025-07-18 04:23:13","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 04:23:17","tokenBalance":0,"userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 04:23:17","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"ip":"::1","level":"info","message":"User logged out","service":"auth-service","timestamp":"2025-07-18 04:23:27","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-18 05:34:50"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-18 05:34:50"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-18 05:35:15"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-18 05:35:15"}
{"error":"Invalid value { username: 'superadmin' }","level":"error","message":"Admin login failed","service":"auth-service","timestamp":"2025-07-18 05:35:39","username":"superadmin"}
{"error":"Invalid value { username: 'superadmin' }","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/admin/login","requestId":"59f20432-3a02-4c8e-9e89-53b9d9dd2f13","service":"auth-service","stack":"Error: Invalid value { username: 'superadmin' }\n    at Object.escape (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\sql-string.js:54:11)\n    at PostgresQueryGenerator.escape (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:766:22)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:1996:71\n    at Array.map (<anonymous>)\n    at PostgresQueryGenerator._whereParseSingleValueObject (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:1996:52)\n    at PostgresQueryGenerator.whereItemQuery (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:1812:19)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:1737:25\n    at Array.forEach (<anonymous>)\n    at PostgresQueryGenerator.whereItemsQuery (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:1735:35)\n    at PostgresQueryGenerator.getWhereConditions (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:2078:19)","timestamp":"2025-07-18 05:35:39","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Invalid value { username: 'superadmin' }","level":"error","message":"Admin login failed","service":"auth-service","timestamp":"2025-07-18 05:36:04","username":"superadmin"}
{"error":"Invalid value { username: 'superadmin' }","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/admin/login","requestId":"e1bf6768-182f-4b28-a069-e388f3e25203","service":"auth-service","stack":"Error: Invalid value { username: 'superadmin' }\n    at Object.escape (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\sql-string.js:54:11)\n    at PostgresQueryGenerator.escape (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:766:22)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:1996:71\n    at Array.map (<anonymous>)\n    at PostgresQueryGenerator._whereParseSingleValueObject (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:1996:52)\n    at PostgresQueryGenerator.whereItemQuery (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:1812:19)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:1737:25\n    at Array.forEach (<anonymous>)\n    at PostgresQueryGenerator.whereItemsQuery (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:1735:35)\n    at PostgresQueryGenerator.getWhereConditions (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:2078:19)","timestamp":"2025-07-18 05:36:04","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-18 05:38:45"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-18 05:38:45"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-18 05:39:45"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-18 05:39:45"}
{"error":"Invalid username/email or password","level":"error","message":"Admin login failed","service":"auth-service","timestamp":"2025-07-18 05:39:59","username":"superadmin"}
{"error":"Invalid username/email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/admin/login","requestId":"4953c00f-1f82-4b2d-95ac-4856292b29e1","service":"auth-service","stack":"Error: Invalid username/email or password\n    at Object.loginAdmin (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\adminService.js:126:13)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\adminController.js:65:20)","timestamp":"2025-07-18 05:39:59","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-18 05:46:36"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-18 05:46:57"}
{"error":"request aborted","level":"error","message":"Unhandled error occurred","method":"POST","path":"/admin/login","service":"auth-service","stack":"BadRequestError: request aborted\n    at IncomingMessage.onAborted (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\raw-body\\index.js:245:10)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage._destroy (node:_http_incoming:221:10)\n    at _destroy (node:internal/streams/destroy:122:10)\n    at IncomingMessage.destroy (node:internal/streams/destroy:84:5)\n    at abortIncoming (node:_http_server:811:9)\n    at socketOnClose (node:_http_server:805:3)\n    at Socket.emit (node:events:530:35)\n    at TCP.<anonymous> (node:net:346:12)","timestamp":"2025-07-18 05:47:41","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-18 05:47:41"}
{"error":"Invalid username/email or password","level":"error","message":"Admin login failed","service":"auth-service","timestamp":"2025-07-18 05:47:48","username":"superadmin"}
{"error":"Invalid username/email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/admin/login","requestId":"3a1e19ea-5604-462e-96fe-c51aed2ca653","service":"auth-service","stack":"Error: Invalid username/email or password\n    at Object.loginAdmin (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\adminService.js:126:13)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\adminController.js:65:20)","timestamp":"2025-07-18 05:47:48","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-18 05:48:36"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-18 05:48:36"}
{"error":"Invalid username/email or password","level":"error","message":"Admin login failed","service":"auth-service","timestamp":"2025-07-18 05:49:10","username":"superadmin"}
{"error":"Invalid username/email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/admin/login","requestId":"0ef7e33c-665f-473e-b697-e6ec0fed6167","service":"auth-service","stack":"Error: Invalid username/email or password\n    at Object.loginAdmin (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\adminService.js:126:13)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\adminController.js:65:20)","timestamp":"2025-07-18 05:49:10","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-18 05:52:32"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-18 05:52:54"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 05:53:19","username":"superadmin"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","ip":"::1","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 05:53:19","username":"superadmin"}
{"error":"Invalid username/email or password","level":"error","message":"Admin login failed","service":"auth-service","timestamp":"2025-07-18 05:53:45","username":"superadmin"}
{"error":"Invalid username/email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/admin/login","requestId":"a0a820b1-effb-426f-8500-c5d8de9a0f1f","service":"auth-service","stack":"Error: Invalid username/email or password\n    at Object.loginAdmin (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\adminService.js:126:13)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\adminController.js:65:20)","timestamp":"2025-07-18 05:53:45","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 05:54:13","username":"superadmin"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","ip":"::1","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 05:54:13","username":"superadmin"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-18 05:55:25"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-18 05:55:25"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 05:58:52","username":"superadmin"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","ip":"::1","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 05:58:52","username":"superadmin"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 05:59:27","username":"superadmin"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","ip":"::1","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 05:59:27","username":"superadmin"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 06:00:33","username":"superadmin"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","ip":"::1","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 06:00:33","username":"superadmin"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 06:07:20","username":"superadmin"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","ip":"::1","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 06:07:20","username":"superadmin"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-18 06:17:30"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-18 06:17:31"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 06:17:54","tokenBalance":0,"userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 06:17:54","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 06:18:04","username":"superadmin"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","ip":"::1","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 06:18:04","username":"superadmin"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","ip":"::1","level":"info","message":"Admin logged out","service":"auth-service","timestamp":"2025-07-18 06:18:04","username":"superadmin"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","ip":"::1","level":"info","message":"Admin logged out","service":"auth-service","timestamp":"2025-07-18 06:18:04","username":"superadmin"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-18 06:18:33"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-18 06:18:33"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-18 06:21:50"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-18 06:21:50"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 06:25:17","username":"superadmin"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","ip":"::1","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 06:25:17","username":"superadmin"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 06:43:22","username":"superadmin"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","ip":"::1","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 06:43:22","username":"superadmin"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 07:03:51","username":"superadmin"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","ip":"::1","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 07:03:51","username":"superadmin"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 07:04:00","username":"superadmin"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","ip":"::1","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 07:04:00","username":"superadmin"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 07:06:26","username":"superadmin"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","ip":"::1","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 07:06:26","username":"superadmin"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 07:06:34","username":"superadmin"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","ip":"::1","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 07:06:34","username":"superadmin"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 07:20:28","tokenBalance":20,"userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 07:20:28","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":19,"oldBalance":19,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 07:20:31","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":19,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 07:20:31","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":18,"oldBalance":18,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 07:29:40","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":18,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 07:29:40","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":17,"oldBalance":17,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 07:34:16","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":17,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 07:34:16","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":16,"oldBalance":16,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 07:40:10","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":16,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 07:40:10","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-18 08:02:36"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-18 08:02:36"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-18 08:10:37"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-18 08:10:37"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-18 08:21:02"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-18 08:21:02"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":15,"oldBalance":15,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 08:21:48","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":15,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 08:21:48","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":14,"oldBalance":14,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 08:22:13","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":14,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 08:22:13","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":13,"oldBalance":13,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 08:23:31","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":13,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 08:23:31","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":12,"oldBalance":12,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 08:24:00","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":12,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 08:24:00","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":11,"oldBalance":11,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 08:32:37","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":11,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 08:32:37","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":10,"oldBalance":10,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 08:38:34","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":10,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 08:38:34","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"ip":"::1","level":"info","message":"User logged out","service":"auth-service","timestamp":"2025-07-18 08:40:32","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 08:40:34","tokenBalance":10,"userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 08:40:34","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"ip":"::1","level":"info","message":"User logged out","service":"auth-service","timestamp":"2025-07-18 08:46:07","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 08:46:09","tokenBalance":10,"userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 08:46:09","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-18 08:46:35"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-18 08:46:35"}
{"ip":"::1","level":"info","message":"User logged out","service":"auth-service","timestamp":"2025-07-18 08:46:40","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 08:46:42","tokenBalance":10,"userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 08:46:42","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":9,"oldBalance":9,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 08:52:55","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":9,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 08:52:55","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":8,"oldBalance":8,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 08:55:30","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":8,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 08:55:30","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"ip":"::1","level":"info","message":"User logged out","service":"auth-service","timestamp":"2025-07-18 08:57:46","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 08:57:50","tokenBalance":0,"userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 08:57:50","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"ip":"::1","level":"info","message":"User logged out","service":"auth-service","timestamp":"2025-07-18 08:58:26","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 08:58:29","tokenBalance":8,"userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 08:58:29","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":7,"oldBalance":7,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 08:58:33","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":7,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 08:58:33","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"ip":"::1","level":"info","message":"User logged out","service":"auth-service","timestamp":"2025-07-18 09:00:12","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 09:00:14","tokenBalance":7,"userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 09:00:14","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"ip":"::1","level":"info","message":"User logged out","service":"auth-service","timestamp":"2025-07-18 09:03:01","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-18 09:06:32"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-18 09:06:32"}
{"email":"<EMAIL>","error":"Invalid email or password","level":"error","message":"User login failed","service":"auth-service","timestamp":"2025-07-18 09:10:06"}
{"error":"Invalid email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"ee296169-91ad-432f-a923-5dfe32a0b4e4","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.loginUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:77:13)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:46:20)","timestamp":"2025-07-18 09:10:06","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 09:10:31"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"054cbcfe-2f00-45fd-a072-3694fadd06b2","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 09:10:31","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Invalid email or password","level":"error","message":"User login failed","service":"auth-service","timestamp":"2025-07-18 09:10:31"}
{"error":"Invalid email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"62bd736b-2f4f-4a19-b153-e01e1ba0fd28","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.loginUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:77:13)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:46:20)","timestamp":"2025-07-18 09:10:31","userAgent":"axios/1.10.0"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-18 10:09:53"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-18 10:09:53"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-18 10:12:20"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-18 10:12:20"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-18 10:15:24"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-18 10:15:24"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-18 10:17:51"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-18 10:17:51"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-18 10:18:39"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-18 10:18:39"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-18 10:24:49"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-18 10:24:49"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-18 12:40:53"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-18 12:40:53"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:03","tokenBalance":3,"userId":"da6e7756-6da7-4762-9478-15989c524ba5"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:03","userId":"da6e7756-6da7-4762-9478-15989c524ba5"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:03","tokenBalance":3,"userId":"ff453c60-d4cc-4a3b-895b-58aea0d76994"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:03","userId":"ff453c60-d4cc-4a3b-895b-58aea0d76994"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:03","tokenBalance":3,"userId":"842827f5-e6a0-464c-adee-d849b9ffb009"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:03","userId":"842827f5-e6a0-464c-adee-d849b9ffb009"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:03","tokenBalance":3,"userId":"b41051d1-550d-4d43-a899-ff6731e904ef"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:03","userId":"b41051d1-550d-4d43-a899-ff6731e904ef"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:03","tokenBalance":3,"userId":"15902e84-9592-4b67-b3c2-1ec79e1dd54d"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:03","userId":"15902e84-9592-4b67-b3c2-1ec79e1dd54d"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:03","tokenBalance":3,"userId":"a95c638e-a5c5-4d5a-bac3-440783832e88"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:03","userId":"a95c638e-a5c5-4d5a-bac3-440783832e88"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:03","tokenBalance":3,"userId":"d9acc7c4-6671-4254-8f39-6034e3924c8b"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:03","userId":"d9acc7c4-6671-4254-8f39-6034e3924c8b"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:03","tokenBalance":3,"userId":"2d1c9020-140a-46dc-842d-2ca54be7a854"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:03","userId":"2d1c9020-140a-46dc-842d-2ca54be7a854"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:03","tokenBalance":3,"userId":"80b44903-76e5-4d53-9831-574ecab7f147"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:03","userId":"80b44903-76e5-4d53-9831-574ecab7f147"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:03","tokenBalance":3,"userId":"b74f8249-1c2d-4db6-ad8d-6af943390e77"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:03","userId":"b74f8249-1c2d-4db6-ad8d-6af943390e77"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:03","tokenBalance":3,"userId":"cb7a1876-d50d-4ac0-b263-6790e313d4f8"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:03","userId":"cb7a1876-d50d-4ac0-b263-6790e313d4f8"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:03","tokenBalance":3,"userId":"392fd33d-ab74-4da6-a91a-02c56656a57c"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:03","userId":"392fd33d-ab74-4da6-a91a-02c56656a57c"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:03","tokenBalance":3,"userId":"0ee6c0c4-05ae-4a76-8872-6bc82a9413df"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:03","userId":"0ee6c0c4-05ae-4a76-8872-6bc82a9413df"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:03","tokenBalance":3,"userId":"43411672-8178-4b77-a062-73d1340d6dd3"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:03","userId":"43411672-8178-4b77-a062-73d1340d6dd3"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:03","tokenBalance":3,"userId":"8cd96f02-f438-4e30-b81d-df35181b1dd3"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:03","userId":"8cd96f02-f438-4e30-b81d-df35181b1dd3"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:03","tokenBalance":3,"userId":"6c14d02a-a968-495c-889c-d9a3398c74a0"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:03","userId":"6c14d02a-a968-495c-889c-d9a3398c74a0"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:03","tokenBalance":3,"userId":"ee52423e-4703-4517-8fe5-b8661b377999"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:03","userId":"ee52423e-4703-4517-8fe5-b8661b377999"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:03","tokenBalance":3,"userId":"8d16eed0-635c-4881-900f-1fdfcee24078"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:03","userId":"8d16eed0-635c-4881-900f-1fdfcee24078"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:03","tokenBalance":3,"userId":"384e4e16-5ef1-4fc2-93c0-d0b08e952d12"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:03","userId":"384e4e16-5ef1-4fc2-93c0-d0b08e952d12"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:03","tokenBalance":3,"userId":"b48bb1d1-7bf9-4262-8655-88c3c751a97e"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:03","userId":"b48bb1d1-7bf9-4262-8655-88c3c751a97e"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:03","tokenBalance":3,"userId":"8f153039-6f2b-411e-af79-c636d53cc3f9"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:03","userId":"8f153039-6f2b-411e-af79-c636d53cc3f9"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:03","tokenBalance":3,"userId":"bc754f4a-5ac0-4053-8bba-a2a42a31a746"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:03","userId":"bc754f4a-5ac0-4053-8bba-a2a42a31a746"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:03","tokenBalance":3,"userId":"849ecb09-081f-43d5-a71a-6bca85f6a8aa"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:03","userId":"849ecb09-081f-43d5-a71a-6bca85f6a8aa"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:03","tokenBalance":3,"userId":"6bc0b5c1-151e-4b05-b743-4762063acbee"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:03","userId":"6bc0b5c1-151e-4b05-b743-4762063acbee"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","tokenBalance":3,"userId":"009e09fc-311a-4851-949e-b76859139743"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","userId":"009e09fc-311a-4851-949e-b76859139743"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","tokenBalance":3,"userId":"040790f4-eeb4-46d7-84e7-768e0f8c500b"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","userId":"040790f4-eeb4-46d7-84e7-768e0f8c500b"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","tokenBalance":3,"userId":"b29a61ec-cda2-4ce1-9e63-a655109ba149"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","userId":"b29a61ec-cda2-4ce1-9e63-a655109ba149"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","tokenBalance":3,"userId":"4aa44128-345e-4ebe-b3eb-39676d2596ce"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","userId":"4aa44128-345e-4ebe-b3eb-39676d2596ce"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","tokenBalance":3,"userId":"12e2ba85-6226-4701-80f5-f7831eedfd48"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","userId":"12e2ba85-6226-4701-80f5-f7831eedfd48"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","tokenBalance":3,"userId":"d6d1f30e-be74-494c-8a9b-bb501e91a0e3"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","userId":"d6d1f30e-be74-494c-8a9b-bb501e91a0e3"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","tokenBalance":3,"userId":"bb269993-1022-43b2-8926-4fbf4043ba0c"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","userId":"bb269993-1022-43b2-8926-4fbf4043ba0c"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","tokenBalance":3,"userId":"078de71f-122d-41ec-be55-b4b93c20d6b8"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","userId":"078de71f-122d-41ec-be55-b4b93c20d6b8"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","tokenBalance":3,"userId":"b03799da-c6da-4925-87c6-901060f1ffc3"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","userId":"b03799da-c6da-4925-87c6-901060f1ffc3"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","tokenBalance":3,"userId":"3dbea5fc-4925-4aa7-bfcd-318599a9e5b1"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","userId":"3dbea5fc-4925-4aa7-bfcd-318599a9e5b1"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","tokenBalance":3,"userId":"c52e498b-c989-4e27-b783-2fe3316085a3"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","userId":"c52e498b-c989-4e27-b783-2fe3316085a3"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","tokenBalance":3,"userId":"8f72b4c2-3265-4ba0-8f52-6a1ff9d5e497"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","userId":"8f72b4c2-3265-4ba0-8f52-6a1ff9d5e497"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","tokenBalance":3,"userId":"7e1bb330-f26a-4a0f-a0df-b16e75e63d36"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","userId":"7e1bb330-f26a-4a0f-a0df-b16e75e63d36"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","tokenBalance":3,"userId":"3653691f-57cf-4782-8edf-157e46f19bd8"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","userId":"3653691f-57cf-4782-8edf-157e46f19bd8"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","tokenBalance":3,"userId":"11556c6b-f185-42ce-a3b6-369dd56a6ac5"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","userId":"11556c6b-f185-42ce-a3b6-369dd56a6ac5"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","tokenBalance":3,"userId":"d7d7720a-b095-4f24-8dea-4b4fae817a5a"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","userId":"d7d7720a-b095-4f24-8dea-4b4fae817a5a"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","tokenBalance":3,"userId":"68abe24d-9fd9-4a34-9538-81ebed775b4c"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","userId":"68abe24d-9fd9-4a34-9538-81ebed775b4c"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","tokenBalance":3,"userId":"ed022609-ebc6-41d0-a464-2dc10ca8773e"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","userId":"ed022609-ebc6-41d0-a464-2dc10ca8773e"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","tokenBalance":3,"userId":"45f1e061-9c35-4aad-802e-6d1a58fbdaf0"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","userId":"45f1e061-9c35-4aad-802e-6d1a58fbdaf0"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","tokenBalance":3,"userId":"24a91af5-f630-4d85-8a86-1863e3c977ec"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","userId":"24a91af5-f630-4d85-8a86-1863e3c977ec"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","tokenBalance":3,"userId":"f1b2443e-1fd7-40a2-b26e-001251819505"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","userId":"f1b2443e-1fd7-40a2-b26e-001251819505"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","tokenBalance":3,"userId":"c540dde8-d7e7-4b31-946b-c44073078958"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","userId":"c540dde8-d7e7-4b31-946b-c44073078958"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","tokenBalance":3,"userId":"c760022b-5504-4436-b0df-12e523fb08cf"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","userId":"c760022b-5504-4436-b0df-12e523fb08cf"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","tokenBalance":3,"userId":"950a1c16-ba41-4ac6-bea8-b052c6bd32fb"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","userId":"950a1c16-ba41-4ac6-bea8-b052c6bd32fb"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","tokenBalance":3,"userId":"b713b871-2151-4908-82c7-e25043087388"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","userId":"b713b871-2151-4908-82c7-e25043087388"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","tokenBalance":3,"userId":"31ffaa30-7eca-4caa-a144-e4d4184797f8"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","userId":"31ffaa30-7eca-4caa-a144-e4d4184797f8"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","tokenBalance":3,"userId":"27046cf8-947c-460f-bcac-21f0770d22ec"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","userId":"27046cf8-947c-460f-bcac-21f0770d22ec"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","tokenBalance":3,"userId":"15060bb5-db18-49b3-931e-9222c270e677"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","userId":"15060bb5-db18-49b3-931e-9222c270e677"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","tokenBalance":3,"userId":"2280d0b1-52dd-4ea1-af58-caf1a0dc1ccb"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","userId":"2280d0b1-52dd-4ea1-af58-caf1a0dc1ccb"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","tokenBalance":3,"userId":"8644861e-dabc-4b8b-a627-a0ae38bc44fe"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","userId":"8644861e-dabc-4b8b-a627-a0ae38bc44fe"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","tokenBalance":3,"userId":"36a5194c-32f2-4426-b5e4-76f19f18b956"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","userId":"36a5194c-32f2-4426-b5e4-76f19f18b956"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","tokenBalance":3,"userId":"b7572a8f-d3ba-410d-9871-a3439187285c"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","userId":"b7572a8f-d3ba-410d-9871-a3439187285c"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","tokenBalance":3,"userId":"cc61ca36-c057-44e6-8dee-f275f24dc1c0"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","userId":"cc61ca36-c057-44e6-8dee-f275f24dc1c0"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","tokenBalance":3,"userId":"49862644-6bd8-4161-bbf0-e0bb0565df48"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","userId":"49862644-6bd8-4161-bbf0-e0bb0565df48"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","tokenBalance":3,"userId":"f47f861d-74bb-4198-a24d-8509841f9e80"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","userId":"f47f861d-74bb-4198-a24d-8509841f9e80"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","tokenBalance":3,"userId":"072f69fa-6cbe-400b-b796-7e3271da936d"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","userId":"072f69fa-6cbe-400b-b796-7e3271da936d"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","tokenBalance":3,"userId":"b4bc5f99-ad7c-4687-83c5-9e9e21aeba64"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","userId":"b4bc5f99-ad7c-4687-83c5-9e9e21aeba64"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","tokenBalance":3,"userId":"9b4c5ed1-e673-4ee9-b696-5be44a561be0"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","userId":"9b4c5ed1-e673-4ee9-b696-5be44a561be0"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","tokenBalance":3,"userId":"fd129d2c-39d3-404c-a103-21a379463fcb"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","userId":"fd129d2c-39d3-404c-a103-21a379463fcb"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","tokenBalance":3,"userId":"a26a6dd4-23e9-46f7-b782-15f7859d8342"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","userId":"a26a6dd4-23e9-46f7-b782-15f7859d8342"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","tokenBalance":3,"userId":"8c044fe4-4f97-4ace-8360-641d1fe1e52c"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","userId":"8c044fe4-4f97-4ace-8360-641d1fe1e52c"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","tokenBalance":3,"userId":"778981a0-fa31-4a3a-9d23-e9cbb9c0c952"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","userId":"778981a0-fa31-4a3a-9d23-e9cbb9c0c952"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","tokenBalance":3,"userId":"37571634-9fe7-47c1-b50d-a0b5c5918171"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:04","userId":"37571634-9fe7-47c1-b50d-a0b5c5918171"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:05","tokenBalance":3,"userId":"5cc55727-a792-495d-ac51-0d0e844e5d53"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:05","userId":"5cc55727-a792-495d-ac51-0d0e844e5d53"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:05","tokenBalance":3,"userId":"2db8eac1-765e-4011-bb8d-7b92904ac01b"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:05","userId":"2db8eac1-765e-4011-bb8d-7b92904ac01b"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:05","tokenBalance":3,"userId":"68b8a7f4-00cf-4787-9396-f1983ed53648"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:05","userId":"68b8a7f4-00cf-4787-9396-f1983ed53648"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:05","tokenBalance":3,"userId":"7dde7770-1e0e-481e-af32-84bc96240188"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:05","userId":"7dde7770-1e0e-481e-af32-84bc96240188"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:05","tokenBalance":3,"userId":"727601f0-6746-4c23-b6af-d4d8f27c36b3"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:05","userId":"727601f0-6746-4c23-b6af-d4d8f27c36b3"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:05","tokenBalance":3,"userId":"6761c652-3586-4274-8e32-44b45eb0ab1b"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:05","userId":"6761c652-3586-4274-8e32-44b45eb0ab1b"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:05","tokenBalance":3,"userId":"4f114070-2569-44d2-ae17-e0e84d825a51"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:05","userId":"4f114070-2569-44d2-ae17-e0e84d825a51"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:05","tokenBalance":3,"userId":"c50c6b3a-274f-47ad-97e4-19a762a6aed6"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:05","userId":"c50c6b3a-274f-47ad-97e4-19a762a6aed6"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:05","tokenBalance":3,"userId":"44bf17ee-fb4d-4a0a-ab8c-ea5ac863032d"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:05","userId":"44bf17ee-fb4d-4a0a-ab8c-ea5ac863032d"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:05","tokenBalance":3,"userId":"f9b12420-847f-4968-b8b9-4153fdc8aae2"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:05","userId":"f9b12420-847f-4968-b8b9-4153fdc8aae2"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:05","tokenBalance":3,"userId":"25a10f5b-94bb-4e95-b143-09ead677e663"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:05","userId":"25a10f5b-94bb-4e95-b143-09ead677e663"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:05","tokenBalance":3,"userId":"4b9c8855-018c-44b3-89ef-8f0201454847"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:05","userId":"4b9c8855-018c-44b3-89ef-8f0201454847"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:05","tokenBalance":3,"userId":"39ede2d4-b490-4c9a-b193-f3328fbd4857"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:05","userId":"39ede2d4-b490-4c9a-b193-f3328fbd4857"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:05","tokenBalance":3,"userId":"ce779b7f-d252-445b-99cf-776c8d253223"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:05","userId":"ce779b7f-d252-445b-99cf-776c8d253223"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:05","tokenBalance":3,"userId":"9ae3d55e-01fd-4000-9698-d17c23b6e39c"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:05","userId":"9ae3d55e-01fd-4000-9698-d17c23b6e39c"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:05","tokenBalance":3,"userId":"8eaf3b43-8867-4573-91ec-926d03d93a07"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:05","userId":"8eaf3b43-8867-4573-91ec-926d03d93a07"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:05","tokenBalance":3,"userId":"77a4b43c-1906-4c6e-ab4b-7cf5ca9cc4fe"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:05","userId":"77a4b43c-1906-4c6e-ab4b-7cf5ca9cc4fe"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:05","tokenBalance":3,"userId":"da9eee0f-2f64-4776-943c-45d0496b69e0"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:05","userId":"da9eee0f-2f64-4776-943c-45d0496b69e0"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:05","tokenBalance":3,"userId":"69ae9167-80c7-4164-8f08-14fc7e5f96b8"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:05","userId":"69ae9167-80c7-4164-8f08-14fc7e5f96b8"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:05","tokenBalance":3,"userId":"3de2f9e7-75b6-429a-90a4-b4366adf0874"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:05","userId":"3de2f9e7-75b6-429a-90a4-b4366adf0874"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:05","tokenBalance":3,"userId":"c48a95d5-717b-487b-b2ce-61d7e09e5b43"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:05","userId":"c48a95d5-717b-487b-b2ce-61d7e09e5b43"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:10","tokenBalance":3,"userId":"ae217bd8-d2f3-4a8b-aac3-b3516790c2e2"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:10","userId":"ae217bd8-d2f3-4a8b-aac3-b3516790c2e2"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:10","tokenBalance":3,"userId":"829d69b3-2fdf-44eb-86a6-1d04b90cc581"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:10","userId":"829d69b3-2fdf-44eb-86a6-1d04b90cc581"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:10","tokenBalance":3,"userId":"7f361f12-3132-4693-8a61-f2ad8a8c6b0c"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:10","userId":"7f361f12-3132-4693-8a61-f2ad8a8c6b0c"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:10","tokenBalance":3,"userId":"1beefbcc-ded3-4675-85df-6c1468d16c02"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:10","userId":"1beefbcc-ded3-4675-85df-6c1468d16c02"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:10","tokenBalance":3,"userId":"e5084c4d-a410-4bed-aaae-8b1e791b1180"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:10","userId":"e5084c4d-a410-4bed-aaae-8b1e791b1180"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:13","tokenBalance":3,"userId":"50520da3-8855-4d53-bac8-711fc04cfd19"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:13","userId":"50520da3-8855-4d53-bac8-711fc04cfd19"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:13","tokenBalance":3,"userId":"c0a5909b-84fd-4460-8b7d-76601a8a73e2"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:13","userId":"c0a5909b-84fd-4460-8b7d-76601a8a73e2"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:13","tokenBalance":3,"userId":"ee3c2f77-9ce0-4e25-a239-0ccdb4b80c24"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:13","userId":"ee3c2f77-9ce0-4e25-a239-0ccdb4b80c24"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:13","tokenBalance":3,"userId":"63a9574c-b28b-4dc2-b0a6-0e009a92c1ae"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:13","userId":"63a9574c-b28b-4dc2-b0a6-0e009a92c1ae"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:13","tokenBalance":3,"userId":"62a862d9-1280-4b90-b469-db2e46dd6941"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:13","userId":"62a862d9-1280-4b90-b469-db2e46dd6941"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:21","tokenBalance":3,"userId":"0d81b437-2a5b-45bb-87e3-b22638c79d2d"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:21","userId":"0d81b437-2a5b-45bb-87e3-b22638c79d2d"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:21","tokenBalance":3,"userId":"4bcc4e0f-367a-4ecd-a233-ec3e9d48de38"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 12:42:21","userId":"4bcc4e0f-367a-4ecd-a233-ec3e9d48de38"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:21","tokenBalance":3,"userId":"da6e7756-6da7-4762-9478-15989c524ba5"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:21","userId":"da6e7756-6da7-4762-9478-15989c524ba5"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:21","tokenBalance":3,"userId":"ff453c60-d4cc-4a3b-895b-58aea0d76994"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:21","userId":"ff453c60-d4cc-4a3b-895b-58aea0d76994"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:21","tokenBalance":3,"userId":"842827f5-e6a0-464c-adee-d849b9ffb009"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:21","userId":"842827f5-e6a0-464c-adee-d849b9ffb009"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:21","tokenBalance":3,"userId":"b41051d1-550d-4d43-a899-ff6731e904ef"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:21","userId":"b41051d1-550d-4d43-a899-ff6731e904ef"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:21","tokenBalance":3,"userId":"15902e84-9592-4b67-b3c2-1ec79e1dd54d"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:21","userId":"15902e84-9592-4b67-b3c2-1ec79e1dd54d"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:21","tokenBalance":3,"userId":"a95c638e-a5c5-4d5a-bac3-440783832e88"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:21","userId":"a95c638e-a5c5-4d5a-bac3-440783832e88"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:21","tokenBalance":3,"userId":"d9acc7c4-6671-4254-8f39-6034e3924c8b"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:21","userId":"d9acc7c4-6671-4254-8f39-6034e3924c8b"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:21","tokenBalance":3,"userId":"2d1c9020-140a-46dc-842d-2ca54be7a854"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:21","userId":"2d1c9020-140a-46dc-842d-2ca54be7a854"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:21","tokenBalance":3,"userId":"80b44903-76e5-4d53-9831-574ecab7f147"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:21","userId":"80b44903-76e5-4d53-9831-574ecab7f147"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:21","tokenBalance":3,"userId":"b74f8249-1c2d-4db6-ad8d-6af943390e77"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:21","userId":"b74f8249-1c2d-4db6-ad8d-6af943390e77"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:21","tokenBalance":3,"userId":"cb7a1876-d50d-4ac0-b263-6790e313d4f8"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:21","userId":"cb7a1876-d50d-4ac0-b263-6790e313d4f8"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:21","tokenBalance":3,"userId":"392fd33d-ab74-4da6-a91a-02c56656a57c"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:21","userId":"392fd33d-ab74-4da6-a91a-02c56656a57c"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:21","tokenBalance":3,"userId":"0ee6c0c4-05ae-4a76-8872-6bc82a9413df"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:21","userId":"0ee6c0c4-05ae-4a76-8872-6bc82a9413df"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:21","tokenBalance":3,"userId":"43411672-8178-4b77-a062-73d1340d6dd3"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:21","userId":"43411672-8178-4b77-a062-73d1340d6dd3"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:21","tokenBalance":3,"userId":"8cd96f02-f438-4e30-b81d-df35181b1dd3"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:21","userId":"8cd96f02-f438-4e30-b81d-df35181b1dd3"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:21","tokenBalance":3,"userId":"6c14d02a-a968-495c-889c-d9a3398c74a0"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:21","userId":"6c14d02a-a968-495c-889c-d9a3398c74a0"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:21","tokenBalance":3,"userId":"ee52423e-4703-4517-8fe5-b8661b377999"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:21","userId":"ee52423e-4703-4517-8fe5-b8661b377999"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:21","tokenBalance":3,"userId":"8d16eed0-635c-4881-900f-1fdfcee24078"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:21","userId":"8d16eed0-635c-4881-900f-1fdfcee24078"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:21","tokenBalance":3,"userId":"384e4e16-5ef1-4fc2-93c0-d0b08e952d12"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:21","userId":"384e4e16-5ef1-4fc2-93c0-d0b08e952d12"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:21","tokenBalance":3,"userId":"b48bb1d1-7bf9-4262-8655-88c3c751a97e"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:21","userId":"b48bb1d1-7bf9-4262-8655-88c3c751a97e"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:21","tokenBalance":3,"userId":"8f153039-6f2b-411e-af79-c636d53cc3f9"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:21","userId":"8f153039-6f2b-411e-af79-c636d53cc3f9"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:21","tokenBalance":3,"userId":"bc754f4a-5ac0-4053-8bba-a2a42a31a746"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:21","userId":"bc754f4a-5ac0-4053-8bba-a2a42a31a746"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:21","tokenBalance":3,"userId":"849ecb09-081f-43d5-a71a-6bca85f6a8aa"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:21","userId":"849ecb09-081f-43d5-a71a-6bca85f6a8aa"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:21","tokenBalance":3,"userId":"6bc0b5c1-151e-4b05-b743-4762063acbee"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:21","userId":"6bc0b5c1-151e-4b05-b743-4762063acbee"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","tokenBalance":3,"userId":"009e09fc-311a-4851-949e-b76859139743"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","userId":"009e09fc-311a-4851-949e-b76859139743"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","tokenBalance":3,"userId":"040790f4-eeb4-46d7-84e7-768e0f8c500b"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","userId":"040790f4-eeb4-46d7-84e7-768e0f8c500b"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","tokenBalance":3,"userId":"b29a61ec-cda2-4ce1-9e63-a655109ba149"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","userId":"b29a61ec-cda2-4ce1-9e63-a655109ba149"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","tokenBalance":3,"userId":"4aa44128-345e-4ebe-b3eb-39676d2596ce"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","userId":"4aa44128-345e-4ebe-b3eb-39676d2596ce"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","tokenBalance":3,"userId":"12e2ba85-6226-4701-80f5-f7831eedfd48"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","userId":"12e2ba85-6226-4701-80f5-f7831eedfd48"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","tokenBalance":3,"userId":"d6d1f30e-be74-494c-8a9b-bb501e91a0e3"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","userId":"d6d1f30e-be74-494c-8a9b-bb501e91a0e3"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","tokenBalance":3,"userId":"bb269993-1022-43b2-8926-4fbf4043ba0c"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","userId":"bb269993-1022-43b2-8926-4fbf4043ba0c"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","tokenBalance":3,"userId":"078de71f-122d-41ec-be55-b4b93c20d6b8"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","userId":"078de71f-122d-41ec-be55-b4b93c20d6b8"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","tokenBalance":3,"userId":"b03799da-c6da-4925-87c6-901060f1ffc3"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","userId":"b03799da-c6da-4925-87c6-901060f1ffc3"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","tokenBalance":3,"userId":"3dbea5fc-4925-4aa7-bfcd-318599a9e5b1"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","userId":"3dbea5fc-4925-4aa7-bfcd-318599a9e5b1"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","tokenBalance":3,"userId":"c52e498b-c989-4e27-b783-2fe3316085a3"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","userId":"c52e498b-c989-4e27-b783-2fe3316085a3"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","tokenBalance":3,"userId":"8f72b4c2-3265-4ba0-8f52-6a1ff9d5e497"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","userId":"8f72b4c2-3265-4ba0-8f52-6a1ff9d5e497"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","tokenBalance":3,"userId":"7e1bb330-f26a-4a0f-a0df-b16e75e63d36"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","userId":"7e1bb330-f26a-4a0f-a0df-b16e75e63d36"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","tokenBalance":3,"userId":"3653691f-57cf-4782-8edf-157e46f19bd8"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","userId":"3653691f-57cf-4782-8edf-157e46f19bd8"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","tokenBalance":3,"userId":"11556c6b-f185-42ce-a3b6-369dd56a6ac5"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","userId":"11556c6b-f185-42ce-a3b6-369dd56a6ac5"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","tokenBalance":3,"userId":"d7d7720a-b095-4f24-8dea-4b4fae817a5a"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","userId":"d7d7720a-b095-4f24-8dea-4b4fae817a5a"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","tokenBalance":3,"userId":"68abe24d-9fd9-4a34-9538-81ebed775b4c"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","userId":"68abe24d-9fd9-4a34-9538-81ebed775b4c"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","tokenBalance":3,"userId":"ed022609-ebc6-41d0-a464-2dc10ca8773e"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","userId":"ed022609-ebc6-41d0-a464-2dc10ca8773e"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","tokenBalance":3,"userId":"45f1e061-9c35-4aad-802e-6d1a58fbdaf0"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","userId":"45f1e061-9c35-4aad-802e-6d1a58fbdaf0"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","tokenBalance":3,"userId":"24a91af5-f630-4d85-8a86-1863e3c977ec"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","userId":"24a91af5-f630-4d85-8a86-1863e3c977ec"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","tokenBalance":3,"userId":"f1b2443e-1fd7-40a2-b26e-001251819505"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","userId":"f1b2443e-1fd7-40a2-b26e-001251819505"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","tokenBalance":3,"userId":"c540dde8-d7e7-4b31-946b-c44073078958"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","userId":"c540dde8-d7e7-4b31-946b-c44073078958"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","tokenBalance":3,"userId":"c760022b-5504-4436-b0df-12e523fb08cf"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","userId":"c760022b-5504-4436-b0df-12e523fb08cf"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","tokenBalance":3,"userId":"950a1c16-ba41-4ac6-bea8-b052c6bd32fb"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","userId":"950a1c16-ba41-4ac6-bea8-b052c6bd32fb"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","tokenBalance":3,"userId":"b713b871-2151-4908-82c7-e25043087388"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","userId":"b713b871-2151-4908-82c7-e25043087388"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","tokenBalance":3,"userId":"31ffaa30-7eca-4caa-a144-e4d4184797f8"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","userId":"31ffaa30-7eca-4caa-a144-e4d4184797f8"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","tokenBalance":3,"userId":"27046cf8-947c-460f-bcac-21f0770d22ec"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","userId":"27046cf8-947c-460f-bcac-21f0770d22ec"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","tokenBalance":3,"userId":"15060bb5-db18-49b3-931e-9222c270e677"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","userId":"15060bb5-db18-49b3-931e-9222c270e677"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","tokenBalance":3,"userId":"2280d0b1-52dd-4ea1-af58-caf1a0dc1ccb"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","userId":"2280d0b1-52dd-4ea1-af58-caf1a0dc1ccb"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","tokenBalance":3,"userId":"8644861e-dabc-4b8b-a627-a0ae38bc44fe"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","userId":"8644861e-dabc-4b8b-a627-a0ae38bc44fe"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","tokenBalance":3,"userId":"36a5194c-32f2-4426-b5e4-76f19f18b956"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","userId":"36a5194c-32f2-4426-b5e4-76f19f18b956"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","tokenBalance":3,"userId":"b7572a8f-d3ba-410d-9871-a3439187285c"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","userId":"b7572a8f-d3ba-410d-9871-a3439187285c"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","tokenBalance":3,"userId":"cc61ca36-c057-44e6-8dee-f275f24dc1c0"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","userId":"cc61ca36-c057-44e6-8dee-f275f24dc1c0"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","tokenBalance":3,"userId":"49862644-6bd8-4161-bbf0-e0bb0565df48"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","userId":"49862644-6bd8-4161-bbf0-e0bb0565df48"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","tokenBalance":3,"userId":"f47f861d-74bb-4198-a24d-8509841f9e80"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:26","userId":"f47f861d-74bb-4198-a24d-8509841f9e80"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","tokenBalance":3,"userId":"072f69fa-6cbe-400b-b796-7e3271da936d"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","userId":"072f69fa-6cbe-400b-b796-7e3271da936d"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","tokenBalance":3,"userId":"b4bc5f99-ad7c-4687-83c5-9e9e21aeba64"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","userId":"b4bc5f99-ad7c-4687-83c5-9e9e21aeba64"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","tokenBalance":3,"userId":"9b4c5ed1-e673-4ee9-b696-5be44a561be0"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","userId":"9b4c5ed1-e673-4ee9-b696-5be44a561be0"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","tokenBalance":3,"userId":"fd129d2c-39d3-404c-a103-21a379463fcb"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","userId":"fd129d2c-39d3-404c-a103-21a379463fcb"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","tokenBalance":3,"userId":"a26a6dd4-23e9-46f7-b782-15f7859d8342"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","userId":"a26a6dd4-23e9-46f7-b782-15f7859d8342"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","tokenBalance":3,"userId":"8c044fe4-4f97-4ace-8360-641d1fe1e52c"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","userId":"8c044fe4-4f97-4ace-8360-641d1fe1e52c"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","tokenBalance":3,"userId":"778981a0-fa31-4a3a-9d23-e9cbb9c0c952"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","userId":"778981a0-fa31-4a3a-9d23-e9cbb9c0c952"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","tokenBalance":3,"userId":"37571634-9fe7-47c1-b50d-a0b5c5918171"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","userId":"37571634-9fe7-47c1-b50d-a0b5c5918171"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","tokenBalance":3,"userId":"5cc55727-a792-495d-ac51-0d0e844e5d53"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","userId":"5cc55727-a792-495d-ac51-0d0e844e5d53"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","tokenBalance":3,"userId":"2db8eac1-765e-4011-bb8d-7b92904ac01b"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","userId":"2db8eac1-765e-4011-bb8d-7b92904ac01b"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","tokenBalance":3,"userId":"68b8a7f4-00cf-4787-9396-f1983ed53648"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","userId":"68b8a7f4-00cf-4787-9396-f1983ed53648"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","tokenBalance":3,"userId":"7dde7770-1e0e-481e-af32-84bc96240188"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","userId":"7dde7770-1e0e-481e-af32-84bc96240188"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","tokenBalance":3,"userId":"727601f0-6746-4c23-b6af-d4d8f27c36b3"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","userId":"727601f0-6746-4c23-b6af-d4d8f27c36b3"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","tokenBalance":3,"userId":"6761c652-3586-4274-8e32-44b45eb0ab1b"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","userId":"6761c652-3586-4274-8e32-44b45eb0ab1b"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","tokenBalance":3,"userId":"4f114070-2569-44d2-ae17-e0e84d825a51"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","userId":"4f114070-2569-44d2-ae17-e0e84d825a51"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","tokenBalance":3,"userId":"c50c6b3a-274f-47ad-97e4-19a762a6aed6"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","userId":"c50c6b3a-274f-47ad-97e4-19a762a6aed6"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","tokenBalance":3,"userId":"44bf17ee-fb4d-4a0a-ab8c-ea5ac863032d"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","userId":"44bf17ee-fb4d-4a0a-ab8c-ea5ac863032d"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","tokenBalance":3,"userId":"f9b12420-847f-4968-b8b9-4153fdc8aae2"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","userId":"f9b12420-847f-4968-b8b9-4153fdc8aae2"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","tokenBalance":3,"userId":"25a10f5b-94bb-4e95-b143-09ead677e663"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","userId":"25a10f5b-94bb-4e95-b143-09ead677e663"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","tokenBalance":3,"userId":"4b9c8855-018c-44b3-89ef-8f0201454847"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","userId":"4b9c8855-018c-44b3-89ef-8f0201454847"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","tokenBalance":3,"userId":"39ede2d4-b490-4c9a-b193-f3328fbd4857"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","userId":"39ede2d4-b490-4c9a-b193-f3328fbd4857"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","tokenBalance":3,"userId":"ce779b7f-d252-445b-99cf-776c8d253223"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","userId":"ce779b7f-d252-445b-99cf-776c8d253223"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","tokenBalance":3,"userId":"9ae3d55e-01fd-4000-9698-d17c23b6e39c"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","userId":"9ae3d55e-01fd-4000-9698-d17c23b6e39c"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","tokenBalance":3,"userId":"8eaf3b43-8867-4573-91ec-926d03d93a07"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","userId":"8eaf3b43-8867-4573-91ec-926d03d93a07"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","tokenBalance":3,"userId":"77a4b43c-1906-4c6e-ab4b-7cf5ca9cc4fe"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","userId":"77a4b43c-1906-4c6e-ab4b-7cf5ca9cc4fe"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","tokenBalance":3,"userId":"da9eee0f-2f64-4776-943c-45d0496b69e0"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","userId":"da9eee0f-2f64-4776-943c-45d0496b69e0"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","tokenBalance":3,"userId":"69ae9167-80c7-4164-8f08-14fc7e5f96b8"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","userId":"69ae9167-80c7-4164-8f08-14fc7e5f96b8"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","tokenBalance":3,"userId":"3de2f9e7-75b6-429a-90a4-b4366adf0874"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","userId":"3de2f9e7-75b6-429a-90a4-b4366adf0874"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","tokenBalance":3,"userId":"c48a95d5-717b-487b-b2ce-61d7e09e5b43"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:42:28","userId":"c48a95d5-717b-487b-b2ce-61d7e09e5b43"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-18 12:43:18"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-18 12:43:18"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:43:29"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"0ef2435c-4ea2-4e0f-9ba8-281a921a7ba7","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:43:29","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:43:29"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"cd55f958-cc19-4b20-9c1d-3983554dc61d","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:43:29","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:43:29"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"9402f583-f30a-4a91-b829-2537ec7abc05","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:43:29","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:43:29"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"291d7ad2-4897-4976-ad82-aceb76460932","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:43:29","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:43:29"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"43649956-1d4f-4803-9c76-3a01bb1323a7","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:43:29","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:43:29"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"a9e4a65c-23f3-4218-aba4-618d5567a95a","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:43:29","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:43:29"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"285753bc-1594-4bda-97c8-e122b9cec6a3","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:43:29","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:43:29"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"55609144-1141-4edd-aa82-a1824a2e6095","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:43:29","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:43:29"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"71447a92-2103-42fa-890f-4daff663a832","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:43:29","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:43:29"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"0d9444d6-3c34-4504-999b-ad3f37daa025","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:43:29","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:43:59"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"52d8babc-c29f-42eb-be67-8cd9b129099f","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:43:59","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652"}
{"email":"<EMAIL>","error":"Invalid email or password","level":"error","message":"User login failed","service":"auth-service","timestamp":"2025-07-18 12:44:05"}
{"error":"Invalid email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"59bf7e4c-9b26-4874-83ab-ff4d973225cf","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.loginUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:77:13)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:46:20)","timestamp":"2025-07-18 12:44:05","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:44:29"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"d4eba1ba-54ad-49f9-a741-65433e1ac591","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:44:29","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:44:35","tokenBalance":3,"userId":"da6e7756-6da7-4762-9478-15989c524ba5"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:44:35","userId":"da6e7756-6da7-4762-9478-15989c524ba5"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:44:42"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"745e4812-7041-4083-b353-2686f5a649d1","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:44:42","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:44:42"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"bd3f901d-82ff-4b18-a808-a074cbc7f780","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:44:42","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:44:42"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"fd7a173f-d572-4a75-9009-2af9f246e1b6","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:44:42","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:44:42"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"aa38e16e-f507-4d7b-997d-24b5878eb12a","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:44:42","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:44:42"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"f4ebdf01-65c2-40e2-b9a6-68712b71b80a","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:44:42","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:44:42"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"442ee8b6-bf44-4407-b057-0ec765712bd6","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:44:42","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:44:42"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"3b957855-1f87-4690-92c7-5839b58315ae","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:44:42","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:44:42"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"7e8cf305-f82e-4b58-bd21-be42991c83a2","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:44:42","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:44:42"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"9591c0a9-45b2-4464-8bda-a4db118f71e2","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:44:42","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:44:42"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"06ff4d76-397b-4ac3-85e6-070928502c4e","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:44:42","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:45:01"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"42649b3f-3f38-489a-88bc-6b5addd6a9f5","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:45:01","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:45:24"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"94669e86-f656-4a97-b999-1b1228f2ee94","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:45:24","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:45:25","tokenBalance":3,"userId":"da6e7756-6da7-4762-9478-15989c524ba5"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:45:25","userId":"da6e7756-6da7-4762-9478-15989c524ba5"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:46:45"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"5b5ad10b-2963-4003-8014-41843b62f341","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:46:45","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:46:46","tokenBalance":3,"userId":"da6e7756-6da7-4762-9478-15989c524ba5"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:46:46","userId":"da6e7756-6da7-4762-9478-15989c524ba5"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:46:46","userId":"da6e7756-6da7-4762-9478-15989c524ba5"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:46:46","userId":"da6e7756-6da7-4762-9478-15989c524ba5"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:46:53"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"6127f828-a3cc-4af8-adcf-2447cac964b8","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:46:53","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:46:53"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"a36d578f-b21b-4721-8502-e5f1a890cb34","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:46:53","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:46:53"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"f5cf3c42-5c72-41a3-b47d-8153fcf577bd","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:46:53","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:46:53"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"ecb3ac0c-0875-4191-a1f3-048f77397877","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:46:53","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:46:53"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"cd6f8575-abbe-4c2e-924c-f3ec009209c6","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:46:53","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:46:53"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"4cce95a7-70ec-4aa1-9034-aee815f51903","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:46:53","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:46:53"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"a052873d-5de1-4fa2-bfc9-e4f7b6c6c4ca","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:46:53","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:46:53"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"c902f060-f0cc-49f5-8ae9-593d3fa910fa","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:46:53","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:46:53"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"bb6f65eb-1f9a-4dc2-8b48-a81d4dd43f9a","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:46:53","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:46:53"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"18b27399-2a8f-403b-8e0a-f15e2a1c2cb7","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:46:53","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:46:55","tokenBalance":2,"userId":"da6e7756-6da7-4762-9478-15989c524ba5"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:46:55","userId":"da6e7756-6da7-4762-9478-15989c524ba5"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:46:55","tokenBalance":3,"userId":"bb269993-1022-43b2-8926-4fbf4043ba0c"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:46:55","userId":"bb269993-1022-43b2-8926-4fbf4043ba0c"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:46:55","tokenBalance":3,"userId":"ff453c60-d4cc-4a3b-895b-58aea0d76994"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:46:55","userId":"ff453c60-d4cc-4a3b-895b-58aea0d76994"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:46:55","tokenBalance":3,"userId":"842827f5-e6a0-464c-adee-d849b9ffb009"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:46:55","userId":"842827f5-e6a0-464c-adee-d849b9ffb009"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:46:56","tokenBalance":3,"userId":"15902e84-9592-4b67-b3c2-1ec79e1dd54d"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:46:56","userId":"15902e84-9592-4b67-b3c2-1ec79e1dd54d"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:46:56","tokenBalance":3,"userId":"b41051d1-550d-4d43-a899-ff6731e904ef"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:46:56","userId":"b41051d1-550d-4d43-a899-ff6731e904ef"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:46:56","tokenBalance":3,"userId":"a95c638e-a5c5-4d5a-bac3-440783832e88"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:46:56","userId":"a95c638e-a5c5-4d5a-bac3-440783832e88"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:46:56","tokenBalance":3,"userId":"d9acc7c4-6671-4254-8f39-6034e3924c8b"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:46:56","userId":"d9acc7c4-6671-4254-8f39-6034e3924c8b"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:46:56","tokenBalance":3,"userId":"6c14d02a-a968-495c-889c-d9a3398c74a0"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:46:56","userId":"6c14d02a-a968-495c-889c-d9a3398c74a0"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:46:56","tokenBalance":3,"userId":"078de71f-122d-41ec-be55-b4b93c20d6b8"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:46:56","userId":"078de71f-122d-41ec-be55-b4b93c20d6b8"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":1,"oldBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:46:56","userId":"da6e7756-6da7-4762-9478-15989c524ba5"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:46:56","userId":"da6e7756-6da7-4762-9478-15989c524ba5"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:46:56","userId":"bb269993-1022-43b2-8926-4fbf4043ba0c"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:46:56","userId":"bb269993-1022-43b2-8926-4fbf4043ba0c"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:46:56","userId":"ff453c60-d4cc-4a3b-895b-58aea0d76994"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:46:56","userId":"ff453c60-d4cc-4a3b-895b-58aea0d76994"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:46:56","userId":"842827f5-e6a0-464c-adee-d849b9ffb009"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:46:56","userId":"842827f5-e6a0-464c-adee-d849b9ffb009"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:46:56","userId":"15902e84-9592-4b67-b3c2-1ec79e1dd54d"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:46:56","userId":"15902e84-9592-4b67-b3c2-1ec79e1dd54d"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:46:56","userId":"b41051d1-550d-4d43-a899-ff6731e904ef"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:46:56","userId":"b41051d1-550d-4d43-a899-ff6731e904ef"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:46:56","userId":"6c14d02a-a968-495c-889c-d9a3398c74a0"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:46:56","userId":"6c14d02a-a968-495c-889c-d9a3398c74a0"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:46:56","userId":"a95c638e-a5c5-4d5a-bac3-440783832e88"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:46:56","userId":"a95c638e-a5c5-4d5a-bac3-440783832e88"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:46:56","userId":"d9acc7c4-6671-4254-8f39-6034e3924c8b"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:46:56","userId":"d9acc7c4-6671-4254-8f39-6034e3924c8b"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:46:56","userId":"078de71f-122d-41ec-be55-b4b93c20d6b8"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:46:56","userId":"078de71f-122d-41ec-be55-b4b93c20d6b8"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:02"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"89387b03-653f-4d21-9c7b-9f2f14fc7cf8","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:02","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:02"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"b402d901-2fd6-4250-9fcf-f45e20d4662e","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:02","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:02"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"a42551be-4543-460f-9a61-a6ba059427aa","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:02","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:02"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"dffea3c3-7e6d-460f-96bb-2ef8376d4587","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:02","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:02"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"1c3c2e8c-7476-4872-8051-baa0dc339c2d","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:02","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:02"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"acbd0921-7a04-4322-aacd-f953890e47e0","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:02","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:02"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"36794360-9288-41c2-80c5-3fb8a86603d6","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:02","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:02"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"f38fb972-7a7d-497c-b76c-b09bf375cac2","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:02","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:02"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"dffc1aeb-f102-44bc-b118-5c938ef84f85","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:02","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:02"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"c2b35061-b6f4-4873-8cc2-871cd62b52fa","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:02","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:05","tokenBalance":1,"userId":"da6e7756-6da7-4762-9478-15989c524ba5"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:05","userId":"da6e7756-6da7-4762-9478-15989c524ba5"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:05","tokenBalance":2,"userId":"b41051d1-550d-4d43-a899-ff6731e904ef"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:05","userId":"b41051d1-550d-4d43-a899-ff6731e904ef"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:05","tokenBalance":2,"userId":"15902e84-9592-4b67-b3c2-1ec79e1dd54d"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:05","userId":"15902e84-9592-4b67-b3c2-1ec79e1dd54d"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:05","tokenBalance":2,"userId":"a95c638e-a5c5-4d5a-bac3-440783832e88"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:05","userId":"a95c638e-a5c5-4d5a-bac3-440783832e88"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:05","tokenBalance":2,"userId":"d9acc7c4-6671-4254-8f39-6034e3924c8b"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:05","userId":"d9acc7c4-6671-4254-8f39-6034e3924c8b"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:05","tokenBalance":2,"userId":"6c14d02a-a968-495c-889c-d9a3398c74a0"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:05","userId":"6c14d02a-a968-495c-889c-d9a3398c74a0"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:05","tokenBalance":2,"userId":"842827f5-e6a0-464c-adee-d849b9ffb009"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:05","userId":"842827f5-e6a0-464c-adee-d849b9ffb009"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:05","tokenBalance":2,"userId":"bb269993-1022-43b2-8926-4fbf4043ba0c"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:05","userId":"bb269993-1022-43b2-8926-4fbf4043ba0c"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:05","tokenBalance":2,"userId":"ff453c60-d4cc-4a3b-895b-58aea0d76994"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:05","userId":"ff453c60-d4cc-4a3b-895b-58aea0d76994"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:05","tokenBalance":2,"userId":"078de71f-122d-41ec-be55-b4b93c20d6b8"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:05","userId":"078de71f-122d-41ec-be55-b4b93c20d6b8"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":0,"oldBalance":0,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:05","userId":"da6e7756-6da7-4762-9478-15989c524ba5"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":0,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:05","userId":"da6e7756-6da7-4762-9478-15989c524ba5"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":1,"oldBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:05","userId":"a95c638e-a5c5-4d5a-bac3-440783832e88"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:05","userId":"a95c638e-a5c5-4d5a-bac3-440783832e88"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":1,"oldBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:05","userId":"d9acc7c4-6671-4254-8f39-6034e3924c8b"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:05","userId":"d9acc7c4-6671-4254-8f39-6034e3924c8b"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":1,"oldBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:05","userId":"b41051d1-550d-4d43-a899-ff6731e904ef"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:05","userId":"b41051d1-550d-4d43-a899-ff6731e904ef"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":1,"oldBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:05","userId":"15902e84-9592-4b67-b3c2-1ec79e1dd54d"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:05","userId":"15902e84-9592-4b67-b3c2-1ec79e1dd54d"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":1,"oldBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:05","userId":"6c14d02a-a968-495c-889c-d9a3398c74a0"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:05","userId":"6c14d02a-a968-495c-889c-d9a3398c74a0"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":1,"oldBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:05","userId":"842827f5-e6a0-464c-adee-d849b9ffb009"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:05","userId":"842827f5-e6a0-464c-adee-d849b9ffb009"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":1,"oldBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:05","userId":"bb269993-1022-43b2-8926-4fbf4043ba0c"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:05","userId":"bb269993-1022-43b2-8926-4fbf4043ba0c"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":1,"oldBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:05","userId":"ff453c60-d4cc-4a3b-895b-58aea0d76994"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:05","userId":"ff453c60-d4cc-4a3b-895b-58aea0d76994"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":1,"oldBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:05","userId":"078de71f-122d-41ec-be55-b4b93c20d6b8"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:05","userId":"078de71f-122d-41ec-be55-b4b93c20d6b8"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"9af4807a-b702-4f65-9443-cd5a02e3ab94","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"449194a9-c4c6-45ee-be46-6ecaaf8e4b63","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"548376c3-9c27-4e20-8d4a-6a8cc0f938c9","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"4957bafa-99ca-49b6-b344-19aac40989b9","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"892517b7-e2e8-4e0c-bdce-c95dd2c730c6","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"6571b1ad-f821-4370-8b18-7f25afc88300","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"191fc92a-fa69-4131-85aa-9519fe4813bb","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"fe40b867-9035-4b53-8feb-ae7ce2659405","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"e87ea04d-a8a6-46c2-9e3b-8110b1fb3006","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"3bd921d0-fe3e-4862-9f49-15f0f31d04c8","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"09b54745-987b-4a32-897c-b6918ed7c1ce","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"23c93ed2-0212-44c7-bcfd-fd2beab44ea5","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"7dcb3548-dbfc-4ca6-b3ee-aad66c3534dc","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"306cf1e2-522d-4b58-ae20-519e6c6a1c76","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"734e52f7-d0a2-4d06-a2fd-4df40e5641c3","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"d4095cfc-27bf-44cb-a430-7f197bb83029","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"9847872a-991d-4f80-8cf0-716880c1eaa5","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"a9c4c3e1-aadd-439d-a4c3-9a5d6880385c","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"e360ad3c-5a4f-41d0-8cab-fe8ee63b203a","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"1d65ee83-7293-47f5-92ec-c414f935757a","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"59af6947-2348-4e85-a8e2-7f4de2a44cb3","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"ae7caf66-ad9e-4b3f-ae63-61ab9edbb85b","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"be87e3cc-6487-4168-a50e-74abaabd5c41","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"275ff3f3-d909-49e3-8dfc-a2732ad0388c","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"dbf49d89-6d9f-47cb-9c9b-fe78eea8d650","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"182f2b13-56c0-4e47-9436-cdee2971e27d","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"2fc8c6fe-4cf6-41f9-943f-1bda2b709d28","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"eeeb0141-c82b-43b2-b6fe-ccdcc59fd613","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"ea3b7288-f546-4751-ad6c-3b7af4e02501","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"69381630-3f9e-4da2-b09e-3000e61e073a","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"b5d8f8f1-f94b-4b25-94e6-f66e63fa5d36","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"e8eb5c50-a462-49b7-8bae-b8510796b9bd","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"f61d175f-9704-4830-99a6-e02254374119","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"d7935350-768a-475a-aff7-abb3cd153f30","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"ab8a330e-9756-4c99-b927-9f56ead44ec4","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"2d6600ec-a9f8-4c6c-abe7-843374a8ccc6","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"bc4fc568-cb93-4928-9ac0-5e7d7b6358b5","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"04efb959-8fb1-4df2-aec0-72dc7931ae7c","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"3e614a2f-17ba-4417-9b79-343202ab9eec","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"f7c30229-f87d-4cc9-896a-b14b3c238ed1","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"9ce32779-e6ab-41db-a6bd-167bb9d4d8d4","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"7b338011-66f6-4b90-8ff3-e4efc7d7376e","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"e573b0cf-1751-4ab5-ba46-0d9d40b1723e","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"ff969bf7-0f74-431f-997a-b00f376b252e","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"8c0e2482-1aa9-4272-bebc-375e438c572e","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"492467a6-dc85-4fdf-bc5f-dfd4952d332c","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"994bd3ed-fa3c-41a0-adc8-8ebef0c7cae1","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"1e7b7e14-e10a-49c7-8221-d514fe72a956","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"bba2676b-7307-4db5-a772-325176618dbf","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"2f66cfe8-f552-4d5e-8238-5db162791966","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"a78a1b2c-80ac-4f41-b6ad-b4215cedbedb","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"edc918e1-6ef2-470d-8f82-84490a7c750b","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"8aef20c9-585d-4fa8-bdec-43637dd5743c","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"802be727-d739-4de1-a375-499d956efa28","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"f99aee9d-43be-432a-a232-622c723d3347","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"553f8909-646a-4287-895b-4adf1e97e418","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"ebb54782-4871-44c9-8beb-7571dd115346","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"a9d5c0d1-a097-4194-8be3-4df722163565","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"417d0173-dd86-4eb4-9edf-c053386dd3f3","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"443fa9e9-f4d9-4e62-bf73-b4f12e53298d","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"f45c89ea-ec1f-4728-bfd2-0cf6e40298f9","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"a4249bb0-2dfc-4b7e-bafc-138884b74b69","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"25584a4c-54c0-439b-84da-78e316ba9236","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"cbd25fd0-90ec-4367-948b-f0f0d6e7e6d9","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"cb1d51ae-61a0-4a1b-a3d7-3a6c2b9ca3e3","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"7f39954d-b46d-4254-86c2-40f282eb1091","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"c59991cd-f641-473e-a259-edae7ed79f8b","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:18"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"271d26f0-f169-4351-90b7-7adf892909b2","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:18","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:18"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"ab860ab8-3d78-4db7-93d2-263b896dee42","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:18","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:18"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"85385133-3443-4be9-bab4-02256b04c7ae","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:18","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:18"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"15d8f34e-90e3-4a29-9513-ce119db3993d","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:18","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:18"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"f93f15c7-2d42-43a9-8028-86e8a065dc61","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:18","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:18"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"39cd876a-ebf8-4c4e-adf2-f25b88dd6e54","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:18","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:18"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"265f79eb-58e0-4760-9be6-9e2fcd835000","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:18","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:18"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"598266e0-2680-485b-88ec-c420950cecb2","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:18","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:18"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"3788287b-c9cb-4d4b-8376-0220fd4e007a","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:18","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:18"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"673e18b6-bb0d-4d6e-80c1-f8f9815a7ac6","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:18","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:18"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"575602eb-5e62-4d14-9b21-3bbd72f41bab","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:18","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:18"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"d26ca3f9-548d-403d-9758-7d832ad3920d","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:18","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:18"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"2938b3d0-2cae-472d-92b3-7f0bacc011b7","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:18","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:18"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"8f977109-cd9e-4f75-801e-fb4cd04f559b","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:18","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:18"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"3c06213e-3aef-4c54-868b-de4b1f67d2a0","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:18","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:18"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"b7393d22-1569-4fb0-b553-1cfaf8071ff1","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:18","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:18"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"457776a0-c33d-4f2f-b4fc-75b8f95507e7","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:18","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:18"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"865142c1-a27c-40aa-ba6f-6aa6821d8d2a","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:18","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:18"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"35b2774a-2fdc-44de-a15f-568de4de6ee8","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:18","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:24"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"cc105eef-8c90-4a65-a7f9-4d27b73c27cf","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:24","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:24"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"47f6c803-8d04-4c10-ac2b-6f5d2e18ba36","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:24","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:24"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"2acf9802-8c91-44ac-b76c-d9b55db38630","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:24","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:24"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"b080fd9c-f0d2-474d-aa23-5536a4ea5dc2","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:24","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:24"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"e2afd5bf-8fca-4ef9-88fc-941132283db1","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:24","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:24"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"1cc6edc5-d334-450d-8ea4-dd78de37444c","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:24","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:24"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"51a42505-8741-4759-a702-a2984f0721a5","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:24","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:24"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"04bbed30-e696-41fe-aec2-ad6ef07aa449","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:24","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:24"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"9307afb9-cd04-40fb-bbf7-c278a68e2d62","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:24","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:24"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"4d37f6ef-37a6-47a4-bcfb-6af4a8520cd5","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:24","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:24"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"79ff5006-9396-42c1-8126-2a8282ea18e0","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:24","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:24"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"201f37ec-3890-4afc-8aed-fa4025f5e7f3","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:24","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:24"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"008a2acf-f89c-4a39-96e7-25c6f855c3c6","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:24","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:24"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"905974c1-c2fe-4639-9fd0-b9cb037c1822","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:24","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","tokenBalance":0,"userId":"da6e7756-6da7-4762-9478-15989c524ba5"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","userId":"da6e7756-6da7-4762-9478-15989c524ba5"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","tokenBalance":1,"userId":"b41051d1-550d-4d43-a899-ff6731e904ef"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","userId":"b41051d1-550d-4d43-a899-ff6731e904ef"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","tokenBalance":1,"userId":"15902e84-9592-4b67-b3c2-1ec79e1dd54d"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","userId":"15902e84-9592-4b67-b3c2-1ec79e1dd54d"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","tokenBalance":1,"userId":"a95c638e-a5c5-4d5a-bac3-440783832e88"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","userId":"a95c638e-a5c5-4d5a-bac3-440783832e88"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","tokenBalance":1,"userId":"d9acc7c4-6671-4254-8f39-6034e3924c8b"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","userId":"d9acc7c4-6671-4254-8f39-6034e3924c8b"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","tokenBalance":1,"userId":"6c14d02a-a968-495c-889c-d9a3398c74a0"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","userId":"6c14d02a-a968-495c-889c-d9a3398c74a0"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","tokenBalance":3,"userId":"2d1c9020-140a-46dc-842d-2ca54be7a854"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","userId":"2d1c9020-140a-46dc-842d-2ca54be7a854"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","tokenBalance":1,"userId":"842827f5-e6a0-464c-adee-d849b9ffb009"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","userId":"842827f5-e6a0-464c-adee-d849b9ffb009"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","tokenBalance":1,"userId":"ff453c60-d4cc-4a3b-895b-58aea0d76994"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","userId":"ff453c60-d4cc-4a3b-895b-58aea0d76994"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","tokenBalance":1,"userId":"078de71f-122d-41ec-be55-b4b93c20d6b8"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","userId":"078de71f-122d-41ec-be55-b4b93c20d6b8"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","tokenBalance":3,"userId":"b74f8249-1c2d-4db6-ad8d-6af943390e77"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","userId":"b74f8249-1c2d-4db6-ad8d-6af943390e77"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","tokenBalance":3,"userId":"0ee6c0c4-05ae-4a76-8872-6bc82a9413df"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","userId":"0ee6c0c4-05ae-4a76-8872-6bc82a9413df"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","tokenBalance":3,"userId":"43411672-8178-4b77-a062-73d1340d6dd3"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","userId":"43411672-8178-4b77-a062-73d1340d6dd3"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","tokenBalance":3,"userId":"ee52423e-4703-4517-8fe5-b8661b377999"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","userId":"ee52423e-4703-4517-8fe5-b8661b377999"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","tokenBalance":3,"userId":"8d16eed0-635c-4881-900f-1fdfcee24078"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","userId":"8d16eed0-635c-4881-900f-1fdfcee24078"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","tokenBalance":3,"userId":"384e4e16-5ef1-4fc2-93c0-d0b08e952d12"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","userId":"384e4e16-5ef1-4fc2-93c0-d0b08e952d12"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","tokenBalance":3,"userId":"b48bb1d1-7bf9-4262-8655-88c3c751a97e"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","userId":"b48bb1d1-7bf9-4262-8655-88c3c751a97e"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","tokenBalance":1,"userId":"bb269993-1022-43b2-8926-4fbf4043ba0c"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","userId":"bb269993-1022-43b2-8926-4fbf4043ba0c"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","tokenBalance":3,"userId":"8cd96f02-f438-4e30-b81d-df35181b1dd3"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","userId":"8cd96f02-f438-4e30-b81d-df35181b1dd3"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","tokenBalance":3,"userId":"8f153039-6f2b-411e-af79-c636d53cc3f9"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","userId":"8f153039-6f2b-411e-af79-c636d53cc3f9"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","tokenBalance":3,"userId":"bc754f4a-5ac0-4053-8bba-a2a42a31a746"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","userId":"bc754f4a-5ac0-4053-8bba-a2a42a31a746"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","tokenBalance":3,"userId":"80b44903-76e5-4d53-9831-574ecab7f147"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","userId":"80b44903-76e5-4d53-9831-574ecab7f147"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","tokenBalance":3,"userId":"cb7a1876-d50d-4ac0-b263-6790e313d4f8"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","userId":"cb7a1876-d50d-4ac0-b263-6790e313d4f8"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","tokenBalance":3,"userId":"392fd33d-ab74-4da6-a91a-02c56656a57c"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","userId":"392fd33d-ab74-4da6-a91a-02c56656a57c"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","tokenBalance":3,"userId":"849ecb09-081f-43d5-a71a-6bca85f6a8aa"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","userId":"849ecb09-081f-43d5-a71a-6bca85f6a8aa"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","tokenBalance":3,"userId":"6bc0b5c1-151e-4b05-b743-4762063acbee"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","userId":"6bc0b5c1-151e-4b05-b743-4762063acbee"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","tokenBalance":3,"userId":"009e09fc-311a-4851-949e-b76859139743"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","userId":"009e09fc-311a-4851-949e-b76859139743"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","tokenBalance":3,"userId":"040790f4-eeb4-46d7-84e7-768e0f8c500b"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","userId":"040790f4-eeb4-46d7-84e7-768e0f8c500b"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","tokenBalance":3,"userId":"4aa44128-345e-4ebe-b3eb-39676d2596ce"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","userId":"4aa44128-345e-4ebe-b3eb-39676d2596ce"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","tokenBalance":3,"userId":"12e2ba85-6226-4701-80f5-f7831eedfd48"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","userId":"12e2ba85-6226-4701-80f5-f7831eedfd48"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","tokenBalance":3,"userId":"d6d1f30e-be74-494c-8a9b-bb501e91a0e3"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","userId":"d6d1f30e-be74-494c-8a9b-bb501e91a0e3"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","tokenBalance":3,"userId":"b29a61ec-cda2-4ce1-9e63-a655109ba149"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","userId":"b29a61ec-cda2-4ce1-9e63-a655109ba149"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","tokenBalance":3,"userId":"b03799da-c6da-4925-87c6-901060f1ffc3"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","userId":"b03799da-c6da-4925-87c6-901060f1ffc3"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","tokenBalance":3,"userId":"3dbea5fc-4925-4aa7-bfcd-318599a9e5b1"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","userId":"3dbea5fc-4925-4aa7-bfcd-318599a9e5b1"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","tokenBalance":3,"userId":"8f72b4c2-3265-4ba0-8f52-6a1ff9d5e497"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","userId":"8f72b4c2-3265-4ba0-8f52-6a1ff9d5e497"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","tokenBalance":3,"userId":"7e1bb330-f26a-4a0f-a0df-b16e75e63d36"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:42","userId":"7e1bb330-f26a-4a0f-a0df-b16e75e63d36"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"c52e498b-c989-4e27-b783-2fe3316085a3"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"c52e498b-c989-4e27-b783-2fe3316085a3"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"3653691f-57cf-4782-8edf-157e46f19bd8"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"3653691f-57cf-4782-8edf-157e46f19bd8"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"11556c6b-f185-42ce-a3b6-369dd56a6ac5"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"11556c6b-f185-42ce-a3b6-369dd56a6ac5"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"d7d7720a-b095-4f24-8dea-4b4fae817a5a"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"d7d7720a-b095-4f24-8dea-4b4fae817a5a"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"68abe24d-9fd9-4a34-9538-81ebed775b4c"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"68abe24d-9fd9-4a34-9538-81ebed775b4c"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"ed022609-ebc6-41d0-a464-2dc10ca8773e"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"ed022609-ebc6-41d0-a464-2dc10ca8773e"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"45f1e061-9c35-4aad-802e-6d1a58fbdaf0"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"45f1e061-9c35-4aad-802e-6d1a58fbdaf0"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"f1b2443e-1fd7-40a2-b26e-001251819505"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"f1b2443e-1fd7-40a2-b26e-001251819505"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"24a91af5-f630-4d85-8a86-1863e3c977ec"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"24a91af5-f630-4d85-8a86-1863e3c977ec"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"c540dde8-d7e7-4b31-946b-c44073078958"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"c540dde8-d7e7-4b31-946b-c44073078958"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"c760022b-5504-4436-b0df-12e523fb08cf"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"c760022b-5504-4436-b0df-12e523fb08cf"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"950a1c16-ba41-4ac6-bea8-b052c6bd32fb"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"950a1c16-ba41-4ac6-bea8-b052c6bd32fb"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"b713b871-2151-4908-82c7-e25043087388"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"b713b871-2151-4908-82c7-e25043087388"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"31ffaa30-7eca-4caa-a144-e4d4184797f8"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"31ffaa30-7eca-4caa-a144-e4d4184797f8"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"27046cf8-947c-460f-bcac-21f0770d22ec"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"27046cf8-947c-460f-bcac-21f0770d22ec"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"15060bb5-db18-49b3-931e-9222c270e677"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"15060bb5-db18-49b3-931e-9222c270e677"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"2280d0b1-52dd-4ea1-af58-caf1a0dc1ccb"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"2280d0b1-52dd-4ea1-af58-caf1a0dc1ccb"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"8644861e-dabc-4b8b-a627-a0ae38bc44fe"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"8644861e-dabc-4b8b-a627-a0ae38bc44fe"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"36a5194c-32f2-4426-b5e4-76f19f18b956"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"36a5194c-32f2-4426-b5e4-76f19f18b956"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"b7572a8f-d3ba-410d-9871-a3439187285c"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"b7572a8f-d3ba-410d-9871-a3439187285c"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"cc61ca36-c057-44e6-8dee-f275f24dc1c0"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"cc61ca36-c057-44e6-8dee-f275f24dc1c0"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"49862644-6bd8-4161-bbf0-e0bb0565df48"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"49862644-6bd8-4161-bbf0-e0bb0565df48"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"f47f861d-74bb-4198-a24d-8509841f9e80"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"f47f861d-74bb-4198-a24d-8509841f9e80"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"072f69fa-6cbe-400b-b796-7e3271da936d"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"072f69fa-6cbe-400b-b796-7e3271da936d"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"fd129d2c-39d3-404c-a103-21a379463fcb"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"fd129d2c-39d3-404c-a103-21a379463fcb"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"b4bc5f99-ad7c-4687-83c5-9e9e21aeba64"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"b4bc5f99-ad7c-4687-83c5-9e9e21aeba64"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"9b4c5ed1-e673-4ee9-b696-5be44a561be0"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"9b4c5ed1-e673-4ee9-b696-5be44a561be0"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"a26a6dd4-23e9-46f7-b782-15f7859d8342"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"a26a6dd4-23e9-46f7-b782-15f7859d8342"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"8c044fe4-4f97-4ace-8360-641d1fe1e52c"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"8c044fe4-4f97-4ace-8360-641d1fe1e52c"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"778981a0-fa31-4a3a-9d23-e9cbb9c0c952"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"778981a0-fa31-4a3a-9d23-e9cbb9c0c952"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"37571634-9fe7-47c1-b50d-a0b5c5918171"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"37571634-9fe7-47c1-b50d-a0b5c5918171"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"5cc55727-a792-495d-ac51-0d0e844e5d53"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"5cc55727-a792-495d-ac51-0d0e844e5d53"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"2db8eac1-765e-4011-bb8d-7b92904ac01b"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"2db8eac1-765e-4011-bb8d-7b92904ac01b"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"68b8a7f4-00cf-4787-9396-f1983ed53648"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"68b8a7f4-00cf-4787-9396-f1983ed53648"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"727601f0-6746-4c23-b6af-d4d8f27c36b3"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"727601f0-6746-4c23-b6af-d4d8f27c36b3"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"6761c652-3586-4274-8e32-44b45eb0ab1b"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"6761c652-3586-4274-8e32-44b45eb0ab1b"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"f9b12420-847f-4968-b8b9-4153fdc8aae2"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"f9b12420-847f-4968-b8b9-4153fdc8aae2"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"4f114070-2569-44d2-ae17-e0e84d825a51"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"4f114070-2569-44d2-ae17-e0e84d825a51"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"c50c6b3a-274f-47ad-97e4-19a762a6aed6"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"c50c6b3a-274f-47ad-97e4-19a762a6aed6"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"44bf17ee-fb4d-4a0a-ab8c-ea5ac863032d"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"44bf17ee-fb4d-4a0a-ab8c-ea5ac863032d"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"25a10f5b-94bb-4e95-b143-09ead677e663"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"25a10f5b-94bb-4e95-b143-09ead677e663"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"4b9c8855-018c-44b3-89ef-8f0201454847"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"4b9c8855-018c-44b3-89ef-8f0201454847"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"39ede2d4-b490-4c9a-b193-f3328fbd4857"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"39ede2d4-b490-4c9a-b193-f3328fbd4857"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"ce779b7f-d252-445b-99cf-776c8d253223"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"ce779b7f-d252-445b-99cf-776c8d253223"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"9ae3d55e-01fd-4000-9698-d17c23b6e39c"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"9ae3d55e-01fd-4000-9698-d17c23b6e39c"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"8eaf3b43-8867-4573-91ec-926d03d93a07"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"8eaf3b43-8867-4573-91ec-926d03d93a07"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"77a4b43c-1906-4c6e-ab4b-7cf5ca9cc4fe"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"77a4b43c-1906-4c6e-ab4b-7cf5ca9cc4fe"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"da9eee0f-2f64-4776-943c-45d0496b69e0"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"da9eee0f-2f64-4776-943c-45d0496b69e0"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"69ae9167-80c7-4164-8f08-14fc7e5f96b8"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"69ae9167-80c7-4164-8f08-14fc7e5f96b8"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"3de2f9e7-75b6-429a-90a4-b4366adf0874"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"3de2f9e7-75b6-429a-90a4-b4366adf0874"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"ae217bd8-d2f3-4a8b-aac3-b3516790c2e2"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"ae217bd8-d2f3-4a8b-aac3-b3516790c2e2"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"829d69b3-2fdf-44eb-86a6-1d04b90cc581"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"829d69b3-2fdf-44eb-86a6-1d04b90cc581"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"7f361f12-3132-4693-8a61-f2ad8a8c6b0c"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"7f361f12-3132-4693-8a61-f2ad8a8c6b0c"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"1beefbcc-ded3-4675-85df-6c1468d16c02"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"1beefbcc-ded3-4675-85df-6c1468d16c02"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"e5084c4d-a410-4bed-aaae-8b1e791b1180"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"e5084c4d-a410-4bed-aaae-8b1e791b1180"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"50520da3-8855-4d53-bac8-711fc04cfd19"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"50520da3-8855-4d53-bac8-711fc04cfd19"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"c0a5909b-84fd-4460-8b7d-76601a8a73e2"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"c0a5909b-84fd-4460-8b7d-76601a8a73e2"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"ee3c2f77-9ce0-4e25-a239-0ccdb4b80c24"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"ee3c2f77-9ce0-4e25-a239-0ccdb4b80c24"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"63a9574c-b28b-4dc2-b0a6-0e009a92c1ae"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"63a9574c-b28b-4dc2-b0a6-0e009a92c1ae"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"62a862d9-1280-4b90-b469-db2e46dd6941"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"62a862d9-1280-4b90-b469-db2e46dd6941"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"0d81b437-2a5b-45bb-87e3-b22638c79d2d"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"0d81b437-2a5b-45bb-87e3-b22638c79d2d"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"4bcc4e0f-367a-4ecd-a233-ec3e9d48de38"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"4bcc4e0f-367a-4ecd-a233-ec3e9d48de38"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"7dde7770-1e0e-481e-af32-84bc96240188"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"7dde7770-1e0e-481e-af32-84bc96240188"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","tokenBalance":3,"userId":"c48a95d5-717b-487b-b2ce-61d7e09e5b43"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"c48a95d5-717b-487b-b2ce-61d7e09e5b43"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":0,"oldBalance":0,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"bb269993-1022-43b2-8926-4fbf4043ba0c"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":0,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"bb269993-1022-43b2-8926-4fbf4043ba0c"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"8cd96f02-f438-4e30-b81d-df35181b1dd3"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"8cd96f02-f438-4e30-b81d-df35181b1dd3"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"8f153039-6f2b-411e-af79-c636d53cc3f9"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"8f153039-6f2b-411e-af79-c636d53cc3f9"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"bc754f4a-5ac0-4053-8bba-a2a42a31a746"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"bc754f4a-5ac0-4053-8bba-a2a42a31a746"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"80b44903-76e5-4d53-9831-574ecab7f147"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"80b44903-76e5-4d53-9831-574ecab7f147"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"cb7a1876-d50d-4ac0-b263-6790e313d4f8"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"cb7a1876-d50d-4ac0-b263-6790e313d4f8"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"392fd33d-ab74-4da6-a91a-02c56656a57c"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"392fd33d-ab74-4da6-a91a-02c56656a57c"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"12e2ba85-6226-4701-80f5-f7831eedfd48"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"12e2ba85-6226-4701-80f5-f7831eedfd48"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"b03799da-c6da-4925-87c6-901060f1ffc3"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"b03799da-c6da-4925-87c6-901060f1ffc3"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"6bc0b5c1-151e-4b05-b743-4762063acbee"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"6bc0b5c1-151e-4b05-b743-4762063acbee"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"009e09fc-311a-4851-949e-b76859139743"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"009e09fc-311a-4851-949e-b76859139743"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"040790f4-eeb4-46d7-84e7-768e0f8c500b"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"040790f4-eeb4-46d7-84e7-768e0f8c500b"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"4aa44128-345e-4ebe-b3eb-39676d2596ce"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"4aa44128-345e-4ebe-b3eb-39676d2596ce"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"3dbea5fc-4925-4aa7-bfcd-318599a9e5b1"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"3dbea5fc-4925-4aa7-bfcd-318599a9e5b1"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"8f72b4c2-3265-4ba0-8f52-6a1ff9d5e497"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"8f72b4c2-3265-4ba0-8f52-6a1ff9d5e497"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"7e1bb330-f26a-4a0f-a0df-b16e75e63d36"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"7e1bb330-f26a-4a0f-a0df-b16e75e63d36"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"c52e498b-c989-4e27-b783-2fe3316085a3"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"c52e498b-c989-4e27-b783-2fe3316085a3"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"3653691f-57cf-4782-8edf-157e46f19bd8"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"3653691f-57cf-4782-8edf-157e46f19bd8"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"11556c6b-f185-42ce-a3b6-369dd56a6ac5"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"11556c6b-f185-42ce-a3b6-369dd56a6ac5"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"d7d7720a-b095-4f24-8dea-4b4fae817a5a"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"d7d7720a-b095-4f24-8dea-4b4fae817a5a"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"68abe24d-9fd9-4a34-9538-81ebed775b4c"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"68abe24d-9fd9-4a34-9538-81ebed775b4c"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"ed022609-ebc6-41d0-a464-2dc10ca8773e"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"ed022609-ebc6-41d0-a464-2dc10ca8773e"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"45f1e061-9c35-4aad-802e-6d1a58fbdaf0"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"45f1e061-9c35-4aad-802e-6d1a58fbdaf0"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"f1b2443e-1fd7-40a2-b26e-001251819505"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"f1b2443e-1fd7-40a2-b26e-001251819505"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"24a91af5-f630-4d85-8a86-1863e3c977ec"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"24a91af5-f630-4d85-8a86-1863e3c977ec"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"c540dde8-d7e7-4b31-946b-c44073078958"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"c540dde8-d7e7-4b31-946b-c44073078958"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"849ecb09-081f-43d5-a71a-6bca85f6a8aa"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"849ecb09-081f-43d5-a71a-6bca85f6a8aa"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"b29a61ec-cda2-4ce1-9e63-a655109ba149"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"b29a61ec-cda2-4ce1-9e63-a655109ba149"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"c760022b-5504-4436-b0df-12e523fb08cf"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"c760022b-5504-4436-b0df-12e523fb08cf"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"950a1c16-ba41-4ac6-bea8-b052c6bd32fb"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"950a1c16-ba41-4ac6-bea8-b052c6bd32fb"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"b713b871-2151-4908-82c7-e25043087388"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"b713b871-2151-4908-82c7-e25043087388"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"27046cf8-947c-460f-bcac-21f0770d22ec"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"27046cf8-947c-460f-bcac-21f0770d22ec"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"15060bb5-db18-49b3-931e-9222c270e677"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"15060bb5-db18-49b3-931e-9222c270e677"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"d6d1f30e-be74-494c-8a9b-bb501e91a0e3"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"d6d1f30e-be74-494c-8a9b-bb501e91a0e3"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"31ffaa30-7eca-4caa-a144-e4d4184797f8"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"31ffaa30-7eca-4caa-a144-e4d4184797f8"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"cc61ca36-c057-44e6-8dee-f275f24dc1c0"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"cc61ca36-c057-44e6-8dee-f275f24dc1c0"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"49862644-6bd8-4161-bbf0-e0bb0565df48"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"49862644-6bd8-4161-bbf0-e0bb0565df48"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"f47f861d-74bb-4198-a24d-8509841f9e80"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"f47f861d-74bb-4198-a24d-8509841f9e80"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"072f69fa-6cbe-400b-b796-7e3271da936d"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"072f69fa-6cbe-400b-b796-7e3271da936d"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"fd129d2c-39d3-404c-a103-21a379463fcb"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"fd129d2c-39d3-404c-a103-21a379463fcb"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"b4bc5f99-ad7c-4687-83c5-9e9e21aeba64"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"b4bc5f99-ad7c-4687-83c5-9e9e21aeba64"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"9b4c5ed1-e673-4ee9-b696-5be44a561be0"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"9b4c5ed1-e673-4ee9-b696-5be44a561be0"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"2280d0b1-52dd-4ea1-af58-caf1a0dc1ccb"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"2280d0b1-52dd-4ea1-af58-caf1a0dc1ccb"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"36a5194c-32f2-4426-b5e4-76f19f18b956"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"36a5194c-32f2-4426-b5e4-76f19f18b956"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"8644861e-dabc-4b8b-a627-a0ae38bc44fe"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"8644861e-dabc-4b8b-a627-a0ae38bc44fe"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"b7572a8f-d3ba-410d-9871-a3439187285c"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"b7572a8f-d3ba-410d-9871-a3439187285c"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"a26a6dd4-23e9-46f7-b782-15f7859d8342"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"a26a6dd4-23e9-46f7-b782-15f7859d8342"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"37571634-9fe7-47c1-b50d-a0b5c5918171"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"37571634-9fe7-47c1-b50d-a0b5c5918171"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"5cc55727-a792-495d-ac51-0d0e844e5d53"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"5cc55727-a792-495d-ac51-0d0e844e5d53"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"68b8a7f4-00cf-4787-9396-f1983ed53648"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"68b8a7f4-00cf-4787-9396-f1983ed53648"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"727601f0-6746-4c23-b6af-d4d8f27c36b3"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"727601f0-6746-4c23-b6af-d4d8f27c36b3"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"6761c652-3586-4274-8e32-44b45eb0ab1b"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"6761c652-3586-4274-8e32-44b45eb0ab1b"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"2db8eac1-765e-4011-bb8d-7b92904ac01b"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"2db8eac1-765e-4011-bb8d-7b92904ac01b"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"f9b12420-847f-4968-b8b9-4153fdc8aae2"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"f9b12420-847f-4968-b8b9-4153fdc8aae2"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"4f114070-2569-44d2-ae17-e0e84d825a51"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"4f114070-2569-44d2-ae17-e0e84d825a51"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"c50c6b3a-274f-47ad-97e4-19a762a6aed6"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"c50c6b3a-274f-47ad-97e4-19a762a6aed6"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"44bf17ee-fb4d-4a0a-ab8c-ea5ac863032d"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"44bf17ee-fb4d-4a0a-ab8c-ea5ac863032d"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"25a10f5b-94bb-4e95-b143-09ead677e663"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"25a10f5b-94bb-4e95-b143-09ead677e663"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"4b9c8855-018c-44b3-89ef-8f0201454847"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"4b9c8855-018c-44b3-89ef-8f0201454847"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"39ede2d4-b490-4c9a-b193-f3328fbd4857"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"39ede2d4-b490-4c9a-b193-f3328fbd4857"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"ce779b7f-d252-445b-99cf-776c8d253223"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"ce779b7f-d252-445b-99cf-776c8d253223"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"9ae3d55e-01fd-4000-9698-d17c23b6e39c"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"9ae3d55e-01fd-4000-9698-d17c23b6e39c"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"8eaf3b43-8867-4573-91ec-926d03d93a07"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"8eaf3b43-8867-4573-91ec-926d03d93a07"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"77a4b43c-1906-4c6e-ab4b-7cf5ca9cc4fe"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"77a4b43c-1906-4c6e-ab4b-7cf5ca9cc4fe"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"da9eee0f-2f64-4776-943c-45d0496b69e0"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"da9eee0f-2f64-4776-943c-45d0496b69e0"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"69ae9167-80c7-4164-8f08-14fc7e5f96b8"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"69ae9167-80c7-4164-8f08-14fc7e5f96b8"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"8c044fe4-4f97-4ace-8360-641d1fe1e52c"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"8c044fe4-4f97-4ace-8360-641d1fe1e52c"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"778981a0-fa31-4a3a-9d23-e9cbb9c0c952"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"778981a0-fa31-4a3a-9d23-e9cbb9c0c952"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"3de2f9e7-75b6-429a-90a4-b4366adf0874"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"3de2f9e7-75b6-429a-90a4-b4366adf0874"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"ae217bd8-d2f3-4a8b-aac3-b3516790c2e2"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"ae217bd8-d2f3-4a8b-aac3-b3516790c2e2"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"829d69b3-2fdf-44eb-86a6-1d04b90cc581"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"829d69b3-2fdf-44eb-86a6-1d04b90cc581"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"7f361f12-3132-4693-8a61-f2ad8a8c6b0c"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"7f361f12-3132-4693-8a61-f2ad8a8c6b0c"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"1beefbcc-ded3-4675-85df-6c1468d16c02"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"1beefbcc-ded3-4675-85df-6c1468d16c02"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"e5084c4d-a410-4bed-aaae-8b1e791b1180"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"e5084c4d-a410-4bed-aaae-8b1e791b1180"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"50520da3-8855-4d53-bac8-711fc04cfd19"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"50520da3-8855-4d53-bac8-711fc04cfd19"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"ee3c2f77-9ce0-4e25-a239-0ccdb4b80c24"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"ee3c2f77-9ce0-4e25-a239-0ccdb4b80c24"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"63a9574c-b28b-4dc2-b0a6-0e009a92c1ae"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"63a9574c-b28b-4dc2-b0a6-0e009a92c1ae"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"62a862d9-1280-4b90-b469-db2e46dd6941"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"62a862d9-1280-4b90-b469-db2e46dd6941"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"0d81b437-2a5b-45bb-87e3-b22638c79d2d"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"0d81b437-2a5b-45bb-87e3-b22638c79d2d"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"7dde7770-1e0e-481e-af32-84bc96240188"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"7dde7770-1e0e-481e-af32-84bc96240188"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"c48a95d5-717b-487b-b2ce-61d7e09e5b43"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"c48a95d5-717b-487b-b2ce-61d7e09e5b43"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"c0a5909b-84fd-4460-8b7d-76601a8a73e2"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"c0a5909b-84fd-4460-8b7d-76601a8a73e2"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"4bcc4e0f-367a-4ecd-a233-ec3e9d48de38"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 12:47:45","userId":"4bcc4e0f-367a-4ecd-a233-ec3e9d48de38"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-18 12:52:02"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-18 12:52:02"}
