[2025-07-15T11:48:11.881Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-15T11:48:11.900Z] [ERROR] Failed to initialize RabbitMQ {"error":""}
[2025-07-15T11:48:11.900Z] [ERROR] Failed to initialize queue service {"error":""}
[2025-07-15T11:48:11.901Z] [ERROR] Failed to initialize services {"error":""}
[2025-07-15T12:02:15.744Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-15T12:02:15.765Z] [ERROR] Failed to initialize RabbitMQ {"error":""}
[2025-07-15T12:02:15.765Z] [ERROR] Failed to initialize queue service {"error":""}
[2025-07-15T12:02:15.766Z] [ERROR] Failed to initialize services {"error":""}
[2025-07-15T12:04:07.612Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-15T12:04:07.628Z] [ERROR] Failed to initialize RabbitMQ {"error":""}
[2025-07-15T12:04:07.629Z] [ERROR] Failed to initialize queue service {"error":""}
[2025-07-15T12:04:07.629Z] [ERROR] Failed to initialize services {"error":""}
[2025-07-15T20:56:27.483Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-15T20:56:27.500Z] [ERROR] Failed to initialize RabbitMQ {"error":""}
[2025-07-15T20:56:27.500Z] [ERROR] Failed to initialize queue service {"error":""}
[2025-07-15T20:56:27.501Z] [ERROR] Failed to initialize services {"error":""}
[2025-07-15T20:57:24.122Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-15T20:57:24.139Z] [ERROR] Failed to initialize RabbitMQ {"error":""}
[2025-07-15T20:57:24.140Z] [ERROR] Failed to initialize queue service {"error":""}
[2025-07-15T20:57:24.140Z] [ERROR] Failed to initialize services {"error":""}
[2025-07-15T21:49:00.208Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T21:49:00.287Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-15T21:49:00.288Z] [INFO] Queue service initialized successfully
[2025-07-15T21:49:00.289Z] [INFO] All services initialized successfully
[2025-07-15T21:49:00.292Z] [INFO] Assessment Service running on port 3003
[2025-07-15T21:49:00.293Z] [INFO] Environment: development
[2025-07-15T21:50:52.135Z] [INFO] Shutting down gracefully...
[2025-07-15T21:50:52.141Z] [WARN] RabbitMQ connection closed
[2025-07-15T21:50:52.142Z] [INFO] RabbitMQ connection closed gracefully
[2025-07-15T21:50:52.144Z] [INFO] Queue service closed successfully
[2025-07-15T21:50:52.145Z] [INFO] All services closed successfully
[2025-07-15T21:50:53.912Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T21:50:53.987Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-15T21:50:53.989Z] [INFO] Queue service initialized successfully
[2025-07-15T21:50:53.990Z] [INFO] All services initialized successfully
[2025-07-15T21:50:53.994Z] [INFO] Assessment Service running on port 3003
[2025-07-15T21:50:53.995Z] [INFO] Environment: development
[2025-07-15T21:54:01.637Z] [ERROR] Auth service health check failed {"error":"getaddrinfo ENOTFOUND auth-service","url":"http://auth-service:3001"}
[2025-07-15T21:54:01.644Z] [INFO] ::ffff:********** - - [15/Jul/2025:21:54:01 +0000] "GET /health HTTP/1.1" 200 603 "-" "axios/1.10.0"
[2025-07-15T21:54:01.669Z] [INFO] ::ffff:********** - - [15/Jul/2025:21:54:01 +0000] "POST /assessment/submit HTTP/1.1" 404 98 "-" "axios/1.10.0"
[2025-07-15T21:55:31.885Z] [INFO] Shutting down gracefully...
[2025-07-15T21:55:31.890Z] [WARN] RabbitMQ connection closed
[2025-07-15T21:55:31.891Z] [INFO] RabbitMQ connection closed gracefully
[2025-07-15T21:55:31.892Z] [INFO] Queue service closed successfully
[2025-07-15T21:55:31.892Z] [INFO] All services closed successfully
[2025-07-15T21:55:32.904Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T21:55:32.977Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-15T21:55:32.980Z] [INFO] Queue service initialized successfully
[2025-07-15T21:55:32.981Z] [INFO] All services initialized successfully
[2025-07-15T21:55:32.984Z] [INFO] Assessment Service running on port 3003
[2025-07-15T21:55:32.986Z] [INFO] Environment: development
[2025-07-15T21:55:55.864Z] [ERROR] Auth service health check failed {"error":"getaddrinfo ENOTFOUND auth-service","url":"http://auth-service:3001"}
[2025-07-15T21:55:55.870Z] [INFO] ::ffff:********** - - [15/Jul/2025:21:55:55 +0000] "GET /health HTTP/1.1" 200 601 "-" "axios/1.10.0"
[2025-07-15T21:55:55.898Z] [INFO] ::ffff:********** - - [15/Jul/2025:21:55:55 +0000] "POST /test/submit HTTP/1.1" 404 92 "-" "axios/1.10.0"
[2025-07-15T21:57:03.251Z] [INFO] Shutting down gracefully...
[2025-07-15T21:57:03.256Z] [WARN] RabbitMQ connection closed
[2025-07-15T21:57:03.257Z] [INFO] RabbitMQ connection closed gracefully
[2025-07-15T21:57:03.258Z] [INFO] Queue service closed successfully
[2025-07-15T21:57:03.259Z] [INFO] All services closed successfully
[2025-07-15T21:57:06.009Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T21:57:06.080Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-15T21:57:06.082Z] [INFO] Queue service initialized successfully
[2025-07-15T21:57:06.083Z] [INFO] All services initialized successfully
[2025-07-15T21:57:06.087Z] [INFO] Assessment Service running on port 3003
[2025-07-15T21:57:06.088Z] [INFO] Environment: development
[2025-07-15T21:57:19.674Z] [ERROR] Auth service health check failed {"error":"getaddrinfo ENOTFOUND auth-service","url":"http://auth-service:3001"}
[2025-07-15T21:57:19.680Z] [INFO] ::ffff:********** - - [15/Jul/2025:21:57:19 +0000] "GET /health HTTP/1.1" 200 602 "-" "axios/1.10.0"
[2025-07-15T21:57:19.711Z] [INFO] Test assessment submission received {"userId":"test-user-*************","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs","multipleIntelligences","cognitiveStyleIndex"],"ip":"::ffff:**********"}
[2025-07-15T21:57:19.719Z] [INFO] Job created {"jobId":"9af9a39d-a428-48b7-a016-128cd29917e3","userId":"test-user-*************","userEmail":"<EMAIL>","status":"queued"}
[2025-07-15T21:57:19.723Z] [INFO] Assessment job published to queue {"jobId":"6f7da46c-ebcd-43b7-9fce-1be758bfaa2f","userId":"test-user-*************","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-15T21:57:19.775Z] [INFO] ::ffff:********** - - [15/Jul/2025:21:57:19 +0000] "POST /test/submit HTTP/1.1" 202 341 "-" "axios/1.10.0"
[2025-07-15T21:59:18.464Z] [WARN] RabbitMQ connection closed
[2025-07-15T21:59:23.471Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-15T21:59:23.474Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T21:59:23.478Z] [ERROR] Failed to initialize RabbitMQ {"error":"connect ECONNREFUSED 172.18.0.3:5672"}
[2025-07-15T21:59:23.479Z] [ERROR] Failed to reconnect to RabbitMQ {"error":"connect ECONNREFUSED 172.18.0.3:5672"}
[2025-07-15T21:59:28.485Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-15T21:59:28.486Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T21:59:28.489Z] [ERROR] Failed to initialize RabbitMQ {"error":"connect ECONNREFUSED 172.18.0.3:5672"}
[2025-07-15T21:59:28.490Z] [ERROR] Failed to reconnect to RabbitMQ {"error":"connect ECONNREFUSED 172.18.0.3:5672"}
[2025-07-15T21:59:33.496Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-15T21:59:33.498Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T21:59:33.555Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-15T22:01:19.348Z] [INFO] Shutting down gracefully...
[2025-07-15T22:01:19.353Z] [WARN] RabbitMQ connection closed
[2025-07-15T22:01:19.354Z] [INFO] RabbitMQ connection closed gracefully
[2025-07-15T22:01:19.355Z] [INFO] Queue service closed successfully
[2025-07-15T22:01:19.355Z] [INFO] All services closed successfully
[2025-07-15T22:01:20.602Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T22:01:20.679Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-15T22:01:20.681Z] [INFO] Queue service initialized successfully
[2025-07-15T22:01:20.682Z] [INFO] All services initialized successfully
[2025-07-15T22:01:20.686Z] [INFO] Assessment Service running on port 3003
[2025-07-15T22:01:20.687Z] [INFO] Environment: development
[2025-07-15T22:24:18.737Z] [WARN] RabbitMQ connection closed
[2025-07-15T22:24:23.744Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-15T22:24:23.746Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T22:24:23.750Z] [ERROR] Failed to initialize RabbitMQ {"error":"connect ECONNREFUSED 172.18.0.3:5672"}
[2025-07-15T22:24:23.751Z] [ERROR] Failed to reconnect to RabbitMQ {"error":"connect ECONNREFUSED 172.18.0.3:5672"}
[2025-07-15T22:24:28.758Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-15T22:24:28.760Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T22:24:32.709Z] [ERROR] Failed to initialize RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:24:32.711Z] [ERROR] Failed to reconnect to RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:24:37.715Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-15T22:24:37.716Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T22:24:41.672Z] [ERROR] Failed to initialize RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:24:41.673Z] [ERROR] Failed to reconnect to RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:24:46.680Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-15T22:24:46.681Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T22:24:50.649Z] [ERROR] Failed to initialize RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:24:50.650Z] [ERROR] Failed to reconnect to RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:24:55.656Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-15T22:24:55.659Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T22:24:59.617Z] [ERROR] Failed to initialize RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:24:59.618Z] [ERROR] Failed to reconnect to RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:25:04.625Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-15T22:25:04.627Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T22:25:08.568Z] [ERROR] Failed to initialize RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:25:08.569Z] [ERROR] Failed to reconnect to RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:25:13.576Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-15T22:25:13.577Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T22:25:17.529Z] [ERROR] Failed to initialize RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:25:17.531Z] [ERROR] Failed to reconnect to RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:25:22.534Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-15T22:25:22.535Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T22:25:26.503Z] [ERROR] Failed to initialize RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:25:26.504Z] [ERROR] Failed to reconnect to RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:25:31.512Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-15T22:25:31.514Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T22:25:35.461Z] [ERROR] Failed to initialize RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:25:35.462Z] [ERROR] Failed to reconnect to RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:25:40.467Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-15T22:25:40.469Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T22:25:44.439Z] [ERROR] Failed to initialize RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:25:44.440Z] [ERROR] Failed to reconnect to RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:25:49.447Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-15T22:25:49.448Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T22:25:53.399Z] [ERROR] Failed to initialize RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:25:53.401Z] [ERROR] Failed to reconnect to RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:25:58.407Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-15T22:25:58.409Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T22:26:02.355Z] [ERROR] Failed to initialize RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:26:02.357Z] [ERROR] Failed to reconnect to RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:26:07.362Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-15T22:26:07.365Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T22:26:11.313Z] [ERROR] Failed to initialize RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:26:11.315Z] [ERROR] Failed to reconnect to RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:26:16.322Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-15T22:26:16.323Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T22:26:20.294Z] [ERROR] Failed to initialize RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:26:20.295Z] [ERROR] Failed to reconnect to RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:26:25.302Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-15T22:26:25.303Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T22:26:29.251Z] [ERROR] Failed to initialize RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:26:29.252Z] [ERROR] Failed to reconnect to RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:26:34.256Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-15T22:26:34.257Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T22:26:38.208Z] [ERROR] Failed to initialize RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:26:38.210Z] [ERROR] Failed to reconnect to RabbitMQ {"error":"getaddrinfo ENOTFOUND rabbitmq"}
[2025-07-15T22:26:43.217Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-15T22:26:43.218Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T22:26:45.724Z] [ERROR] Failed to initialize RabbitMQ {"error":"connect ECONNREFUSED 172.18.0.3:5672"}
[2025-07-15T22:26:45.726Z] [ERROR] Failed to reconnect to RabbitMQ {"error":"connect ECONNREFUSED 172.18.0.3:5672"}
[2025-07-15T22:26:50.727Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-15T22:26:50.729Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T22:26:50.731Z] [ERROR] Failed to initialize RabbitMQ {"error":"connect ECONNREFUSED 172.18.0.3:5672"}
[2025-07-15T22:26:50.733Z] [ERROR] Failed to reconnect to RabbitMQ {"error":"connect ECONNREFUSED 172.18.0.3:5672"}
[2025-07-15T22:26:54.844Z] [INFO] Shutting down gracefully...
[2025-07-15T22:26:54.846Z] [ERROR] Error closing RabbitMQ connection {"error":"Channel closed"}
[2025-07-15T22:26:54.848Z] [ERROR] Failed to close queue service {"error":"Channel closed"}
[2025-07-15T22:26:54.849Z] [ERROR] Error during shutdown {"error":"Channel closed"}
[2025-07-15T22:27:17.503Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T22:27:17.609Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-15T22:27:17.611Z] [INFO] Queue service initialized successfully
[2025-07-15T22:27:17.612Z] [INFO] All services initialized successfully
[2025-07-15T22:27:17.618Z] [INFO] Assessment Service running on port 3003
[2025-07-15T22:27:17.620Z] [INFO] Environment: development
[2025-07-15T22:31:59.926Z] [INFO] ::ffff:********** - - [15/Jul/2025:22:31:59 +0000] "GET /health HTTP/1.1" 200 601 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-15T22:32:33.618Z] [INFO] Assessment submission received {"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs","multipleIntelligences","cognitiveStyleIndex"],"ip":"::ffff:**********"}
[2025-07-15T22:32:33.625Z] [ERROR] Token deduction failed: Auth service error {"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da","tokenAmount":1,"status":404,"statusText":"Not Found","data":{"success":false,"error":{"code":"NOT_FOUND","message":"Route POST /auth/deduct-tokens not found"}}}
[2025-07-15T22:32:33.627Z] [ERROR] Unhandled error occurred {"error":"Failed to deduct tokens","stack":"AppError: Failed to deduct tokens\n    at Object.deductTokens (/app/src/services/authService.js:145:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /app/src/routes/assessments.js:39:27","url":"/assessments/submit","method":"POST","ip":"::ffff:**********","userAgent":"axios/1.10.0","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
[2025-07-15T22:32:33.629Z] [INFO] ::ffff:********** - - [15/Jul/2025:22:32:33 +0000] "POST /assessments/submit HTTP/1.1" 500 108 "-" "axios/1.10.0"
[2025-07-15T23:08:09.404Z] [INFO] Shutting down gracefully...
[2025-07-15T23:08:09.414Z] [WARN] RabbitMQ connection closed
[2025-07-15T23:08:09.416Z] [INFO] RabbitMQ connection closed gracefully
[2025-07-15T23:08:09.420Z] [INFO] Queue service closed successfully
[2025-07-15T23:08:09.422Z] [INFO] All services closed successfully
[2025-07-15T23:08:10.689Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T23:08:10.763Z] [ERROR] RabbitMQ connection error {"error":"Channel closed by server: 406 (PRECONDITION-FAILED) with message \"PRECONDITION_FAILED - inequivalent arg 'x-dead-letter-exchange' for queue 'assessment_analysis' in vhost '/': received none but current is the value 'atma_exchange' of type 'longstr'\""}
[2025-07-15T23:08:10.766Z] [WARN] RabbitMQ connection closed
[2025-07-15T23:08:10.768Z] [ERROR] Failed to initialize RabbitMQ {"error":"Operation failed: QueueDeclare; 406 (PRECONDITION-FAILED) with message \"PRECONDITION_FAILED - inequivalent arg 'x-dead-letter-exchange' for queue 'assessment_analysis' in vhost '/': received none but current is the value 'atma_exchange' of type 'longstr'\""}
[2025-07-15T23:08:10.769Z] [ERROR] Failed to initialize queue service {"error":"Operation failed: QueueDeclare; 406 (PRECONDITION-FAILED) with message \"PRECONDITION_FAILED - inequivalent arg 'x-dead-letter-exchange' for queue 'assessment_analysis' in vhost '/': received none but current is the value 'atma_exchange' of type 'longstr'\""}
[2025-07-15T23:08:10.771Z] [ERROR] Failed to initialize services {"error":"Operation failed: QueueDeclare; 406 (PRECONDITION-FAILED) with message \"PRECONDITION_FAILED - inequivalent arg 'x-dead-letter-exchange' for queue 'assessment_analysis' in vhost '/': received none but current is the value 'atma_exchange' of type 'longstr'\""}
[2025-07-15T23:10:26.657Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T23:10:26.726Z] [ERROR] RabbitMQ connection error {"error":"Channel closed by server: 406 (PRECONDITION-FAILED) with message \"PRECONDITION_FAILED - inequivalent arg 'x-dead-letter-exchange' for queue 'assessment_analysis' in vhost '/': received none but current is the value 'atma_exchange' of type 'longstr'\""}
[2025-07-15T23:10:26.729Z] [WARN] RabbitMQ connection closed
[2025-07-15T23:10:26.731Z] [ERROR] Failed to initialize RabbitMQ {"error":"Operation failed: QueueDeclare; 406 (PRECONDITION-FAILED) with message \"PRECONDITION_FAILED - inequivalent arg 'x-dead-letter-exchange' for queue 'assessment_analysis' in vhost '/': received none but current is the value 'atma_exchange' of type 'longstr'\""}
[2025-07-15T23:10:26.733Z] [ERROR] Failed to initialize queue service {"error":"Operation failed: QueueDeclare; 406 (PRECONDITION-FAILED) with message \"PRECONDITION_FAILED - inequivalent arg 'x-dead-letter-exchange' for queue 'assessment_analysis' in vhost '/': received none but current is the value 'atma_exchange' of type 'longstr'\""}
[2025-07-15T23:10:26.734Z] [ERROR] Failed to initialize services {"error":"Operation failed: QueueDeclare; 406 (PRECONDITION-FAILED) with message \"PRECONDITION_FAILED - inequivalent arg 'x-dead-letter-exchange' for queue 'assessment_analysis' in vhost '/': received none but current is the value 'atma_exchange' of type 'longstr'\""}
[2025-07-15T23:11:10.199Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T23:11:10.273Z] [ERROR] RabbitMQ connection error {"error":"Channel closed by server: 406 (PRECONDITION-FAILED) with message \"PRECONDITION_FAILED - inequivalent arg 'x-dead-letter-exchange' for queue 'assessment_analysis' in vhost '/': received none but current is the value 'atma_exchange' of type 'longstr'\""}
[2025-07-15T23:11:10.275Z] [WARN] RabbitMQ connection closed
[2025-07-15T23:11:10.277Z] [ERROR] Failed to initialize RabbitMQ {"error":"Operation failed: QueueDeclare; 406 (PRECONDITION-FAILED) with message \"PRECONDITION_FAILED - inequivalent arg 'x-dead-letter-exchange' for queue 'assessment_analysis' in vhost '/': received none but current is the value 'atma_exchange' of type 'longstr'\""}
[2025-07-15T23:11:10.279Z] [ERROR] Failed to initialize queue service {"error":"Operation failed: QueueDeclare; 406 (PRECONDITION-FAILED) with message \"PRECONDITION_FAILED - inequivalent arg 'x-dead-letter-exchange' for queue 'assessment_analysis' in vhost '/': received none but current is the value 'atma_exchange' of type 'longstr'\""}
[2025-07-15T23:11:10.280Z] [ERROR] Failed to initialize services {"error":"Operation failed: QueueDeclare; 406 (PRECONDITION-FAILED) with message \"PRECONDITION_FAILED - inequivalent arg 'x-dead-letter-exchange' for queue 'assessment_analysis' in vhost '/': received none but current is the value 'atma_exchange' of type 'longstr'\""}
[2025-07-15T23:12:11.601Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T23:12:11.739Z] [ERROR] RabbitMQ connection error {"error":"Channel closed by server: 406 (PRECONDITION-FAILED) with message \"PRECONDITION_FAILED - inequivalent arg 'x-dead-letter-exchange' for queue 'assessment_analysis' in vhost '/': received none but current is the value 'atma_exchange' of type 'longstr'\""}
[2025-07-15T23:12:11.745Z] [WARN] RabbitMQ connection closed
[2025-07-15T23:12:11.748Z] [ERROR] Failed to initialize RabbitMQ {"error":"Operation failed: QueueDeclare; 406 (PRECONDITION-FAILED) with message \"PRECONDITION_FAILED - inequivalent arg 'x-dead-letter-exchange' for queue 'assessment_analysis' in vhost '/': received none but current is the value 'atma_exchange' of type 'longstr'\""}
[2025-07-15T23:12:11.750Z] [ERROR] Failed to initialize queue service {"error":"Operation failed: QueueDeclare; 406 (PRECONDITION-FAILED) with message \"PRECONDITION_FAILED - inequivalent arg 'x-dead-letter-exchange' for queue 'assessment_analysis' in vhost '/': received none but current is the value 'atma_exchange' of type 'longstr'\""}
[2025-07-15T23:12:11.752Z] [ERROR] Failed to initialize services {"error":"Operation failed: QueueDeclare; 406 (PRECONDITION-FAILED) with message \"PRECONDITION_FAILED - inequivalent arg 'x-dead-letter-exchange' for queue 'assessment_analysis' in vhost '/': received none but current is the value 'atma_exchange' of type 'longstr'\""}
[2025-07-15T23:12:44.765Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T23:12:44.849Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-15T23:12:44.852Z] [INFO] Queue service initialized successfully
[2025-07-15T23:12:44.853Z] [INFO] All services initialized successfully
[2025-07-15T23:12:44.858Z] [INFO] Assessment Service running on port 3003
[2025-07-15T23:12:44.859Z] [INFO] Environment: development
[2025-07-15T23:13:02.474Z] [INFO] Assessment submission received {"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs","multipleIntelligences","cognitiveStyleIndex"],"ip":"::ffff:**********"}
[2025-07-15T23:13:02.483Z] [ERROR] Token deduction failed: Auth service error {"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da","tokenAmount":1,"status":401,"statusText":"Unauthorized","data":{"success":false,"error":{"code":"UNAUTHORIZED","message":"Invalid service key"}}}
[2025-07-15T23:13:02.485Z] [ERROR] Unhandled error occurred {"error":"Invalid or expired token","stack":"AppError: Invalid or expired token\n    at Object.deductTokens (/app/src/services/authService.js:145:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /app/src/routes/assessments.js:39:27","url":"/assessments/submit","method":"POST","ip":"::ffff:**********","userAgent":"axios/1.10.0","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
[2025-07-15T23:13:02.489Z] [INFO] ::ffff:********** - - [15/Jul/2025:23:13:02 +0000] "POST /assessments/submit HTTP/1.1" 401 99 "-" "axios/1.10.0"
[2025-07-15T23:13:41.762Z] [INFO] Shutting down gracefully...
[2025-07-15T23:13:41.771Z] [WARN] RabbitMQ connection closed
[2025-07-15T23:13:41.774Z] [INFO] RabbitMQ connection closed gracefully
[2025-07-15T23:13:41.776Z] [INFO] Queue service closed successfully
[2025-07-15T23:13:41.777Z] [INFO] All services closed successfully
[2025-07-15T23:13:42.913Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T23:13:42.985Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-15T23:13:42.986Z] [INFO] Queue service initialized successfully
[2025-07-15T23:13:42.987Z] [INFO] All services initialized successfully
[2025-07-15T23:13:42.992Z] [INFO] Assessment Service running on port 3003
[2025-07-15T23:13:42.993Z] [INFO] Environment: development
[2025-07-15T23:13:51.346Z] [INFO] Assessment submission received {"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs","multipleIntelligences","cognitiveStyleIndex"],"ip":"::ffff:**********"}
[2025-07-15T23:13:51.356Z] [ERROR] Token deduction failed: Auth service error {"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da","tokenAmount":1,"status":401,"statusText":"Unauthorized","data":{"success":false,"error":{"code":"UNAUTHORIZED","message":"Invalid service key"}}}
[2025-07-15T23:13:51.361Z] [ERROR] Unhandled error occurred {"error":"Invalid or expired token","stack":"AppError: Invalid or expired token\n    at Object.deductTokens (/app/src/services/authService.js:145:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /app/src/routes/assessments.js:39:27","url":"/assessments/submit","method":"POST","ip":"::ffff:**********","userAgent":"axios/1.10.0","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
[2025-07-15T23:13:51.365Z] [INFO] ::ffff:********** - - [15/Jul/2025:23:13:51 +0000] "POST /assessments/submit HTTP/1.1" 401 99 "-" "axios/1.10.0"
[2025-07-15T23:14:36.397Z] [INFO] Shutting down gracefully...
[2025-07-15T23:14:36.428Z] [WARN] RabbitMQ connection closed
[2025-07-15T23:14:36.433Z] [INFO] RabbitMQ connection closed gracefully
[2025-07-15T23:14:36.437Z] [INFO] Queue service closed successfully
[2025-07-15T23:14:36.442Z] [INFO] All services closed successfully
[2025-07-15T23:14:38.211Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T23:14:38.282Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-15T23:14:38.284Z] [INFO] Queue service initialized successfully
[2025-07-15T23:14:38.286Z] [INFO] All services initialized successfully
[2025-07-15T23:14:38.291Z] [INFO] Assessment Service running on port 3003
[2025-07-15T23:14:38.292Z] [INFO] Environment: development
[2025-07-15T23:14:45.745Z] [INFO] Assessment submission received {"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs","multipleIntelligences","cognitiveStyleIndex"],"ip":"::ffff:**********"}
[2025-07-15T23:14:45.754Z] [ERROR] Token deduction failed: Auth service error {"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da","tokenAmount":1,"status":401,"statusText":"Unauthorized","data":{"success":false,"error":{"code":"UNAUTHORIZED","message":"Invalid service key"}}}
[2025-07-15T23:14:45.757Z] [ERROR] Unhandled error occurred {"error":"Invalid or expired token","stack":"AppError: Invalid or expired token\n    at Object.deductTokens (/app/src/services/authService.js:145:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /app/src/routes/assessments.js:39:27","url":"/assessments/submit","method":"POST","ip":"::ffff:**********","userAgent":"axios/1.10.0","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
[2025-07-15T23:14:45.762Z] [INFO] ::ffff:********** - - [15/Jul/2025:23:14:45 +0000] "POST /assessments/submit HTTP/1.1" 401 99 "-" "axios/1.10.0"
[2025-07-15T23:15:48.494Z] [INFO] Shutting down gracefully...
[2025-07-15T23:15:48.505Z] [WARN] RabbitMQ connection closed
[2025-07-15T23:15:48.509Z] [INFO] RabbitMQ connection closed gracefully
[2025-07-15T23:15:48.513Z] [INFO] Queue service closed successfully
[2025-07-15T23:15:48.518Z] [INFO] All services closed successfully
[2025-07-15T23:15:59.324Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T23:15:59.408Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-15T23:15:59.410Z] [INFO] Queue service initialized successfully
[2025-07-15T23:15:59.412Z] [INFO] All services initialized successfully
[2025-07-15T23:15:59.416Z] [INFO] Assessment Service running on port 3003
[2025-07-15T23:15:59.418Z] [INFO] Environment: development
[2025-07-15T23:16:17.835Z] [INFO] Assessment submission received {"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs","multipleIntelligences","cognitiveStyleIndex"],"ip":"::ffff:**********"}
[2025-07-15T23:16:17.869Z] [INFO] Token deduction successful {"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da","deductedAmount":1,"remainingBalance":4}
[2025-07-15T23:16:17.871Z] [INFO] Job created {"jobId":"d1359cf8-37bc-4d7e-9170-3965a1a39b39","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da","userEmail":"<EMAIL>","status":"queued"}
[2025-07-15T23:16:17.874Z] [INFO] Assessment job published to queue {"jobId":"449ede9e-62cb-436a-97fa-84abb17a1f2b","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-15T23:16:17.925Z] [INFO] ::ffff:********** - - [15/Jul/2025:23:16:17 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-15T23:20:59.074Z] [INFO] Assessment submission received {"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs","multipleIntelligences","cognitiveStyleIndex"],"ip":"::ffff:**********"}
[2025-07-15T23:20:59.099Z] [INFO] Token deduction successful {"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da","deductedAmount":1,"remainingBalance":3}
[2025-07-15T23:20:59.102Z] [INFO] Job created {"jobId":"77de69ee-f397-45b2-b5f0-e60bc3335216","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da","userEmail":"<EMAIL>","status":"queued"}
[2025-07-15T23:20:59.104Z] [INFO] Assessment job published to queue {"jobId":"53f944c9-339d-4bd5-95d4-dc6230fc7add","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-15T23:20:59.108Z] [INFO] ::ffff:********** - - [15/Jul/2025:23:20:59 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-15T23:27:53.079Z] [INFO] Assessment submission received {"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs","multipleIntelligences","cognitiveStyleIndex"],"ip":"::ffff:**********"}
[2025-07-15T23:27:53.098Z] [INFO] Token deduction successful {"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da","deductedAmount":1,"remainingBalance":2}
[2025-07-15T23:27:53.100Z] [INFO] Job created {"jobId":"66d8f0e1-cca7-44f4-9484-8fba933cd464","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da","userEmail":"<EMAIL>","status":"queued"}
[2025-07-15T23:27:53.102Z] [INFO] Assessment job published to queue {"jobId":"abaee042-b2b2-4913-a783-898e116fcc87","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-15T23:27:53.105Z] [INFO] ::ffff:********** - - [15/Jul/2025:23:27:53 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-15T23:31:42.802Z] [INFO] Shutting down gracefully...
[2025-07-15T23:31:42.820Z] [WARN] RabbitMQ connection closed
[2025-07-15T23:31:42.827Z] [INFO] RabbitMQ connection closed gracefully
[2025-07-15T23:31:42.840Z] [INFO] Queue service closed successfully
[2025-07-15T23:31:42.843Z] [INFO] All services closed successfully
[2025-07-15T23:32:39.265Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://guest:guest@rabbitmq:5672"}
[2025-07-15T23:32:39.367Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-15T23:32:39.369Z] [INFO] Queue service initialized successfully
[2025-07-15T23:32:39.370Z] [INFO] All services initialized successfully
[2025-07-15T23:32:39.375Z] [INFO] Assessment Service running on port 3003
[2025-07-15T23:32:39.376Z] [INFO] Environment: development
[2025-07-16T01:20:08.326Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-16T01:20:08.361Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-16T01:20:08.361Z] [INFO] Queue service initialized successfully
[2025-07-16T01:20:08.362Z] [INFO] All services initialized successfully
[2025-07-16T01:20:08.365Z] [INFO] Assessment Service running on port 3003
[2025-07-16T01:20:08.365Z] [INFO] Environment: development
[2025-07-16T01:21:04.785Z] [INFO] ::1 - - [16/Jul/2025:01:21:04 +0000] "GET / HTTP/1.1" 200 120 "-" "axios/1.10.0"
[2025-07-16T01:41:14.802Z] [INFO] ::1 - - [16/Jul/2025:01:41:14 +0000] "GET / HTTP/1.1" 200 120 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652"
[2025-07-16T01:43:54.481Z] [INFO] ::1 - - [16/Jul/2025:01:43:54 +0000] "GET / HTTP/1.1" 200 120 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652"
[2025-07-16T06:16:13.289Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-16T06:16:13.331Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-16T06:16:13.332Z] [INFO] Queue service initialized successfully
[2025-07-16T06:16:13.332Z] [INFO] All services initialized successfully
[2025-07-16T06:16:13.337Z] [INFO] Assessment Service running on port 3003
[2025-07-16T06:16:13.338Z] [INFO] Environment: development
[2025-07-16T06:18:03.556Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-16T06:18:03.601Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-16T06:18:03.601Z] [INFO] Queue service initialized successfully
[2025-07-16T06:18:03.602Z] [INFO] All services initialized successfully
[2025-07-16T06:18:03.606Z] [INFO] Assessment Service running on port 3003
[2025-07-16T06:18:03.607Z] [INFO] Environment: development
[2025-07-16T06:20:59.362Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-16T06:20:59.401Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-16T06:20:59.401Z] [INFO] Queue service initialized successfully
[2025-07-16T06:20:59.402Z] [INFO] All services initialized successfully
[2025-07-16T06:20:59.406Z] [INFO] Assessment Service running on port 3003
[2025-07-16T06:20:59.407Z] [INFO] Environment: development
[2025-07-16T06:26:05.256Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-16T06:26:05.300Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-16T06:26:05.300Z] [INFO] Queue service initialized successfully
[2025-07-16T06:26:05.301Z] [INFO] All services initialized successfully
[2025-07-16T06:26:05.305Z] [INFO] Assessment Service running on port 3003
[2025-07-16T06:26:05.305Z] [INFO] Environment: development
[2025-07-16T06:55:55.112Z] [WARN] Validation error {"source":"body","errors":{"riasec.realistic":"Realistic score is required","riasec.investigative":"Investigative score is required","riasec.artistic":"Artistic score is required","riasec.social":"Social score is required","riasec.enterprising":"Enterprising score is required","riasec.conventional":"Conventional score is required","ocean.openness":"Openness score is required","ocean.conscientiousness":"Conscientiousness score is required","ocean.extraversion":"Extraversion score is required","ocean.agreeableness":"Agreeableness score is required","ocean.neuroticism":"Neuroticism score is required","viaIs.creativity":"VIA-IS assessment data is required","viaIs.curiosity":"VIA-IS assessment data is required","viaIs.judgment":"VIA-IS assessment data is required","viaIs.loveOfLearning":"VIA-IS assessment data is required","viaIs.perspective":"VIA-IS assessment data is required","viaIs.bravery":"VIA-IS assessment data is required","viaIs.perseverance":"VIA-IS assessment data is required","viaIs.honesty":"VIA-IS assessment data is required","viaIs.zest":"VIA-IS assessment data is required","viaIs.love":"VIA-IS assessment data is required","viaIs.kindness":"VIA-IS assessment data is required","viaIs.socialIntelligence":"VIA-IS assessment data is required","viaIs.teamwork":"VIA-IS assessment data is required","viaIs.fairness":"VIA-IS assessment data is required","viaIs.leadership":"VIA-IS assessment data is required","viaIs.forgiveness":"VIA-IS assessment data is required","viaIs.humility":"VIA-IS assessment data is required","viaIs.prudence":"VIA-IS assessment data is required","viaIs.selfRegulation":"VIA-IS assessment data is required","viaIs.appreciationOfBeauty":"VIA-IS assessment data is required","viaIs.gratitude":"VIA-IS assessment data is required","viaIs.hope":"VIA-IS assessment data is required","viaIs.humor":"VIA-IS assessment data is required","viaIs.spirituality":"VIA-IS assessment data is required"},"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309","url":"/assessments/submit"}
[2025-07-16T06:55:55.116Z] [INFO] ::1 - - [16/Jul/2025:06:55:55 +0000] "POST /assessments/submit HTTP/1.1" 400 2006 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-16T07:41:37.856Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-16T07:41:37.898Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-16T07:41:37.898Z] [INFO] Queue service initialized successfully
[2025-07-16T07:41:37.899Z] [INFO] All services initialized successfully
[2025-07-16T07:41:37.903Z] [INFO] Assessment Service running on port 3003
[2025-07-16T07:41:37.904Z] [INFO] Environment: development
[2025-07-16T07:43:01.970Z] [INFO] ::1 - - [16/Jul/2025:07:43:01 +0000] "GET /health HTTP/1.1" 200 590 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-16T07:43:01.983Z] [INFO] ::1 - - [16/Jul/2025:07:43:01 +0000] "GET /health HTTP/1.1" 200 590 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-16T07:43:08.204Z] [INFO] ::1 - - [16/Jul/2025:07:43:08 +0000] "GET /health HTTP/1.1" 200 590 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-16T08:05:23.613Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-16T08:05:23.655Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-16T08:05:23.655Z] [INFO] Queue service initialized successfully
[2025-07-16T08:05:23.656Z] [INFO] All services initialized successfully
[2025-07-16T08:05:23.659Z] [INFO] Assessment Service running on port 3003
[2025-07-16T08:05:23.660Z] [INFO] Environment: development
[2025-07-16T08:20:30.814Z] [INFO] ::1 - - [16/Jul/2025:08:20:30 +0000] "GET /health HTTP/1.1" 200 590 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-16T08:20:30.829Z] [INFO] ::1 - - [16/Jul/2025:08:20:30 +0000] "GET /health HTTP/1.1" 200 591 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-16T08:20:52.833Z] [INFO] ::1 - - [16/Jul/2025:08:20:52 +0000] "GET /health HTTP/1.1" 200 591 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-16T08:20:55.745Z] [INFO] ::1 - - [16/Jul/2025:08:20:55 +0000] "GET /health HTTP/1.1" 200 589 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-16T08:20:56.396Z] [INFO] ::1 - - [16/Jul/2025:08:20:56 +0000] "GET /health HTTP/1.1" 200 591 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-16T08:20:56.766Z] [INFO] ::1 - - [16/Jul/2025:08:20:56 +0000] "GET /health HTTP/1.1" 200 591 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-16T08:20:57.002Z] [INFO] ::1 - - [16/Jul/2025:08:20:57 +0000] "GET /health HTTP/1.1" 200 591 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-16T08:35:15.593Z] [WARN] Validation error {"source":"body","errors":{"riasec.realistic":"Realistic score is required","riasec.investigative":"Investigative score is required","riasec.artistic":"Artistic score is required","riasec.social":"Social score is required","riasec.enterprising":"Enterprising score is required","riasec.conventional":"Conventional score is required","ocean.openness":"Openness score is required","ocean.conscientiousness":"Conscientiousness score is required","ocean.extraversion":"Extraversion score is required","ocean.agreeableness":"Agreeableness score is required","ocean.neuroticism":"Neuroticism score is required","viaIs.creativity":"VIA-IS assessment data is required","viaIs.curiosity":"VIA-IS assessment data is required","viaIs.judgment":"VIA-IS assessment data is required","viaIs.loveOfLearning":"VIA-IS assessment data is required","viaIs.perspective":"VIA-IS assessment data is required","viaIs.bravery":"VIA-IS assessment data is required","viaIs.perseverance":"VIA-IS assessment data is required","viaIs.honesty":"VIA-IS assessment data is required","viaIs.zest":"VIA-IS assessment data is required","viaIs.love":"VIA-IS assessment data is required","viaIs.kindness":"VIA-IS assessment data is required","viaIs.socialIntelligence":"VIA-IS assessment data is required","viaIs.teamwork":"VIA-IS assessment data is required","viaIs.fairness":"VIA-IS assessment data is required","viaIs.leadership":"VIA-IS assessment data is required","viaIs.forgiveness":"VIA-IS assessment data is required","viaIs.humility":"VIA-IS assessment data is required","viaIs.prudence":"VIA-IS assessment data is required","viaIs.selfRegulation":"VIA-IS assessment data is required","viaIs.appreciationOfBeauty":"VIA-IS assessment data is required","viaIs.gratitude":"VIA-IS assessment data is required","viaIs.hope":"VIA-IS assessment data is required","viaIs.humor":"VIA-IS assessment data is required","viaIs.spirituality":"VIA-IS assessment data is required"},"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309","url":"/assessments/submit"}
[2025-07-16T08:35:15.595Z] [INFO] ::1 - - [16/Jul/2025:08:35:15 +0000] "POST /assessments/submit HTTP/1.1" 400 2006 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-16T09:15:14.713Z] [INFO] ::1 - - [16/Jul/2025:09:15:14 +0000] "GET /health HTTP/1.1" 200 592 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-16T09:15:14.769Z] [INFO] ::1 - - [16/Jul/2025:09:15:14 +0000] "GET /health HTTP/1.1" 200 592 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-16T09:24:42.849Z] [INFO] ::1 - - [16/Jul/2025:09:24:42 +0000] "GET /health HTTP/1.1" 200 592 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-16T09:25:24.200Z] [INFO] ::1 - - [16/Jul/2025:09:25:24 +0000] "GET /health HTTP/1.1" 200 591 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-16T09:25:26.023Z] [WARN] Validation error {"source":"body","errors":{"riasec.investigative":"Investigative score must be an integer","riasec.artistic":"Artistic score must be an integer","riasec.enterprising":"Enterprising score must be an integer","riasec.conventional":"Conventional score must be an integer","ocean.conscientiousness":"Conscientiousness score must be an integer","ocean.extraversion":"Extraversion score must be an integer","ocean.agreeableness":"Agreeableness score must be an integer","ocean.neuroticism":"Neuroticism score must be an integer","viaIs.curiosity":"VIA-IS scores must be integers","viaIs.judgment":"VIA-IS scores must be integers","viaIs.loveOfLearning":"VIA-IS scores must be integers","viaIs.perspective":"VIA-IS scores must be integers","viaIs.bravery":"VIA-IS scores must be integers","viaIs.perseverance":"VIA-IS scores must be integers","viaIs.honesty":"VIA-IS scores must be integers","viaIs.zest":"VIA-IS scores must be integers","viaIs.love":"VIA-IS scores must be integers","viaIs.socialIntelligence":"VIA-IS scores must be integers","viaIs.teamwork":"VIA-IS scores must be integers","viaIs.fairness":"VIA-IS scores must be integers","viaIs.leadership":"VIA-IS scores must be integers","viaIs.forgiveness":"VIA-IS scores must be integers","viaIs.humility":"VIA-IS scores must be integers","viaIs.prudence":"VIA-IS scores must be integers","viaIs.selfRegulation":"VIA-IS scores must be integers","viaIs.appreciationOfBeauty":"VIA-IS scores must be integers","viaIs.gratitude":"VIA-IS scores must be integers","viaIs.hope":"VIA-IS scores must be integers","viaIs.humor":"VIA-IS scores must be integers"},"userId":"********-710f-4360-8dfa-4ce497f7e98f","url":"/assessments/submit"}
[2025-07-16T09:25:26.025Z] [INFO] ::1 - - [16/Jul/2025:09:25:26 +0000] "POST /assessments/submit HTTP/1.1" 400 1672 "-" "axios/1.10.0"
[2025-07-16T09:28:11.694Z] [INFO] ::1 - - [16/Jul/2025:09:28:11 +0000] "GET /health HTTP/1.1" 200 592 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-16T09:28:13.511Z] [WARN] Validation error {"source":"body","errors":{"riasec.investigative":"Investigative score must be an integer","riasec.artistic":"Artistic score must be an integer","riasec.enterprising":"Enterprising score must be an integer","riasec.conventional":"Conventional score must be an integer","ocean.conscientiousness":"Conscientiousness score must be an integer","ocean.extraversion":"Extraversion score must be an integer","ocean.agreeableness":"Agreeableness score must be an integer","ocean.neuroticism":"Neuroticism score must be an integer","viaIs.curiosity":"VIA-IS scores must be integers","viaIs.judgment":"VIA-IS scores must be integers","viaIs.loveOfLearning":"VIA-IS scores must be integers","viaIs.perspective":"VIA-IS scores must be integers","viaIs.bravery":"VIA-IS scores must be integers","viaIs.perseverance":"VIA-IS scores must be integers","viaIs.honesty":"VIA-IS scores must be integers","viaIs.zest":"VIA-IS scores must be integers","viaIs.love":"VIA-IS scores must be integers","viaIs.socialIntelligence":"VIA-IS scores must be integers","viaIs.teamwork":"VIA-IS scores must be integers","viaIs.fairness":"VIA-IS scores must be integers","viaIs.leadership":"VIA-IS scores must be integers","viaIs.forgiveness":"VIA-IS scores must be integers","viaIs.humility":"VIA-IS scores must be integers","viaIs.prudence":"VIA-IS scores must be integers","viaIs.selfRegulation":"VIA-IS scores must be integers","viaIs.appreciationOfBeauty":"VIA-IS scores must be integers","viaIs.gratitude":"VIA-IS scores must be integers","viaIs.hope":"VIA-IS scores must be integers","viaIs.humor":"VIA-IS scores must be integers"},"userId":"********-710f-4360-8dfa-4ce497f7e98f","url":"/assessments/submit"}
[2025-07-16T09:28:13.513Z] [INFO] ::1 - - [16/Jul/2025:09:28:13 +0000] "POST /assessments/submit HTTP/1.1" 400 1672 "-" "axios/1.10.0"
[2025-07-16T09:30:43.072Z] [INFO] ::1 - - [16/Jul/2025:09:30:43 +0000] "GET /health HTTP/1.1" 200 592 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-16T09:30:44.898Z] [INFO] Assessment submission received {"userId":"********-710f-4360-8dfa-4ce497f7e98f","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-16T09:30:44.913Z] [INFO] Token deduction successful {"userId":"********-710f-4360-8dfa-4ce497f7e98f","deductedAmount":1,"remainingBalance":2}
[2025-07-16T09:30:44.914Z] [INFO] Job created {"jobId":"9000c35a-afd1-4640-b27b-c41984feda9c","userId":"********-710f-4360-8dfa-4ce497f7e98f","userEmail":"<EMAIL>","status":"queued"}
[2025-07-16T09:30:44.916Z] [INFO] Assessment job published to queue {"jobId":"dbe17a47-1b38-4503-9f23-488238e3b413","userId":"********-710f-4360-8dfa-4ce497f7e98f","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-16T09:30:44.924Z] [INFO] ::1 - - [16/Jul/2025:09:30:44 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-16T09:30:45.944Z] [INFO] ::1 - - [16/Jul/2025:09:30:45 +0000] "GET /assessments/status/9000c35a-afd1-4640-b27b-c41984feda9c HTTP/1.1" 200 266 "-" "axios/1.10.0"
[2025-07-16T09:33:55.912Z] [INFO] Assessment submission received {"userId":"********-710f-4360-8dfa-4ce497f7e98f","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-16T09:33:55.923Z] [INFO] Token deduction successful {"userId":"********-710f-4360-8dfa-4ce497f7e98f","deductedAmount":1,"remainingBalance":1}
[2025-07-16T09:33:55.924Z] [INFO] Job created {"jobId":"756403f3-001c-4cdc-a371-************","userId":"********-710f-4360-8dfa-4ce497f7e98f","userEmail":"<EMAIL>","status":"queued"}
[2025-07-16T09:33:55.924Z] [INFO] Assessment job published to queue {"jobId":"de2861b0-907c-4337-8deb-95203f5ad28c","userId":"********-710f-4360-8dfa-4ce497f7e98f","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-16T09:33:55.927Z] [INFO] ::1 - - [16/Jul/2025:09:33:55 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-16T09:33:57.954Z] [INFO] ::1 - - [16/Jul/2025:09:33:57 +0000] "GET /assessments/status/756403f3-001c-4cdc-a371-************ HTTP/1.1" 200 266 "-" "axios/1.10.0"
[2025-07-16T09:34:02.097Z] [INFO] ::1 - - [16/Jul/2025:09:34:02 +0000] "GET /health HTTP/1.1" 200 592 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-16T09:34:02.123Z] [INFO] ::1 - - [16/Jul/2025:09:34:02 +0000] "GET /health HTTP/1.1" 200 592 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-16T11:58:48.525Z] [INFO] Assessment submission received {"userId":"********-710f-4360-8dfa-4ce497f7e98f","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-16T11:58:48.544Z] [INFO] Token deduction successful {"userId":"********-710f-4360-8dfa-4ce497f7e98f","deductedAmount":1,"remainingBalance":0}
[2025-07-16T11:58:48.545Z] [INFO] Job created {"jobId":"0024d442-aaff-4f57-970c-2955013f2595","userId":"********-710f-4360-8dfa-4ce497f7e98f","userEmail":"<EMAIL>","status":"queued"}
[2025-07-16T11:58:48.546Z] [INFO] Assessment job published to queue {"jobId":"********-149b-472b-9538-3383cda58b5f","userId":"********-710f-4360-8dfa-4ce497f7e98f","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-16T11:58:48.548Z] [INFO] ::1 - - [16/Jul/2025:11:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-16T11:58:50.572Z] [INFO] ::1 - - [16/Jul/2025:11:58:50 +0000] "GET /assessments/status/0024d442-aaff-4f57-970c-2955013f2595 HTTP/1.1" 200 266 "-" "axios/1.10.0"
[2025-07-16T11:58:54.729Z] [INFO] ::1 - - [16/Jul/2025:11:58:54 +0000] "GET /health HTTP/1.1" 200 593 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-16T11:58:54.755Z] [INFO] ::1 - - [16/Jul/2025:11:58:54 +0000] "GET /health HTTP/1.1" 200 593 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-16T16:26:02.495Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-16T16:26:02.538Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-16T16:26:02.538Z] [INFO] Queue service initialized successfully
[2025-07-16T16:26:02.539Z] [INFO] All services initialized successfully
[2025-07-16T16:26:02.543Z] [INFO] Assessment Service running on port 3003
[2025-07-16T16:26:02.543Z] [INFO] Environment: development
[2025-07-16T16:26:45.729Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-16T16:26:45.774Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-16T16:26:45.775Z] [INFO] Queue service initialized successfully
[2025-07-16T16:26:45.776Z] [INFO] All services initialized successfully
[2025-07-16T16:26:45.780Z] [INFO] Assessment Service running on port 3003
[2025-07-16T16:26:45.781Z] [INFO] Environment: development
[2025-07-16T22:44:43.719Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-16T22:44:43.765Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-16T22:44:43.766Z] [INFO] Queue service initialized successfully
[2025-07-16T22:44:43.766Z] [INFO] All services initialized successfully
[2025-07-16T22:44:43.770Z] [INFO] Assessment Service running on port 3003
[2025-07-16T22:44:43.770Z] [INFO] Environment: development
[2025-07-16T23:00:13.030Z] [INFO] ::1 - - [16/Jul/2025:23:00:13 +0000] "GET /health HTTP/1.1" 200 591 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-16T23:00:13.040Z] [INFO] ::1 - - [16/Jul/2025:23:00:13 +0000] "GET /health HTTP/1.1" 200 589 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-16T23:00:14.736Z] [INFO] ::1 - - [16/Jul/2025:23:00:14 +0000] "GET /health HTTP/1.1" 200 591 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-17T01:01:55.959Z] [INFO] Assessment submission received {"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-17T01:01:55.983Z] [INFO] Token deduction successful {"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63","deductedAmount":1,"remainingBalance":2}
[2025-07-17T01:01:55.984Z] [INFO] Job created {"jobId":"e3c009ea-37a5-4db3-ad16-cd3747fdbcad","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63","userEmail":"<EMAIL>","status":"queued"}
[2025-07-17T01:01:55.986Z] [INFO] Assessment job published to queue {"jobId":"b3108b24-edbc-4f93-b405-f9c556eb51d3","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-17T01:01:55.996Z] [INFO] ::1 - - [17/Jul/2025:01:01:55 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T01:01:56.053Z] [INFO] ::1 - - [17/Jul/2025:01:01:56 +0000] "GET /assessments/status/e3c009ea-37a5-4db3-ad16-cd3747fdbcad HTTP/1.1" 200 266 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T01:01:56.068Z] [INFO] ::1 - - [17/Jul/2025:01:01:56 +0000] "GET /assessments/status/e3c009ea-37a5-4db3-ad16-cd3747fdbcad HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T01:02:01.022Z] [INFO] ::1 - - [17/Jul/2025:01:02:01 +0000] "GET /assessments/status/e3c009ea-37a5-4db3-ad16-cd3747fdbcad HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T01:02:06.038Z] [INFO] ::1 - - [17/Jul/2025:01:02:06 +0000] "GET /assessments/status/e3c009ea-37a5-4db3-ad16-cd3747fdbcad HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T01:02:11.019Z] [INFO] ::1 - - [17/Jul/2025:01:02:11 +0000] "GET /assessments/status/e3c009ea-37a5-4db3-ad16-cd3747fdbcad HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T01:02:16.027Z] [INFO] ::1 - - [17/Jul/2025:01:02:16 +0000] "GET /assessments/status/e3c009ea-37a5-4db3-ad16-cd3747fdbcad HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T01:02:21.022Z] [INFO] ::1 - - [17/Jul/2025:01:02:21 +0000] "GET /assessments/status/e3c009ea-37a5-4db3-ad16-cd3747fdbcad HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T01:02:25.184Z] [INFO] ::1 - - [17/Jul/2025:01:02:25 +0000] "GET /assessments/status/e3c009ea-37a5-4db3-ad16-cd3747fdbcad HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T01:02:25.199Z] [INFO] ::1 - - [17/Jul/2025:01:02:25 +0000] "GET /assessments/status/e3c009ea-37a5-4db3-ad16-cd3747fdbcad HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T01:31:29.095Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T01:31:29.135Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-17T01:31:29.135Z] [INFO] Queue service initialized successfully
[2025-07-17T01:31:29.136Z] [INFO] All services initialized successfully
[2025-07-17T01:31:29.140Z] [INFO] Assessment Service running on port 3003
[2025-07-17T01:31:29.141Z] [INFO] Environment: development
[2025-07-17T01:35:12.350Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T01:35:12.379Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-17T01:35:12.379Z] [INFO] Queue service initialized successfully
[2025-07-17T01:35:12.380Z] [INFO] All services initialized successfully
[2025-07-17T01:35:12.382Z] [INFO] Assessment Service running on port 3003
[2025-07-17T01:35:12.382Z] [INFO] Environment: development
[2025-07-17T01:36:20.082Z] [INFO] Test assessment submission received {"userId":"test-user-*************","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs","multipleIntelligences","cognitiveStyleIndex"],"ip":"::1"}
[2025-07-17T01:36:20.083Z] [INFO] Job created {"jobId":"3708908e-587b-40a1-b1c8-98f790e0173a","userId":"test-user-*************","userEmail":"<EMAIL>","status":"queued"}
[2025-07-17T01:36:20.086Z] [INFO] Assessment job published to queue {"jobId":"********-f5cf-438a-9c0b-f8088f3dc608","userId":"test-user-*************","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-17T01:36:20.093Z] [INFO] ::1 - - [17/Jul/2025:01:36:20 +0000] "POST /test/submit HTTP/1.1" 202 341 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652"
[2025-07-17T01:48:11.859Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T01:48:11.888Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-17T01:48:11.888Z] [INFO] Queue service initialized successfully
[2025-07-17T01:48:11.889Z] [INFO] All services initialized successfully
[2025-07-17T01:48:11.891Z] [INFO] Assessment Service running on port 3003
[2025-07-17T01:48:11.891Z] [INFO] Environment: development
[2025-07-17T01:48:18.685Z] [WARN] Validation error {"source":"body","errors":{"riasec":"RIASEC assessment data is required","ocean":"OCEAN assessment data is required","viaIs":"VIA-IS assessment data is required","multipleIntelligences":"Multiple Intelligences assessment data is required","cognitiveStyleIndex":"Cognitive Style Index assessment data is required"},"url":"/test/submit"}
[2025-07-17T01:48:18.689Z] [INFO] ::1 - - [17/Jul/2025:01:48:18 +0000] "POST /test/submit HTTP/1.1" 400 381 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652"
[2025-07-17T01:48:27.720Z] [INFO] Test assessment submission received {"userId":"test-user-*************","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs","multipleIntelligences","cognitiveStyleIndex"],"ip":"::1"}
[2025-07-17T01:48:27.721Z] [INFO] Job created {"jobId":"7789597f-fa6c-42af-9ef7-cc94b1cf7bd0","userId":"test-user-*************","userEmail":"<EMAIL>","status":"queued"}
[2025-07-17T01:48:27.723Z] [INFO] Assessment job published to queue {"jobId":"********-041c-4180-89ea-f973bde1d5ce","userId":"test-user-*************","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-17T01:48:27.725Z] [INFO] ::1 - - [17/Jul/2025:01:48:27 +0000] "POST /test/submit HTTP/1.1" 202 341 "-" "Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652"
[2025-07-17T03:04:32.191Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T03:04:32.261Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-17T03:04:32.262Z] [INFO] Queue service initialized successfully
[2025-07-17T03:04:32.263Z] [INFO] All services initialized successfully
[2025-07-17T03:04:32.268Z] [INFO] Assessment Service running on port 3003
[2025-07-17T03:04:32.269Z] [INFO] Environment: development
[2025-07-17T03:19:49.652Z] [WARN] RabbitMQ connection closed
[2025-07-17T03:19:54.660Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-17T03:19:54.661Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T03:19:54.664Z] [ERROR] Failed to initialize RabbitMQ {"error":""}
[2025-07-17T03:19:54.664Z] [ERROR] Failed to reconnect to RabbitMQ {"error":""}
[2025-07-17T03:19:59.676Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-17T03:19:59.676Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T03:19:59.679Z] [ERROR] Failed to initialize RabbitMQ {"error":""}
[2025-07-17T03:19:59.680Z] [ERROR] Failed to reconnect to RabbitMQ {"error":""}
[2025-07-17T03:20:04.690Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-17T03:20:04.690Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T03:20:04.692Z] [ERROR] Failed to initialize RabbitMQ {"error":""}
[2025-07-17T03:20:04.692Z] [ERROR] Failed to reconnect to RabbitMQ {"error":""}
[2025-07-17T03:20:09.708Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-17T03:20:09.709Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T03:20:09.711Z] [ERROR] Failed to initialize RabbitMQ {"error":""}
[2025-07-17T03:20:09.711Z] [ERROR] Failed to reconnect to RabbitMQ {"error":""}
[2025-07-17T03:20:14.714Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-17T03:20:14.715Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T03:20:14.717Z] [ERROR] Failed to initialize RabbitMQ {"error":""}
[2025-07-17T03:20:14.717Z] [ERROR] Failed to reconnect to RabbitMQ {"error":""}
[2025-07-17T03:20:19.730Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-17T03:20:19.730Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T03:20:19.732Z] [ERROR] Failed to initialize RabbitMQ {"error":""}
[2025-07-17T03:20:19.733Z] [ERROR] Failed to reconnect to RabbitMQ {"error":""}
[2025-07-17T03:20:24.748Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Connection closed: 320 (CONNECTION-FORCED) with message \"CONNECTION_FORCED - broker forced connection closure with reason 'shutdown'\")"}
[2025-07-17T03:20:24.749Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T03:20:24.769Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-17T03:21:23.338Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T03:21:23.363Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-17T03:21:23.363Z] [INFO] Queue service initialized successfully
[2025-07-17T03:21:23.364Z] [INFO] All services initialized successfully
[2025-07-17T03:27:55.340Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T03:27:55.370Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-17T03:27:55.371Z] [INFO] Queue service initialized successfully
[2025-07-17T03:27:55.371Z] [INFO] All services initialized successfully
[2025-07-17T03:27:55.374Z] [INFO] Assessment Service running on port 3003
[2025-07-17T03:27:55.374Z] [INFO] Environment: development
[2025-07-17T03:30:25.032Z] [INFO] ::1 - - [17/Jul/2025:03:30:25 +0000] "POST /api/assessment/analyze HTTP/1.1" 404 103 "-" "axios/1.10.0"
[2025-07-17T03:31:01.473Z] [WARN] Validation error {"source":"body","errors":{"riasec":"RIASEC assessment data is required","ocean":"OCEAN assessment data is required","viaIs":"VIA-IS assessment data is required","multipleIntelligences":"Multiple Intelligences assessment data is required","cognitiveStyleIndex":"Cognitive Style Index assessment data is required"},"url":"/test/submit"}
[2025-07-17T03:31:01.474Z] [INFO] ::1 - - [17/Jul/2025:03:31:01 +0000] "POST /test/submit HTTP/1.1" 400 381 "-" "axios/1.10.0"
[2025-07-17T03:31:57.024Z] [INFO] Test assessment submission received {"userId":"test-user-*************","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs","multipleIntelligences","cognitiveStyleIndex"],"ip":"::1"}
[2025-07-17T03:31:57.024Z] [INFO] Job created {"jobId":"310c97a6-2f28-4249-a5cf-7088bc11284d","userId":"test-user-*************","userEmail":"<EMAIL>","status":"queued"}
[2025-07-17T03:31:57.026Z] [INFO] Assessment job published to queue {"jobId":"19cb1e75-f1a9-4e6c-9242-05038ad0fc58","userId":"test-user-*************","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-17T03:31:57.028Z] [INFO] ::1 - - [17/Jul/2025:03:31:57 +0000] "POST /test/submit HTTP/1.1" 202 341 "-" "axios/1.10.0"
[2025-07-17T03:33:25.988Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T03:33:26.015Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-17T03:33:26.015Z] [INFO] Queue service initialized successfully
[2025-07-17T03:33:26.015Z] [INFO] All services initialized successfully
[2025-07-17T03:33:26.018Z] [INFO] Assessment Service running on port 3003
[2025-07-17T03:33:26.018Z] [INFO] Environment: development
[2025-07-17T03:33:48.755Z] [INFO] Test assessment submission received {"userId":"a976e5bc-2074-42de-854b-84f52293233f","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs","multipleIntelligences","cognitiveStyleIndex"],"ip":"::1"}
[2025-07-17T03:33:48.755Z] [INFO] Job created {"jobId":"4efbb3b2-10a0-445e-89b4-dfa108741232","userId":"a976e5bc-2074-42de-854b-84f52293233f","userEmail":"<EMAIL>","status":"queued"}
[2025-07-17T03:33:48.757Z] [INFO] Assessment job published to queue {"jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","userId":"a976e5bc-2074-42de-854b-84f52293233f","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-17T03:33:48.763Z] [INFO] ::1 - - [17/Jul/2025:03:33:48 +0000] "POST /test/submit HTTP/1.1" 202 354 "-" "axios/1.10.0"
[2025-07-17T03:42:58.606Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T03:42:58.647Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-17T03:42:58.648Z] [INFO] Queue service initialized successfully
[2025-07-17T03:42:58.648Z] [INFO] All services initialized successfully
[2025-07-17T03:42:58.652Z] [INFO] Assessment Service running on port 3003
[2025-07-17T03:42:58.653Z] [INFO] Environment: development
[2025-07-17T03:47:33.760Z] [WARN] Validation error {"source":"body","errors":{"multipleIntelligences":"Multiple Intelligences assessment data is required","cognitiveStyleIndex":"Cognitive Style Index assessment data is required"},"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63","url":"/assessments/submit"}
[2025-07-17T03:47:33.764Z] [INFO] ::1 - - [17/Jul/2025:03:47:33 +0000] "POST /assessments/submit HTTP/1.1" 400 246 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T03:49:39.676Z] [WARN] Validation error {"source":"body","errors":{"multipleIntelligences":"Multiple Intelligences assessment data is required","cognitiveStyleIndex":"Cognitive Style Index assessment data is required"},"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63","url":"/assessments/submit"}
[2025-07-17T03:49:39.677Z] [INFO] ::1 - - [17/Jul/2025:03:49:39 +0000] "POST /assessments/submit HTTP/1.1" 400 246 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T04:00:12.609Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T04:00:12.657Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-17T04:00:12.658Z] [INFO] Queue service initialized successfully
[2025-07-17T04:00:12.658Z] [INFO] All services initialized successfully
[2025-07-17T04:00:40.249Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T04:00:40.287Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-17T04:00:40.288Z] [INFO] Queue service initialized successfully
[2025-07-17T04:00:40.288Z] [INFO] All services initialized successfully
[2025-07-17T04:00:40.291Z] [INFO] Assessment Service running on port 3003
[2025-07-17T04:00:40.291Z] [INFO] Environment: development
[2025-07-17T04:02:39.568Z] [INFO] Test assessment submission received {"userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-17T04:02:39.568Z] [INFO] Job created {"jobId":"dfb81f3b-a509-47fb-bf14-c7a99bff8409","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","userEmail":"<EMAIL>","status":"queued"}
[2025-07-17T04:02:39.571Z] [INFO] Assessment job published to queue {"jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-17T04:02:39.577Z] [INFO] ::1 - - [17/Jul/2025:04:02:39 +0000] "POST /test/submit HTTP/1.1" 202 354 "-" "axios/1.10.0"
[2025-07-17T04:12:55.559Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T04:12:55.586Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-17T04:12:55.586Z] [INFO] Queue service initialized successfully
[2025-07-17T04:12:55.586Z] [INFO] All services initialized successfully
[2025-07-17T04:12:55.589Z] [INFO] Assessment Service running on port 3003
[2025-07-17T04:12:55.589Z] [INFO] Environment: development
[2025-07-17T04:14:23.018Z] [INFO] Test assessment submission received {"userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-17T04:14:23.018Z] [INFO] Job created {"jobId":"bbf87e5d-da94-47ea-8e43-322c39e1ad26","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","userEmail":"<EMAIL>","status":"queued"}
[2025-07-17T04:14:23.020Z] [INFO] Assessment job published to queue {"jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-17T04:14:23.026Z] [INFO] ::1 - - [17/Jul/2025:04:14:23 +0000] "POST /test/submit HTTP/1.1" 202 354 "-" "axios/1.10.0"
[2025-07-17T04:27:53.076Z] [INFO] Test assessment submission received {"userId":"e9034651-8aa9-471b-97c7-71babc1ac589","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-17T04:27:53.077Z] [INFO] Job created {"jobId":"33aca03b-df0f-4549-bfae-87fda808c3c4","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","userEmail":"<EMAIL>","status":"queued"}
[2025-07-17T04:27:53.078Z] [INFO] Assessment job published to queue {"jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-17T04:27:53.080Z] [INFO] ::1 - - [17/Jul/2025:04:27:53 +0000] "POST /test/submit HTTP/1.1" 202 354 "-" "axios/1.10.0"
[2025-07-17T04:40:05.573Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T04:40:05.616Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-17T04:40:05.616Z] [INFO] Queue service initialized successfully
[2025-07-17T04:40:05.617Z] [INFO] All services initialized successfully
[2025-07-17T04:40:22.600Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T04:40:22.637Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-17T04:40:22.638Z] [INFO] Queue service initialized successfully
[2025-07-17T04:40:22.639Z] [INFO] All services initialized successfully
[2025-07-17T04:40:22.643Z] [INFO] Assessment Service running on port 3003
[2025-07-17T04:40:22.643Z] [INFO] Environment: development
[2025-07-17T05:05:08.478Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T05:05:08.517Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-17T05:05:08.517Z] [INFO] Queue service initialized successfully
[2025-07-17T05:05:08.518Z] [INFO] All services initialized successfully
[2025-07-17T05:05:08.522Z] [INFO] Assessment Service running on port 3003
[2025-07-17T05:05:08.522Z] [INFO] Environment: development
[2025-07-17T05:13:16.498Z] [INFO] Assessment submission received {"userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-17T05:13:16.523Z] [INFO] Token deduction successful {"userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","deductedAmount":1,"remainingBalance":2}
[2025-07-17T05:13:16.524Z] [INFO] Job created {"jobId":"5ae4ad92-a815-4963-ad16-268637412a08","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","userEmail":"<EMAIL>","status":"queued"}
[2025-07-17T05:13:16.526Z] [INFO] Assessment job published to queue {"jobId":"3797105b-a4ac-436d-b2c8-9d812d06f52d","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-17T05:13:16.530Z] [INFO] ::1 - - [17/Jul/2025:05:13:16 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:13:16.586Z] [INFO] ::1 - - [17/Jul/2025:05:13:16 +0000] "GET /assessments/status/5ae4ad92-a815-4963-ad16-268637412a08 HTTP/1.1" 200 266 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:13:16.599Z] [INFO] ::1 - - [17/Jul/2025:05:13:16 +0000] "GET /assessments/status/5ae4ad92-a815-4963-ad16-268637412a08 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:13:21.582Z] [INFO] ::1 - - [17/Jul/2025:05:13:21 +0000] "GET /assessments/status/5ae4ad92-a815-4963-ad16-268637412a08 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:13:26.584Z] [INFO] ::1 - - [17/Jul/2025:05:13:26 +0000] "GET /assessments/status/5ae4ad92-a815-4963-ad16-268637412a08 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:13:31.580Z] [INFO] ::1 - - [17/Jul/2025:05:13:31 +0000] "GET /assessments/status/5ae4ad92-a815-4963-ad16-268637412a08 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:13:36.584Z] [INFO] ::1 - - [17/Jul/2025:05:13:36 +0000] "GET /assessments/status/5ae4ad92-a815-4963-ad16-268637412a08 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:13:41.579Z] [INFO] ::1 - - [17/Jul/2025:05:13:41 +0000] "GET /assessments/status/5ae4ad92-a815-4963-ad16-268637412a08 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:13:46.585Z] [INFO] ::1 - - [17/Jul/2025:05:13:46 +0000] "GET /assessments/status/5ae4ad92-a815-4963-ad16-268637412a08 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:13:51.592Z] [INFO] ::1 - - [17/Jul/2025:05:13:51 +0000] "GET /assessments/status/5ae4ad92-a815-4963-ad16-268637412a08 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:13:56.581Z] [INFO] ::1 - - [17/Jul/2025:05:13:56 +0000] "GET /assessments/status/5ae4ad92-a815-4963-ad16-268637412a08 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:14:01.581Z] [INFO] ::1 - - [17/Jul/2025:05:14:01 +0000] "GET /assessments/status/5ae4ad92-a815-4963-ad16-268637412a08 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:22:13.494Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T05:22:13.549Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-17T05:22:13.550Z] [INFO] Queue service initialized successfully
[2025-07-17T05:22:13.550Z] [INFO] All services initialized successfully
[2025-07-17T05:22:13.555Z] [INFO] Assessment Service running on port 3003
[2025-07-17T05:22:13.555Z] [INFO] Environment: development
[2025-07-17T05:22:48.361Z] [INFO] Assessment submission received {"userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-17T05:22:48.391Z] [INFO] Token deduction successful {"userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","deductedAmount":1,"remainingBalance":1}
[2025-07-17T05:22:48.392Z] [INFO] Job created {"jobId":"cf24392d-c8e0-466a-98c2-7c0459953605","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","userEmail":"<EMAIL>","status":"queued"}
[2025-07-17T05:22:48.394Z] [INFO] Assessment job published to queue {"jobId":"048dd4a0-f14b-4c7b-819e-853e946aeebb","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-17T05:22:48.398Z] [INFO] ::1 - - [17/Jul/2025:05:22:48 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:22:48.474Z] [INFO] ::1 - - [17/Jul/2025:05:22:48 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 200 266 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:22:48.491Z] [INFO] ::1 - - [17/Jul/2025:05:22:48 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:22:53.456Z] [INFO] ::1 - - [17/Jul/2025:05:22:53 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:22:58.462Z] [INFO] ::1 - - [17/Jul/2025:05:22:58 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:23:03.457Z] [INFO] ::1 - - [17/Jul/2025:05:23:03 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:23:08.467Z] [INFO] ::1 - - [17/Jul/2025:05:23:08 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:23:13.454Z] [INFO] ::1 - - [17/Jul/2025:05:23:13 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:23:18.462Z] [INFO] ::1 - - [17/Jul/2025:05:23:18 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:23:23.453Z] [INFO] ::1 - - [17/Jul/2025:05:23:23 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:23:28.460Z] [INFO] ::1 - - [17/Jul/2025:05:23:28 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:23:33.455Z] [INFO] ::1 - - [17/Jul/2025:05:23:33 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:23:38.459Z] [INFO] ::1 - - [17/Jul/2025:05:23:38 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:23:44.094Z] [INFO] ::1 - - [17/Jul/2025:05:23:44 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:23:49.088Z] [INFO] ::1 - - [17/Jul/2025:05:23:49 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:23:54.101Z] [INFO] ::1 - - [17/Jul/2025:05:23:54 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:23:59.094Z] [INFO] ::1 - - [17/Jul/2025:05:23:59 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:24:04.101Z] [INFO] ::1 - - [17/Jul/2025:05:24:04 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:24:09.087Z] [INFO] ::1 - - [17/Jul/2025:05:24:09 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:24:14.103Z] [INFO] ::1 - - [17/Jul/2025:05:24:14 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:24:19.092Z] [INFO] ::1 - - [17/Jul/2025:05:24:19 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:24:24.100Z] [INFO] ::1 - - [17/Jul/2025:05:24:24 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:24:29.087Z] [INFO] ::1 - - [17/Jul/2025:05:24:29 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:24:34.100Z] [INFO] ::1 - - [17/Jul/2025:05:24:34 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:24:39.087Z] [INFO] ::1 - - [17/Jul/2025:05:24:39 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:24:44.092Z] [INFO] ::1 - - [17/Jul/2025:05:24:44 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:24:49.098Z] [INFO] ::1 - - [17/Jul/2025:05:24:49 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:24:54.094Z] [INFO] ::1 - - [17/Jul/2025:05:24:54 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:24:59.096Z] [INFO] ::1 - - [17/Jul/2025:05:24:59 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:25:04.090Z] [INFO] ::1 - - [17/Jul/2025:05:25:04 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:25:06.124Z] [INFO] ::1 - - [17/Jul/2025:05:25:06 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:25:12.094Z] [INFO] ::1 - - [17/Jul/2025:05:25:12 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:25:17.087Z] [INFO] ::1 - - [17/Jul/2025:05:25:17 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:25:22.101Z] [INFO] ::1 - - [17/Jul/2025:05:25:22 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:25:24.107Z] [INFO] ::1 - - [17/Jul/2025:05:25:24 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:25:24.125Z] [INFO] ::1 - - [17/Jul/2025:05:25:24 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:25:27.096Z] [INFO] ::1 - - [17/Jul/2025:05:25:27 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:25:30.100Z] [INFO] ::1 - - [17/Jul/2025:05:25:30 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:25:32.097Z] [INFO] ::1 - - [17/Jul/2025:05:25:32 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:25:35.101Z] [INFO] ::1 - - [17/Jul/2025:05:25:35 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:25:40.092Z] [INFO] ::1 - - [17/Jul/2025:05:25:40 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:25:45.109Z] [INFO] ::1 - - [17/Jul/2025:05:25:45 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:25:47.085Z] [INFO] ::1 - - [17/Jul/2025:05:25:47 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:25:50.089Z] [INFO] ::1 - - [17/Jul/2025:05:25:50 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:25:55.093Z] [INFO] ::1 - - [17/Jul/2025:05:25:55 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:26:47.147Z] [INFO] ::1 - - [17/Jul/2025:05:26:47 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:26:47.162Z] [INFO] ::1 - - [17/Jul/2025:05:26:47 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:27:47.232Z] [INFO] ::1 - - [17/Jul/2025:05:27:47 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:27:47.244Z] [INFO] ::1 - - [17/Jul/2025:05:27:47 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:28:00.333Z] [INFO] ::1 - - [17/Jul/2025:05:28:00 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:28:01.532Z] [INFO] ::1 - - [17/Jul/2025:05:28:01 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 200 266 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:28:01.551Z] [INFO] ::1 - - [17/Jul/2025:05:28:01 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:28:06.523Z] [INFO] ::1 - - [17/Jul/2025:05:28:06 +0000] "GET /assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:31:22.286Z] [INFO] Assessment submission received {"userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-17T05:31:22.301Z] [INFO] Token deduction successful {"userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","deductedAmount":1,"remainingBalance":0}
[2025-07-17T05:31:22.301Z] [INFO] Job created {"jobId":"24ae32ac-30e4-433f-a9d2-3429d8365bbf","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","userEmail":"<EMAIL>","status":"queued"}
[2025-07-17T05:31:22.302Z] [INFO] Assessment job published to queue {"jobId":"20e4119c-4ebe-4126-85d1-61c3ff723190","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-17T05:31:22.305Z] [INFO] ::1 - - [17/Jul/2025:05:31:22 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:31:22.382Z] [INFO] ::1 - - [17/Jul/2025:05:31:22 +0000] "GET /assessments/status/24ae32ac-30e4-433f-a9d2-3429d8365bbf HTTP/1.1" 200 266 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:31:22.397Z] [INFO] ::1 - - [17/Jul/2025:05:31:22 +0000] "GET /assessments/status/24ae32ac-30e4-433f-a9d2-3429d8365bbf HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:31:27.554Z] [INFO] ::1 - - [17/Jul/2025:05:31:27 +0000] "GET /assessments/status/24ae32ac-30e4-433f-a9d2-3429d8365bbf HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:31:32.374Z] [INFO] ::1 - - [17/Jul/2025:05:31:32 +0000] "GET /assessments/status/24ae32ac-30e4-433f-a9d2-3429d8365bbf HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:31:37.364Z] [INFO] ::1 - - [17/Jul/2025:05:31:37 +0000] "GET /assessments/status/24ae32ac-30e4-433f-a9d2-3429d8365bbf HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:31:42.363Z] [INFO] ::1 - - [17/Jul/2025:05:31:42 +0000] "GET /assessments/status/24ae32ac-30e4-433f-a9d2-3429d8365bbf HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T05:31:47.364Z] [INFO] ::1 - - [17/Jul/2025:05:31:47 +0000] "GET /assessments/status/24ae32ac-30e4-433f-a9d2-3429d8365bbf HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T11:15:26.292Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T11:15:26.346Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-17T11:15:26.347Z] [INFO] Queue service initialized successfully
[2025-07-17T11:15:26.348Z] [INFO] All services initialized successfully
[2025-07-17T11:15:26.353Z] [INFO] Assessment Service running on port 3003
[2025-07-17T11:15:26.354Z] [INFO] Environment: development
[2025-07-17T11:23:12.100Z] [INFO] Assessment submission received {"userId":"a8be340d-ff32-4113-999e-60f05d47c6b3","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-17T11:23:12.122Z] [INFO] Token deduction successful {"userId":"a8be340d-ff32-4113-999e-60f05d47c6b3","deductedAmount":1,"remainingBalance":2}
[2025-07-17T11:23:12.124Z] [INFO] Job created {"jobId":"fd21fdca-3b08-4b17-a0c1-011b783d263b","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3","userEmail":"<EMAIL>","status":"queued"}
[2025-07-17T11:23:12.126Z] [INFO] Assessment job published to queue {"jobId":"3b303a96-5d4e-43b2-bc9a-98cbfe005d97","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-17T11:23:12.136Z] [INFO] ::1 - - [17/Jul/2025:11:23:12 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T11:23:12.228Z] [INFO] ::1 - - [17/Jul/2025:11:23:12 +0000] "GET /assessments/status/fd21fdca-3b08-4b17-a0c1-011b783d263b HTTP/1.1" 200 266 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T11:23:12.248Z] [INFO] ::1 - - [17/Jul/2025:11:23:12 +0000] "GET /assessments/status/fd21fdca-3b08-4b17-a0c1-011b783d263b HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T11:23:17.207Z] [INFO] ::1 - - [17/Jul/2025:11:23:17 +0000] "GET /assessments/status/fd21fdca-3b08-4b17-a0c1-011b783d263b HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T11:23:22.208Z] [INFO] ::1 - - [17/Jul/2025:11:23:22 +0000] "GET /assessments/status/fd21fdca-3b08-4b17-a0c1-011b783d263b HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T11:23:27.206Z] [INFO] ::1 - - [17/Jul/2025:11:23:27 +0000] "GET /assessments/status/fd21fdca-3b08-4b17-a0c1-011b783d263b HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T11:23:32.209Z] [INFO] ::1 - - [17/Jul/2025:11:23:32 +0000] "GET /assessments/status/fd21fdca-3b08-4b17-a0c1-011b783d263b HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T11:23:37.207Z] [INFO] ::1 - - [17/Jul/2025:11:23:37 +0000] "GET /assessments/status/fd21fdca-3b08-4b17-a0c1-011b783d263b HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T11:26:39.221Z] [INFO] Assessment submission received {"userId":"a8be340d-ff32-4113-999e-60f05d47c6b3","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-17T11:26:39.242Z] [INFO] Token deduction successful {"userId":"a8be340d-ff32-4113-999e-60f05d47c6b3","deductedAmount":1,"remainingBalance":1}
[2025-07-17T11:26:39.242Z] [INFO] Job created {"jobId":"1aaaeb17-041b-4b62-9105-4d94c195bdfe","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3","userEmail":"<EMAIL>","status":"queued"}
[2025-07-17T11:26:39.243Z] [INFO] Assessment job published to queue {"jobId":"f9d21443-18ed-4f00-ad1e-769e1dd0e7d5","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-17T11:26:39.247Z] [INFO] ::1 - - [17/Jul/2025:11:26:39 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T11:26:39.343Z] [INFO] ::1 - - [17/Jul/2025:11:26:39 +0000] "GET /assessments/status/1aaaeb17-041b-4b62-9105-4d94c195bdfe HTTP/1.1" 200 266 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T11:26:39.360Z] [INFO] ::1 - - [17/Jul/2025:11:26:39 +0000] "GET /assessments/status/1aaaeb17-041b-4b62-9105-4d94c195bdfe HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T12:31:29.066Z] [INFO] Assessment submission received {"userId":"a8be340d-ff32-4113-999e-60f05d47c6b3","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-17T12:31:29.089Z] [INFO] Token deduction successful {"userId":"a8be340d-ff32-4113-999e-60f05d47c6b3","deductedAmount":1,"remainingBalance":0}
[2025-07-17T12:31:29.089Z] [INFO] Job created {"jobId":"8cf30219-ff08-486d-a412-2547e89ce80e","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3","userEmail":"<EMAIL>","status":"queued"}
[2025-07-17T12:31:29.090Z] [INFO] Assessment job published to queue {"jobId":"a606143b-1c09-4427-a471-8a5d16d6fcfe","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-17T12:31:29.095Z] [INFO] ::1 - - [17/Jul/2025:12:31:29 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T12:31:29.177Z] [INFO] ::1 - - [17/Jul/2025:12:31:29 +0000] "GET /assessments/status/8cf30219-ff08-486d-a412-2547e89ce80e HTTP/1.1" 200 266 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T12:31:29.191Z] [INFO] ::1 - - [17/Jul/2025:12:31:29 +0000] "GET /assessments/status/8cf30219-ff08-486d-a412-2547e89ce80e HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T12:31:34.157Z] [INFO] ::1 - - [17/Jul/2025:12:31:34 +0000] "GET /assessments/status/8cf30219-ff08-486d-a412-2547e89ce80e HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T12:31:39.172Z] [INFO] ::1 - - [17/Jul/2025:12:31:39 +0000] "GET /assessments/status/8cf30219-ff08-486d-a412-2547e89ce80e HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T12:31:44.158Z] [INFO] ::1 - - [17/Jul/2025:12:31:44 +0000] "GET /assessments/status/8cf30219-ff08-486d-a412-2547e89ce80e HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T12:31:49.166Z] [INFO] ::1 - - [17/Jul/2025:12:31:49 +0000] "GET /assessments/status/8cf30219-ff08-486d-a412-2547e89ce80e HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T12:31:54.157Z] [INFO] ::1 - - [17/Jul/2025:12:31:54 +0000] "GET /assessments/status/8cf30219-ff08-486d-a412-2547e89ce80e HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T12:31:59.168Z] [INFO] ::1 - - [17/Jul/2025:12:31:59 +0000] "GET /assessments/status/8cf30219-ff08-486d-a412-2547e89ce80e HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T12:32:04.456Z] [INFO] ::1 - - [17/Jul/2025:12:32:04 +0000] "GET /assessments/status/8cf30219-ff08-486d-a412-2547e89ce80e HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T12:32:09.459Z] [INFO] ::1 - - [17/Jul/2025:12:32:09 +0000] "GET /assessments/status/8cf30219-ff08-486d-a412-2547e89ce80e HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T12:32:14.446Z] [INFO] ::1 - - [17/Jul/2025:12:32:14 +0000] "GET /assessments/status/8cf30219-ff08-486d-a412-2547e89ce80e HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T12:32:19.460Z] [INFO] ::1 - - [17/Jul/2025:12:32:19 +0000] "GET /assessments/status/8cf30219-ff08-486d-a412-2547e89ce80e HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T12:32:24.448Z] [INFO] ::1 - - [17/Jul/2025:12:32:24 +0000] "GET /assessments/status/8cf30219-ff08-486d-a412-2547e89ce80e HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T12:32:29.457Z] [INFO] ::1 - - [17/Jul/2025:12:32:29 +0000] "GET /assessments/status/8cf30219-ff08-486d-a412-2547e89ce80e HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T12:34:32.457Z] [INFO] Assessment submission received {"userId":"4bca5142-e6b6-4b37-8d65-3020736087a8","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-17T12:34:32.470Z] [INFO] Token deduction successful {"userId":"4bca5142-e6b6-4b37-8d65-3020736087a8","deductedAmount":1,"remainingBalance":2}
[2025-07-17T12:34:32.471Z] [INFO] Job created {"jobId":"be8c6ad5-944b-4868-a6e1-61f690f50227","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8","userEmail":"<EMAIL>","status":"queued"}
[2025-07-17T12:34:32.472Z] [INFO] Assessment job published to queue {"jobId":"c15eb831-74ae-4906-9c20-cc6effa73c4a","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-17T12:34:32.474Z] [INFO] ::1 - - [17/Jul/2025:12:34:32 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T12:34:32.561Z] [INFO] ::1 - - [17/Jul/2025:12:34:32 +0000] "GET /assessments/status/be8c6ad5-944b-4868-a6e1-61f690f50227 HTTP/1.1" 200 266 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T12:34:32.577Z] [INFO] ::1 - - [17/Jul/2025:12:34:32 +0000] "GET /assessments/status/be8c6ad5-944b-4868-a6e1-61f690f50227 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T12:34:37.546Z] [INFO] ::1 - - [17/Jul/2025:12:34:37 +0000] "GET /assessments/status/be8c6ad5-944b-4868-a6e1-61f690f50227 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T12:34:42.548Z] [INFO] ::1 - - [17/Jul/2025:12:34:42 +0000] "GET /assessments/status/be8c6ad5-944b-4868-a6e1-61f690f50227 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T12:34:47.540Z] [INFO] ::1 - - [17/Jul/2025:12:34:47 +0000] "GET /assessments/status/be8c6ad5-944b-4868-a6e1-61f690f50227 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T12:34:52.551Z] [INFO] ::1 - - [17/Jul/2025:12:34:52 +0000] "GET /assessments/status/be8c6ad5-944b-4868-a6e1-61f690f50227 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:27:31.734Z] [INFO] Assessment submission received {"userId":"4bca5142-e6b6-4b37-8d65-3020736087a8","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-17T13:27:31.753Z] [INFO] Token deduction successful {"userId":"4bca5142-e6b6-4b37-8d65-3020736087a8","deductedAmount":1,"remainingBalance":1}
[2025-07-17T13:27:31.754Z] [INFO] Job created {"jobId":"22c591c3-5e73-4891-bb5f-4a45f601ef29","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8","userEmail":"<EMAIL>","status":"queued"}
[2025-07-17T13:27:31.754Z] [INFO] Assessment job published to queue {"jobId":"********-73f4-4bfe-8241-8b0e54829cda","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-17T13:27:31.760Z] [INFO] ::1 - - [17/Jul/2025:13:27:31 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:27:31.839Z] [INFO] ::1 - - [17/Jul/2025:13:27:31 +0000] "GET /assessments/status/22c591c3-5e73-4891-bb5f-4a45f601ef29 HTTP/1.1" 200 266 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:27:31.855Z] [INFO] ::1 - - [17/Jul/2025:13:27:31 +0000] "GET /assessments/status/22c591c3-5e73-4891-bb5f-4a45f601ef29 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:27:36.830Z] [INFO] ::1 - - [17/Jul/2025:13:27:36 +0000] "GET /assessments/status/22c591c3-5e73-4891-bb5f-4a45f601ef29 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:27:41.827Z] [INFO] ::1 - - [17/Jul/2025:13:27:41 +0000] "GET /assessments/status/22c591c3-5e73-4891-bb5f-4a45f601ef29 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:27:46.827Z] [INFO] ::1 - - [17/Jul/2025:13:27:46 +0000] "GET /assessments/status/22c591c3-5e73-4891-bb5f-4a45f601ef29 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:27:51.834Z] [INFO] ::1 - - [17/Jul/2025:13:27:51 +0000] "GET /assessments/status/22c591c3-5e73-4891-bb5f-4a45f601ef29 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:27:56.825Z] [INFO] ::1 - - [17/Jul/2025:13:27:56 +0000] "GET /assessments/status/22c591c3-5e73-4891-bb5f-4a45f601ef29 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:28:01.932Z] [INFO] ::1 - - [17/Jul/2025:13:28:01 +0000] "GET /assessments/status/22c591c3-5e73-4891-bb5f-4a45f601ef29 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:28:06.828Z] [INFO] ::1 - - [17/Jul/2025:13:28:06 +0000] "GET /assessments/status/22c591c3-5e73-4891-bb5f-4a45f601ef29 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:28:11.847Z] [INFO] ::1 - - [17/Jul/2025:13:28:11 +0000] "GET /assessments/status/22c591c3-5e73-4891-bb5f-4a45f601ef29 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:28:33.395Z] [INFO] Assessment submission received {"userId":"4bca5142-e6b6-4b37-8d65-3020736087a8","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-17T13:28:33.409Z] [INFO] Token deduction successful {"userId":"4bca5142-e6b6-4b37-8d65-3020736087a8","deductedAmount":1,"remainingBalance":0}
[2025-07-17T13:28:33.409Z] [INFO] Job created {"jobId":"82ebf263-9428-447b-88be-3147dbc016b0","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8","userEmail":"<EMAIL>","status":"queued"}
[2025-07-17T13:28:33.410Z] [INFO] Assessment job published to queue {"jobId":"b54cdc93-aa62-4532-aa66-6bed40fc822f","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-17T13:28:33.413Z] [INFO] ::1 - - [17/Jul/2025:13:28:33 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:28:33.525Z] [INFO] ::1 - - [17/Jul/2025:13:28:33 +0000] "GET /assessments/status/82ebf263-9428-447b-88be-3147dbc016b0 HTTP/1.1" 200 266 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:28:33.538Z] [INFO] ::1 - - [17/Jul/2025:13:28:33 +0000] "GET /assessments/status/82ebf263-9428-447b-88be-3147dbc016b0 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:28:38.509Z] [INFO] ::1 - - [17/Jul/2025:13:28:38 +0000] "GET /assessments/status/82ebf263-9428-447b-88be-3147dbc016b0 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:28:43.505Z] [INFO] ::1 - - [17/Jul/2025:13:28:43 +0000] "GET /assessments/status/82ebf263-9428-447b-88be-3147dbc016b0 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:28:48.502Z] [INFO] ::1 - - [17/Jul/2025:13:28:48 +0000] "GET /assessments/status/82ebf263-9428-447b-88be-3147dbc016b0 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:28:53.504Z] [INFO] ::1 - - [17/Jul/2025:13:28:53 +0000] "GET /assessments/status/82ebf263-9428-447b-88be-3147dbc016b0 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:28:58.504Z] [INFO] ::1 - - [17/Jul/2025:13:28:58 +0000] "GET /assessments/status/82ebf263-9428-447b-88be-3147dbc016b0 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:29:03.505Z] [INFO] ::1 - - [17/Jul/2025:13:29:03 +0000] "GET /assessments/status/82ebf263-9428-447b-88be-3147dbc016b0 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:29:08.510Z] [INFO] ::1 - - [17/Jul/2025:13:29:08 +0000] "GET /assessments/status/82ebf263-9428-447b-88be-3147dbc016b0 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:29:13.504Z] [INFO] ::1 - - [17/Jul/2025:13:29:13 +0000] "GET /assessments/status/82ebf263-9428-447b-88be-3147dbc016b0 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:29:18.504Z] [INFO] ::1 - - [17/Jul/2025:13:29:18 +0000] "GET /assessments/status/82ebf263-9428-447b-88be-3147dbc016b0 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:29:24.445Z] [INFO] ::1 - - [17/Jul/2025:13:29:24 +0000] "GET /assessments/status/82ebf263-9428-447b-88be-3147dbc016b0 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:29:29.454Z] [INFO] ::1 - - [17/Jul/2025:13:29:29 +0000] "GET /assessments/status/82ebf263-9428-447b-88be-3147dbc016b0 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:29:34.440Z] [INFO] ::1 - - [17/Jul/2025:13:29:34 +0000] "GET /assessments/status/82ebf263-9428-447b-88be-3147dbc016b0 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:29:38.510Z] [INFO] ::1 - - [17/Jul/2025:13:29:38 +0000] "GET /assessments/status/82ebf263-9428-447b-88be-3147dbc016b0 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:29:39.644Z] [INFO] ::1 - - [17/Jul/2025:13:29:39 +0000] "GET /assessments/status/82ebf263-9428-447b-88be-3147dbc016b0 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:29:44.650Z] [INFO] ::1 - - [17/Jul/2025:13:29:44 +0000] "GET /assessments/status/82ebf263-9428-447b-88be-3147dbc016b0 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:29:49.648Z] [INFO] ::1 - - [17/Jul/2025:13:29:49 +0000] "GET /assessments/status/82ebf263-9428-447b-88be-3147dbc016b0 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:29:52.399Z] [INFO] ::1 - - [17/Jul/2025:13:29:52 +0000] "GET /assessments/status/82ebf263-9428-447b-88be-3147dbc016b0 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:33:46.312Z] [INFO] Assessment submission received {"userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-17T13:33:46.328Z] [INFO] Token deduction successful {"userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc","deductedAmount":1,"remainingBalance":2}
[2025-07-17T13:33:46.329Z] [INFO] Job created {"jobId":"830de7aa-050e-4ff5-9595-04939f6fbc83","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc","userEmail":"<EMAIL>","status":"queued"}
[2025-07-17T13:33:46.330Z] [INFO] Assessment job published to queue {"jobId":"11565c0e-1580-47b1-82c0-c4a653a80db3","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-17T13:33:46.332Z] [INFO] ::1 - - [17/Jul/2025:13:33:46 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:33:46.410Z] [INFO] ::1 - - [17/Jul/2025:13:33:46 +0000] "GET /assessments/status/830de7aa-050e-4ff5-9595-04939f6fbc83 HTTP/1.1" 200 266 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:33:46.425Z] [INFO] ::1 - - [17/Jul/2025:13:33:46 +0000] "GET /assessments/status/830de7aa-050e-4ff5-9595-04939f6fbc83 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:33:51.398Z] [INFO] ::1 - - [17/Jul/2025:13:33:51 +0000] "GET /assessments/status/830de7aa-050e-4ff5-9595-04939f6fbc83 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:33:56.401Z] [INFO] ::1 - - [17/Jul/2025:13:33:56 +0000] "GET /assessments/status/830de7aa-050e-4ff5-9595-04939f6fbc83 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:34:01.396Z] [INFO] ::1 - - [17/Jul/2025:13:34:01 +0000] "GET /assessments/status/830de7aa-050e-4ff5-9595-04939f6fbc83 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:34:05.235Z] [INFO] ::1 - - [17/Jul/2025:13:34:05 +0000] "GET /assessments/status/830de7aa-050e-4ff5-9595-04939f6fbc83 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:34:06.395Z] [INFO] ::1 - - [17/Jul/2025:13:34:06 +0000] "GET /assessments/status/830de7aa-050e-4ff5-9595-04939f6fbc83 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:34:11.400Z] [INFO] ::1 - - [17/Jul/2025:13:34:11 +0000] "GET /assessments/status/830de7aa-050e-4ff5-9595-04939f6fbc83 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:34:16.398Z] [INFO] ::1 - - [17/Jul/2025:13:34:16 +0000] "GET /assessments/status/830de7aa-050e-4ff5-9595-04939f6fbc83 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:36:15.527Z] [INFO] ::1 - - [17/Jul/2025:13:36:15 +0000] "GET /health HTTP/1.1" 200 593 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-17T13:38:31.260Z] [INFO] Assessment submission received {"userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-17T13:38:31.272Z] [INFO] Token deduction successful {"userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc","deductedAmount":1,"remainingBalance":1}
[2025-07-17T13:38:31.273Z] [INFO] Job created {"jobId":"e9bfeafd-1975-4f4c-ad74-aa3941858b9b","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc","userEmail":"<EMAIL>","status":"queued"}
[2025-07-17T13:38:31.274Z] [INFO] Assessment job published to queue {"jobId":"00cd0b6d-afa3-41a2-899d-8cd3ee662438","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-17T13:38:31.283Z] [ERROR] RabbitMQ connection error {"error":"Channel closed by server: 404 (NOT-FOUND) with message \"NOT_FOUND - no queue 'assessment_analysis' in vhost '/'\""}
[2025-07-17T13:38:31.286Z] [WARN] RabbitMQ connection closed
[2025-07-17T13:38:31.286Z] [ERROR] Failed to get queue statistics {"error":"Operation failed: QueueDeclare; 404 (NOT-FOUND) with message \"NOT_FOUND - no queue 'assessment_analysis' in vhost '/'\""}
[2025-07-17T13:38:31.287Z] [INFO] ::1 - - [17/Jul/2025:13:38:31 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:38:31.366Z] [INFO] ::1 - - [17/Jul/2025:13:38:31 +0000] "GET /assessments/status/e9bfeafd-1975-4f4c-ad74-aa3941858b9b HTTP/1.1" 200 266 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:38:31.379Z] [INFO] ::1 - - [17/Jul/2025:13:38:31 +0000] "GET /assessments/status/e9bfeafd-1975-4f4c-ad74-aa3941858b9b HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:38:36.299Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Channel closed by server: 404 (NOT-FOUND) with message \"NOT_FOUND - no queue 'assessment_analysis' in vhost '/'\")"}
[2025-07-17T13:38:36.299Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T13:38:36.304Z] [ERROR] Error closing existing RabbitMQ connection {"error":"Connection closed (Error: Channel closed by server: 404 (NOT-FOUND) with message \"NOT_FOUND - no queue 'assessment_analysis' in vhost '/'\")"}
[2025-07-17T13:38:36.305Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T13:38:36.328Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-17T13:38:36.330Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-17T13:38:36.354Z] [INFO] ::1 - - [17/Jul/2025:13:38:36 +0000] "GET /assessments/status/e9bfeafd-1975-4f4c-ad74-aa3941858b9b HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:38:41.355Z] [INFO] ::1 - - [17/Jul/2025:13:38:41 +0000] "GET /assessments/status/e9bfeafd-1975-4f4c-ad74-aa3941858b9b HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:38:44.549Z] [INFO] ::1 - - [17/Jul/2025:13:38:44 +0000] "GET /assessments/status/e9bfeafd-1975-4f4c-ad74-aa3941858b9b HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:38:45.039Z] [INFO] ::1 - - [17/Jul/2025:13:38:45 +0000] "GET /assessments/status/e9bfeafd-1975-4f4c-ad74-aa3941858b9b HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:38:49.553Z] [INFO] ::1 - - [17/Jul/2025:13:38:49 +0000] "GET /assessments/status/e9bfeafd-1975-4f4c-ad74-aa3941858b9b HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:38:54.548Z] [INFO] ::1 - - [17/Jul/2025:13:38:54 +0000] "GET /assessments/status/e9bfeafd-1975-4f4c-ad74-aa3941858b9b HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:38:59.552Z] [INFO] ::1 - - [17/Jul/2025:13:38:59 +0000] "GET /assessments/status/e9bfeafd-1975-4f4c-ad74-aa3941858b9b HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:39:04.551Z] [INFO] ::1 - - [17/Jul/2025:13:39:04 +0000] "GET /assessments/status/e9bfeafd-1975-4f4c-ad74-aa3941858b9b HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:39:09.552Z] [INFO] ::1 - - [17/Jul/2025:13:39:09 +0000] "GET /assessments/status/e9bfeafd-1975-4f4c-ad74-aa3941858b9b HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:39:14.548Z] [INFO] ::1 - - [17/Jul/2025:13:39:14 +0000] "GET /assessments/status/e9bfeafd-1975-4f4c-ad74-aa3941858b9b HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:39:19.553Z] [INFO] ::1 - - [17/Jul/2025:13:39:19 +0000] "GET /assessments/status/e9bfeafd-1975-4f4c-ad74-aa3941858b9b HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:46:25.275Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T13:46:25.317Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-17T13:46:25.318Z] [INFO] Queue service initialized successfully
[2025-07-17T13:46:25.318Z] [INFO] All services initialized successfully
[2025-07-17T13:46:25.322Z] [INFO] Assessment Service running on port 3003
[2025-07-17T13:46:25.322Z] [INFO] Environment: development
[2025-07-17T13:46:59.667Z] [INFO] Assessment submission received {"userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-17T13:46:59.779Z] [INFO] Token deduction successful {"userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc","deductedAmount":1,"remainingBalance":0}
[2025-07-17T13:46:59.780Z] [INFO] Job created {"jobId":"72cf577d-54c2-4786-89bd-701b4ff76629","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc","userEmail":"<EMAIL>","status":"queued"}
[2025-07-17T13:46:59.782Z] [INFO] Assessment job published to queue {"jobId":"a7a35f0b-b8d6-4cc1-8b96-a4e1d372fc49","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-17T13:46:59.788Z] [INFO] ::1 - - [17/Jul/2025:13:46:59 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:46:59.927Z] [INFO] ::1 - - [17/Jul/2025:13:46:59 +0000] "GET /assessments/status/72cf577d-54c2-4786-89bd-701b4ff76629 HTTP/1.1" 200 266 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:46:59.948Z] [INFO] ::1 - - [17/Jul/2025:13:46:59 +0000] "GET /assessments/status/72cf577d-54c2-4786-89bd-701b4ff76629 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:47:09.918Z] [INFO] ::1 - - [17/Jul/2025:13:47:09 +0000] "GET /assessments/status/72cf577d-54c2-4786-89bd-701b4ff76629 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:47:19.916Z] [INFO] ::1 - - [17/Jul/2025:13:47:19 +0000] "GET /assessments/status/72cf577d-54c2-4786-89bd-701b4ff76629 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:47:20.889Z] [INFO] ::1 - - [17/Jul/2025:13:47:20 +0000] "GET /assessments/status/72cf577d-54c2-4786-89bd-701b4ff76629 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:47:29.917Z] [INFO] ::1 - - [17/Jul/2025:13:47:29 +0000] "GET /assessments/status/72cf577d-54c2-4786-89bd-701b4ff76629 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:55:52.383Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T13:55:52.422Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-17T13:55:52.423Z] [INFO] Queue service initialized successfully
[2025-07-17T13:55:52.423Z] [INFO] All services initialized successfully
[2025-07-17T13:55:52.426Z] [INFO] Assessment Service running on port 3003
[2025-07-17T13:55:52.426Z] [INFO] Environment: development
[2025-07-17T13:58:39.839Z] [INFO] Assessment submission received {"userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-17T13:58:39.855Z] [INFO] Token deduction successful {"userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e","deductedAmount":1,"remainingBalance":2}
[2025-07-17T13:58:39.856Z] [INFO] Job created {"jobId":"fafd7290-7212-4066-b274-2da3388938ba","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e","userEmail":"<EMAIL>","status":"queued"}
[2025-07-17T13:58:39.858Z] [INFO] Assessment job published to queue {"jobId":"fafd7290-7212-4066-b274-2da3388938ba","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-17T13:58:39.864Z] [INFO] ::1 - - [17/Jul/2025:13:58:39 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:58:39.994Z] [INFO] ::1 - - [17/Jul/2025:13:58:39 +0000] "GET /assessments/status/fafd7290-7212-4066-b274-2da3388938ba HTTP/1.1" 200 266 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:58:40.012Z] [INFO] ::1 - - [17/Jul/2025:13:58:40 +0000] "GET /assessments/status/fafd7290-7212-4066-b274-2da3388938ba HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:58:49.989Z] [INFO] ::1 - - [17/Jul/2025:13:58:49 +0000] "GET /assessments/status/fafd7290-7212-4066-b274-2da3388938ba HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T13:58:59.980Z] [INFO] ::1 - - [17/Jul/2025:13:58:59 +0000] "GET /assessments/status/fafd7290-7212-4066-b274-2da3388938ba HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T14:06:48.554Z] [INFO] Assessment submission received {"userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-17T14:06:48.569Z] [INFO] Token deduction successful {"userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e","deductedAmount":1,"remainingBalance":1}
[2025-07-17T14:06:48.570Z] [INFO] Job created {"jobId":"c03694f1-84f7-4a96-8169-a9b2858f1217","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e","userEmail":"<EMAIL>","status":"queued"}
[2025-07-17T14:06:48.571Z] [INFO] Assessment job published to queue {"jobId":"c03694f1-84f7-4a96-8169-a9b2858f1217","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-17T14:06:48.574Z] [INFO] ::1 - - [17/Jul/2025:14:06:48 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T14:06:48.642Z] [INFO] ::1 - - [17/Jul/2025:14:06:48 +0000] "GET /assessments/status/c03694f1-84f7-4a96-8169-a9b2858f1217 HTTP/1.1" 200 266 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T14:06:48.656Z] [INFO] ::1 - - [17/Jul/2025:14:06:48 +0000] "GET /assessments/status/c03694f1-84f7-4a96-8169-a9b2858f1217 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T14:07:18.962Z] [INFO] Assessment submission received {"userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-17T14:07:18.976Z] [INFO] Token deduction successful {"userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e","deductedAmount":1,"remainingBalance":0}
[2025-07-17T14:07:18.977Z] [INFO] Job created {"jobId":"adf69211-2b12-4a93-8bc2-6aec278a2a4f","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e","userEmail":"<EMAIL>","status":"queued"}
[2025-07-17T14:07:18.978Z] [INFO] Assessment job published to queue {"jobId":"adf69211-2b12-4a93-8bc2-6aec278a2a4f","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-17T14:07:18.980Z] [INFO] ::1 - - [17/Jul/2025:14:07:18 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T14:07:19.044Z] [INFO] ::1 - - [17/Jul/2025:14:07:19 +0000] "GET /assessments/status/adf69211-2b12-4a93-8bc2-6aec278a2a4f HTTP/1.1" 200 266 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T14:07:19.056Z] [INFO] ::1 - - [17/Jul/2025:14:07:19 +0000] "GET /assessments/status/adf69211-2b12-4a93-8bc2-6aec278a2a4f HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T14:07:53.591Z] [INFO] ::1 - - [17/Jul/2025:14:07:53 +0000] "GET /assessments/status/adf69211-2b12-4a93-8bc2-6aec278a2a4f HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T14:07:53.605Z] [INFO] ::1 - - [17/Jul/2025:14:07:53 +0000] "GET /assessments/status/adf69211-2b12-4a93-8bc2-6aec278a2a4f HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T20:53:12.280Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T20:53:12.332Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-17T20:53:12.332Z] [INFO] Queue service initialized successfully
[2025-07-17T20:53:12.333Z] [INFO] All services initialized successfully
[2025-07-17T20:53:12.337Z] [INFO] Assessment Service running on port 3003
[2025-07-17T20:53:12.337Z] [INFO] Environment: development
[2025-07-17T20:53:50.348Z] [INFO] Assessment submission received {"userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-17T20:53:50.368Z] [INFO] Token deduction successful {"userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","deductedAmount":1,"remainingBalance":2}
[2025-07-17T20:53:50.370Z] [INFO] Job created {"jobId":"67abbbb1-0b2e-43e8-ad72-c05f7a3e4b75","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","userEmail":"<EMAIL>","status":"queued"}
[2025-07-17T20:53:50.371Z] [INFO] Assessment job published to queue {"jobId":"67abbbb1-0b2e-43e8-ad72-c05f7a3e4b75","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-17T20:53:50.377Z] [INFO] ::1 - - [17/Jul/2025:20:53:50 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T20:53:50.533Z] [INFO] ::1 - - [17/Jul/2025:20:53:50 +0000] "GET /assessments/status/67abbbb1-0b2e-43e8-ad72-c05f7a3e4b75 HTTP/1.1" 200 266 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T20:53:50.549Z] [INFO] ::1 - - [17/Jul/2025:20:53:50 +0000] "GET /assessments/status/67abbbb1-0b2e-43e8-ad72-c05f7a3e4b75 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T20:55:38.830Z] [INFO] Assessment submission received {"userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-17T20:55:38.844Z] [INFO] Token deduction successful {"userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","deductedAmount":1,"remainingBalance":1}
[2025-07-17T20:55:38.844Z] [INFO] Job created {"jobId":"4ff4cd34-3ed9-4d46-b23c-263f7b4b37e2","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","userEmail":"<EMAIL>","status":"queued"}
[2025-07-17T20:55:38.845Z] [INFO] Assessment job published to queue {"jobId":"4ff4cd34-3ed9-4d46-b23c-263f7b4b37e2","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-17T20:55:38.847Z] [INFO] ::1 - - [17/Jul/2025:20:55:38 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T20:55:38.917Z] [INFO] ::1 - - [17/Jul/2025:20:55:38 +0000] "GET /assessments/status/4ff4cd34-3ed9-4d46-b23c-263f7b4b37e2 HTTP/1.1" 200 266 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T20:55:38.931Z] [INFO] ::1 - - [17/Jul/2025:20:55:38 +0000] "GET /assessments/status/4ff4cd34-3ed9-4d46-b23c-263f7b4b37e2 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T20:56:19.512Z] [INFO] Assessment submission received {"userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-17T20:56:19.526Z] [INFO] Token deduction successful {"userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","deductedAmount":1,"remainingBalance":0}
[2025-07-17T20:56:19.526Z] [INFO] Job created {"jobId":"4541ee73-4344-48c3-8e27-1738aa858cb0","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","userEmail":"<EMAIL>","status":"queued"}
[2025-07-17T20:56:19.527Z] [INFO] Assessment job published to queue {"jobId":"4541ee73-4344-48c3-8e27-1738aa858cb0","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-17T20:56:19.529Z] [INFO] ::1 - - [17/Jul/2025:20:56:19 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T20:56:19.598Z] [INFO] ::1 - - [17/Jul/2025:20:56:19 +0000] "GET /assessments/status/4541ee73-4344-48c3-8e27-1738aa858cb0 HTTP/1.1" 200 266 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T20:56:19.618Z] [INFO] ::1 - - [17/Jul/2025:20:56:19 +0000] "GET /assessments/status/4541ee73-4344-48c3-8e27-1738aa858cb0 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T20:56:23.563Z] [INFO] ::1 - - [17/Jul/2025:20:56:23 +0000] "GET /assessments/status/4541ee73-4344-48c3-8e27-1738aa858cb0 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T20:56:27.880Z] [INFO] ::1 - - [17/Jul/2025:20:56:27 +0000] "GET /assessments/status/4541ee73-4344-48c3-8e27-1738aa858cb0 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-17T22:30:01.200Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T22:30:01.250Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-17T22:30:01.250Z] [INFO] Queue service initialized successfully
[2025-07-17T22:30:01.251Z] [INFO] All services initialized successfully
[2025-07-17T22:30:01.255Z] [INFO] Assessment Service running on port 3003
[2025-07-17T22:30:01.255Z] [INFO] Environment: development
[2025-07-17T22:48:35.520Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T22:48:35.556Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-17T22:48:35.557Z] [INFO] Queue service initialized successfully
[2025-07-17T22:48:35.558Z] [INFO] All services initialized successfully
[2025-07-17T22:48:35.562Z] [INFO] Assessment Service running on port 3003
[2025-07-17T22:48:35.563Z] [INFO] Environment: development
[2025-07-17T22:55:24.687Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T22:55:24.736Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-17T22:55:24.737Z] [INFO] Queue service initialized successfully
[2025-07-17T22:55:24.738Z] [INFO] All services initialized successfully
[2025-07-17T22:55:24.742Z] [INFO] Assessment Service running on port 3003
[2025-07-17T22:55:24.743Z] [INFO] Environment: development
[2025-07-17T23:17:30.341Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T23:17:30.404Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-17T23:17:30.405Z] [INFO] Queue service initialized successfully
[2025-07-17T23:17:30.406Z] [INFO] All services initialized successfully
[2025-07-17T23:17:30.410Z] [INFO] Assessment Service running on port 3003
[2025-07-17T23:17:30.411Z] [INFO] Environment: development
[2025-07-17T23:18:32.462Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T23:18:32.516Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-17T23:18:32.516Z] [INFO] Queue service initialized successfully
[2025-07-17T23:18:32.517Z] [INFO] All services initialized successfully
[2025-07-17T23:18:32.520Z] [INFO] Assessment Service running on port 3003
[2025-07-17T23:18:32.521Z] [INFO] Environment: development
[2025-07-17T23:21:49.968Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-17T23:21:50.005Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-17T23:21:50.005Z] [INFO] Queue service initialized successfully
[2025-07-17T23:21:50.006Z] [INFO] All services initialized successfully
[2025-07-17T23:21:50.010Z] [INFO] Assessment Service running on port 3003
[2025-07-17T23:21:50.010Z] [INFO] Environment: development
[2025-07-18T00:20:31.694Z] [INFO] Assessment submission received {"userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T00:20:31.716Z] [INFO] Token deduction successful {"userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","deductedAmount":1,"remainingBalance":19}
[2025-07-18T00:20:31.718Z] [INFO] Job created {"jobId":"12fb4f33-53ce-40ad-a844-0ef39b283710","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T00:20:31.720Z] [INFO] Assessment job published to queue {"jobId":"12fb4f33-53ce-40ad-a844-0ef39b283710","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T00:20:31.744Z] [INFO] ::1 - - [18/Jul/2025:00:20:31 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T00:20:31.829Z] [INFO] ::1 - - [18/Jul/2025:00:20:31 +0000] "GET /assessments/status/12fb4f33-53ce-40ad-a844-0ef39b283710 HTTP/1.1" 200 266 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T00:20:31.851Z] [INFO] ::1 - - [18/Jul/2025:00:20:31 +0000] "GET /assessments/status/12fb4f33-53ce-40ad-a844-0ef39b283710 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T00:20:38.519Z] [INFO] ::1 - - [18/Jul/2025:00:20:38 +0000] "GET /assessments/status/12fb4f33-53ce-40ad-a844-0ef39b283710 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T00:20:38.533Z] [INFO] ::1 - - [18/Jul/2025:00:20:38 +0000] "GET /assessments/status/12fb4f33-53ce-40ad-a844-0ef39b283710 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T00:21:00.434Z] [INFO] ::1 - - [18/Jul/2025:00:21:00 +0000] "GET /assessments/status/12fb4f33-53ce-40ad-a844-0ef39b283710 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T00:21:00.448Z] [INFO] ::1 - - [18/Jul/2025:00:21:00 +0000] "GET /assessments/status/12fb4f33-53ce-40ad-a844-0ef39b283710 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T00:21:19.214Z] [INFO] ::1 - - [18/Jul/2025:00:21:19 +0000] "GET /assessments/status/12fb4f33-53ce-40ad-a844-0ef39b283710 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T00:23:14.320Z] [INFO] ::1 - - [18/Jul/2025:00:23:14 +0000] "GET /assessments/status/12fb4f33-53ce-40ad-a844-0ef39b283710 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T00:23:52.324Z] [INFO] ::1 - - [18/Jul/2025:00:23:52 +0000] "GET /assessments/status/12fb4f33-53ce-40ad-a844-0ef39b283710 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T00:24:11.336Z] [INFO] ::1 - - [18/Jul/2025:00:24:11 +0000] "GET /assessments/status/12fb4f33-53ce-40ad-a844-0ef39b283710 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T00:24:26.326Z] [INFO] ::1 - - [18/Jul/2025:00:24:26 +0000] "GET /assessments/status/12fb4f33-53ce-40ad-a844-0ef39b283710 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T00:25:04.319Z] [INFO] ::1 - - [18/Jul/2025:00:25:04 +0000] "GET /assessments/status/12fb4f33-53ce-40ad-a844-0ef39b283710 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T00:25:27.426Z] [INFO] ::1 - - [18/Jul/2025:00:25:27 +0000] "GET /assessments/status/12fb4f33-53ce-40ad-a844-0ef39b283710 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T00:25:39.325Z] [INFO] ::1 - - [18/Jul/2025:00:25:39 +0000] "GET /assessments/status/12fb4f33-53ce-40ad-a844-0ef39b283710 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T00:29:40.276Z] [INFO] Assessment submission received {"userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T00:29:40.289Z] [INFO] Token deduction successful {"userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","deductedAmount":1,"remainingBalance":18}
[2025-07-18T00:29:40.290Z] [INFO] Job created {"jobId":"e7589ae6-98fe-43b1-8c0b-c9c08c440463","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T00:29:40.291Z] [INFO] Assessment job published to queue {"jobId":"e7589ae6-98fe-43b1-8c0b-c9c08c440463","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T00:29:40.293Z] [INFO] ::1 - - [18/Jul/2025:00:29:40 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T00:29:40.355Z] [INFO] ::1 - - [18/Jul/2025:00:29:40 +0000] "GET /assessments/status/e7589ae6-98fe-43b1-8c0b-c9c08c440463 HTTP/1.1" 200 266 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T00:29:40.368Z] [INFO] ::1 - - [18/Jul/2025:00:29:40 +0000] "GET /assessments/status/e7589ae6-98fe-43b1-8c0b-c9c08c440463 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T00:34:16.519Z] [INFO] Assessment submission received {"userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T00:34:16.533Z] [INFO] Token deduction successful {"userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","deductedAmount":1,"remainingBalance":17}
[2025-07-18T00:34:16.534Z] [INFO] Job created {"jobId":"9ab7c7f0-694b-4734-ac45-e9a574426df6","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T00:34:16.536Z] [INFO] Assessment job published to queue {"jobId":"9ab7c7f0-694b-4734-ac45-e9a574426df6","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T00:34:16.538Z] [INFO] ::1 - - [18/Jul/2025:00:34:16 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T00:34:16.599Z] [INFO] ::1 - - [18/Jul/2025:00:34:16 +0000] "GET /assessments/status/9ab7c7f0-694b-4734-ac45-e9a574426df6 HTTP/1.1" 200 266 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T00:34:16.614Z] [INFO] ::1 - - [18/Jul/2025:00:34:16 +0000] "GET /assessments/status/9ab7c7f0-694b-4734-ac45-e9a574426df6 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T00:40:06.899Z] [INFO] ::1 - - [18/Jul/2025:00:40:06 +0000] "GET /assessments/status/9ab7c7f0-694b-4734-ac45-e9a574426df6 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T00:40:06.915Z] [INFO] ::1 - - [18/Jul/2025:00:40:06 +0000] "GET /assessments/status/9ab7c7f0-694b-4734-ac45-e9a574426df6 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T00:40:10.824Z] [INFO] Assessment submission received {"userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T00:40:10.836Z] [INFO] Token deduction successful {"userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","deductedAmount":1,"remainingBalance":16}
[2025-07-18T00:40:10.836Z] [INFO] Job created {"jobId":"829865a1-9ebe-4b7d-9408-f4043da724cc","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T00:40:10.837Z] [INFO] Assessment job published to queue {"jobId":"829865a1-9ebe-4b7d-9408-f4043da724cc","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T00:40:10.840Z] [INFO] ::1 - - [18/Jul/2025:00:40:10 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T00:40:10.902Z] [INFO] ::1 - - [18/Jul/2025:00:40:10 +0000] "GET /assessments/status/829865a1-9ebe-4b7d-9408-f4043da724cc HTTP/1.1" 200 266 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T00:40:10.915Z] [INFO] ::1 - - [18/Jul/2025:00:40:10 +0000] "GET /assessments/status/829865a1-9ebe-4b7d-9408-f4043da724cc HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T01:02:32.401Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-18T01:02:32.440Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-18T01:02:32.441Z] [INFO] Queue service initialized successfully
[2025-07-18T01:02:32.441Z] [INFO] All services initialized successfully
[2025-07-18T01:02:32.445Z] [INFO] Assessment Service running on port 3003
[2025-07-18T01:02:32.445Z] [INFO] Environment: development
[2025-07-18T01:10:36.944Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-18T01:10:36.987Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-18T01:10:36.987Z] [INFO] Queue service initialized successfully
[2025-07-18T01:10:36.988Z] [INFO] All services initialized successfully
[2025-07-18T01:10:36.992Z] [INFO] Assessment Service running on port 3003
[2025-07-18T01:10:36.992Z] [INFO] Environment: development
[2025-07-18T01:21:01.810Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-18T01:21:01.854Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-18T01:21:01.855Z] [INFO] Queue service initialized successfully
[2025-07-18T01:21:01.855Z] [INFO] All services initialized successfully
[2025-07-18T01:21:01.860Z] [INFO] Assessment Service running on port 3003
[2025-07-18T01:21:01.860Z] [INFO] Environment: development
[2025-07-18T01:21:48.789Z] [INFO] Assessment submission received {"userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T01:21:48.888Z] [INFO] Token deduction successful {"userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","deductedAmount":1,"remainingBalance":15}
[2025-07-18T01:21:48.890Z] [INFO] Job created {"jobId":"3805c462-b97e-4898-9e1a-b2c1ddd71943","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T01:21:48.892Z] [INFO] Assessment job published to queue {"jobId":"3805c462-b97e-4898-9e1a-b2c1ddd71943","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T01:21:48.899Z] [INFO] ::1 - - [18/Jul/2025:01:21:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T01:21:48.982Z] [INFO] ::1 - - [18/Jul/2025:01:21:48 +0000] "GET /assessments/status/3805c462-b97e-4898-9e1a-b2c1ddd71943 HTTP/1.1" 200 266 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T01:21:49.001Z] [INFO] ::1 - - [18/Jul/2025:01:21:49 +0000] "GET /assessments/status/3805c462-b97e-4898-9e1a-b2c1ddd71943 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T01:22:12.995Z] [INFO] Assessment submission received {"userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T01:22:13.009Z] [INFO] Token deduction successful {"userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","deductedAmount":1,"remainingBalance":14}
[2025-07-18T01:22:13.010Z] [INFO] Job created {"jobId":"43cfe473-b3a6-409f-acdb-7d19fceda93d","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T01:22:13.011Z] [INFO] Assessment job published to queue {"jobId":"43cfe473-b3a6-409f-acdb-7d19fceda93d","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T01:22:13.013Z] [INFO] ::1 - - [18/Jul/2025:01:22:13 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T01:22:13.080Z] [INFO] ::1 - - [18/Jul/2025:01:22:13 +0000] "GET /assessments/status/43cfe473-b3a6-409f-acdb-7d19fceda93d HTTP/1.1" 200 266 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T01:22:13.094Z] [INFO] ::1 - - [18/Jul/2025:01:22:13 +0000] "GET /assessments/status/43cfe473-b3a6-409f-acdb-7d19fceda93d HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T01:23:30.994Z] [INFO] Assessment submission received {"userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T01:23:31.003Z] [INFO] Token deduction successful {"userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","deductedAmount":1,"remainingBalance":13}
[2025-07-18T01:23:31.004Z] [INFO] Job created {"jobId":"********-a659-4715-aeb0-ed3d194d3658","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T01:23:31.004Z] [INFO] Assessment job published to queue {"jobId":"********-a659-4715-aeb0-ed3d194d3658","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T01:23:31.006Z] [INFO] ::1 - - [18/Jul/2025:01:23:31 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T01:23:31.068Z] [INFO] ::1 - - [18/Jul/2025:01:23:31 +0000] "GET /assessments/status/********-a659-4715-aeb0-ed3d194d3658 HTTP/1.1" 200 266 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T01:23:31.083Z] [INFO] ::1 - - [18/Jul/2025:01:23:31 +0000] "GET /assessments/status/********-a659-4715-aeb0-ed3d194d3658 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T01:24:00.422Z] [INFO] Assessment submission received {"userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T01:24:00.434Z] [INFO] Token deduction successful {"userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","deductedAmount":1,"remainingBalance":12}
[2025-07-18T01:24:00.434Z] [INFO] Job created {"jobId":"79ad9488-b8ff-4936-b107-6820f5652537","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T01:24:00.435Z] [INFO] Assessment job published to queue {"jobId":"79ad9488-b8ff-4936-b107-6820f5652537","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T01:24:00.437Z] [INFO] ::1 - - [18/Jul/2025:01:24:00 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T01:24:00.491Z] [INFO] ::1 - - [18/Jul/2025:01:24:00 +0000] "GET /assessments/status/79ad9488-b8ff-4936-b107-6820f5652537 HTTP/1.1" 200 266 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T01:24:00.504Z] [INFO] ::1 - - [18/Jul/2025:01:24:00 +0000] "GET /assessments/status/79ad9488-b8ff-4936-b107-6820f5652537 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T01:24:25.379Z] [INFO] ::1 - - [18/Jul/2025:01:24:25 +0000] "GET /assessments/status/79ad9488-b8ff-4936-b107-6820f5652537 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T01:24:25.390Z] [INFO] ::1 - - [18/Jul/2025:01:24:25 +0000] "GET /assessments/status/79ad9488-b8ff-4936-b107-6820f5652537 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T01:26:43.953Z] [INFO] ::1 - - [18/Jul/2025:01:26:43 +0000] "GET /assessments/status/79ad9488-b8ff-4936-b107-6820f5652537 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T01:26:57.382Z] [INFO] ::1 - - [18/Jul/2025:01:26:57 +0000] "GET /assessments/status/79ad9488-b8ff-4936-b107-6820f5652537 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T01:27:15.192Z] [INFO] ::1 - - [18/Jul/2025:01:27:15 +0000] "GET /assessments/status/79ad9488-b8ff-4936-b107-6820f5652537 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T01:27:32.747Z] [INFO] ::1 - - [18/Jul/2025:01:27:32 +0000] "GET /assessments/status/79ad9488-b8ff-4936-b107-6820f5652537 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T01:27:45.028Z] [INFO] ::1 - - [18/Jul/2025:01:27:45 +0000] "GET /assessments/status/79ad9488-b8ff-4936-b107-6820f5652537 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T01:27:59.418Z] [INFO] ::1 - - [18/Jul/2025:01:27:59 +0000] "GET /assessments/status/79ad9488-b8ff-4936-b107-6820f5652537 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T01:28:11.971Z] [INFO] ::1 - - [18/Jul/2025:01:28:11 +0000] "GET /assessments/status/79ad9488-b8ff-4936-b107-6820f5652537 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T01:32:37.879Z] [INFO] Assessment submission received {"userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T01:32:37.890Z] [INFO] Token deduction successful {"userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","deductedAmount":1,"remainingBalance":11}
[2025-07-18T01:32:37.891Z] [INFO] Job created {"jobId":"81aa73ea-fc32-43f0-a493-2ba665247d2a","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T01:32:37.891Z] [INFO] Assessment job published to queue {"jobId":"81aa73ea-fc32-43f0-a493-2ba665247d2a","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T01:32:37.894Z] [INFO] ::1 - - [18/Jul/2025:01:32:37 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T01:34:42.326Z] [INFO] ::1 - - [18/Jul/2025:01:34:42 +0000] "GET /assessments/status/79ad9488-b8ff-4936-b107-6820f5652537 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T01:38:34.368Z] [INFO] Assessment submission received {"userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T01:38:34.382Z] [INFO] Token deduction successful {"userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","deductedAmount":1,"remainingBalance":10}
[2025-07-18T01:38:34.382Z] [INFO] Job created {"jobId":"975f0cf9-b2b0-4ed6-a3d3-4fd660546d64","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T01:38:34.382Z] [INFO] Assessment job published to queue {"jobId":"975f0cf9-b2b0-4ed6-a3d3-4fd660546d64","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T01:38:34.384Z] [INFO] ::1 - - [18/Jul/2025:01:38:34 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T01:38:34.438Z] [INFO] ::1 - - [18/Jul/2025:01:38:34 +0000] "GET /assessments/status/975f0cf9-b2b0-4ed6-a3d3-4fd660546d64 HTTP/1.1" 200 266 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T01:38:34.454Z] [INFO] ::1 - - [18/Jul/2025:01:38:34 +0000] "GET /assessments/status/975f0cf9-b2b0-4ed6-a3d3-4fd660546d64 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T01:46:35.162Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-18T01:46:35.200Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-18T01:46:35.201Z] [INFO] Queue service initialized successfully
[2025-07-18T01:46:35.202Z] [INFO] All services initialized successfully
[2025-07-18T01:46:35.206Z] [INFO] Assessment Service running on port 3003
[2025-07-18T01:46:35.206Z] [INFO] Environment: development
[2025-07-18T01:52:55.813Z] [INFO] Assessment submission received {"userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T01:52:55.832Z] [INFO] Token deduction successful {"userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","deductedAmount":1,"remainingBalance":9}
[2025-07-18T01:52:55.833Z] [INFO] Job created {"jobId":"8f78d6bd-c19b-4231-b969-48267edffd02","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T01:52:55.834Z] [INFO] Assessment job published to queue {"jobId":"8f78d6bd-c19b-4231-b969-48267edffd02","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T01:52:55.839Z] [INFO] ::1 - - [18/Jul/2025:01:52:55 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T01:52:55.902Z] [INFO] ::1 - - [18/Jul/2025:01:52:55 +0000] "GET /assessments/status/8f78d6bd-c19b-4231-b969-48267edffd02 HTTP/1.1" 200 266 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T01:52:55.920Z] [INFO] ::1 - - [18/Jul/2025:01:52:55 +0000] "GET /assessments/status/8f78d6bd-c19b-4231-b969-48267edffd02 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T01:55:30.534Z] [INFO] Assessment submission received {"userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T01:55:30.550Z] [INFO] Token deduction successful {"userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","deductedAmount":1,"remainingBalance":8}
[2025-07-18T01:55:30.551Z] [INFO] Job created {"jobId":"a628abdf-a9ea-4cb4-bbbd-d39b70b56b18","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T01:55:30.552Z] [INFO] Assessment job published to queue {"jobId":"a628abdf-a9ea-4cb4-bbbd-d39b70b56b18","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T01:55:30.556Z] [INFO] ::1 - - [18/Jul/2025:01:55:30 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T01:55:30.627Z] [INFO] ::1 - - [18/Jul/2025:01:55:30 +0000] "GET /assessments/status/a628abdf-a9ea-4cb4-bbbd-d39b70b56b18 HTTP/1.1" 200 266 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T01:55:30.646Z] [INFO] ::1 - - [18/Jul/2025:01:55:30 +0000] "GET /assessments/status/a628abdf-a9ea-4cb4-bbbd-d39b70b56b18 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T01:58:33.126Z] [INFO] Assessment submission received {"userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T01:58:33.140Z] [INFO] Token deduction successful {"userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","deductedAmount":1,"remainingBalance":7}
[2025-07-18T01:58:33.140Z] [INFO] Job created {"jobId":"d8560735-557d-4c8f-97cc-c6ebe441a281","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T01:58:33.141Z] [INFO] Assessment job published to queue {"jobId":"d8560735-557d-4c8f-97cc-c6ebe441a281","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T01:58:33.145Z] [INFO] ::1 - - [18/Jul/2025:01:58:33 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T01:58:33.208Z] [INFO] ::1 - - [18/Jul/2025:01:58:33 +0000] "GET /assessments/status/d8560735-557d-4c8f-97cc-c6ebe441a281 HTTP/1.1" 200 266 "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T01:58:33.224Z] [INFO] ::1 - - [18/Jul/2025:01:58:33 +0000] "GET /assessments/status/d8560735-557d-4c8f-97cc-c6ebe441a281 HTTP/1.1" 304 - "http://localhost:5173/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
[2025-07-18T02:06:32.207Z] [INFO] ::1 - - [18/Jul/2025:02:06:32 +0000] "GET /health HTTP/1.1" 200 592 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-18T03:09:52.765Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-18T03:09:52.810Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-18T03:09:52.812Z] [INFO] Queue service initialized successfully
[2025-07-18T03:09:52.812Z] [INFO] All services initialized successfully
[2025-07-18T03:09:52.816Z] [INFO] Assessment Service running on port 3003
[2025-07-18T03:09:52.817Z] [INFO] Environment: development
[2025-07-18T03:12:19.957Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-18T03:12:19.996Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-18T03:12:19.997Z] [INFO] Queue service initialized successfully
[2025-07-18T03:12:19.997Z] [INFO] All services initialized successfully
[2025-07-18T03:12:20.001Z] [INFO] Assessment Service running on port 3003
[2025-07-18T03:12:20.001Z] [INFO] Environment: development
[2025-07-18T03:15:30.118Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-18T03:15:30.147Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-18T03:15:30.148Z] [INFO] Queue service initialized successfully
[2025-07-18T03:15:30.148Z] [INFO] All services initialized successfully
[2025-07-18T03:15:30.151Z] [INFO] Assessment Service running on port 3003
[2025-07-18T03:15:30.151Z] [INFO] Environment: development
[2025-07-18T03:17:57.413Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-18T03:17:57.451Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-18T03:17:57.452Z] [INFO] Queue service initialized successfully
[2025-07-18T03:17:57.453Z] [INFO] All services initialized successfully
[2025-07-18T03:17:57.456Z] [INFO] Assessment Service running on port 3003
[2025-07-18T03:17:57.456Z] [INFO] Environment: development
[2025-07-18T03:18:49.188Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-18T03:18:49.219Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-18T03:18:49.219Z] [INFO] Queue service initialized successfully
[2025-07-18T03:18:49.220Z] [INFO] All services initialized successfully
[2025-07-18T03:18:49.222Z] [INFO] Assessment Service running on port 3003
[2025-07-18T03:18:49.222Z] [INFO] Environment: development
[2025-07-18T03:24:59.061Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-18T03:24:59.088Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-18T03:24:59.089Z] [INFO] Queue service initialized successfully
[2025-07-18T03:24:59.089Z] [INFO] All services initialized successfully
[2025-07-18T03:24:59.091Z] [INFO] Assessment Service running on port 3003
[2025-07-18T03:24:59.092Z] [INFO] Environment: development
[2025-07-18T05:40:55.956Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-18T05:40:55.994Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-18T05:40:55.994Z] [INFO] Queue service initialized successfully
[2025-07-18T05:40:55.995Z] [INFO] All services initialized successfully
[2025-07-18T05:40:55.998Z] [INFO] Assessment Service running on port 3003
[2025-07-18T05:40:55.998Z] [INFO] Environment: development
[2025-07-18T05:42:28.581Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"da6e7756-6da7-4762-9478-15989c524ba5","url":"/assessments/submit"}
[2025-07-18T05:42:28.586Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.589Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"ff453c60-d4cc-4a3b-895b-58aea0d76994","url":"/assessments/submit"}
[2025-07-18T05:42:28.590Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.592Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"842827f5-e6a0-464c-adee-d849b9ffb009","url":"/assessments/submit"}
[2025-07-18T05:42:28.593Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.596Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"b41051d1-550d-4d43-a899-ff6731e904ef","url":"/assessments/submit"}
[2025-07-18T05:42:28.597Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.598Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"15902e84-9592-4b67-b3c2-1ec79e1dd54d","url":"/assessments/submit"}
[2025-07-18T05:42:28.599Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.601Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"a95c638e-a5c5-4d5a-bac3-440783832e88","url":"/assessments/submit"}
[2025-07-18T05:42:28.604Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.605Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"d9acc7c4-6671-4254-8f39-6034e3924c8b","url":"/assessments/submit"}
[2025-07-18T05:42:28.606Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.608Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"2d1c9020-140a-46dc-842d-2ca54be7a854","url":"/assessments/submit"}
[2025-07-18T05:42:28.609Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.613Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"80b44903-76e5-4d53-9831-574ecab7f147","url":"/assessments/submit"}
[2025-07-18T05:42:28.615Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.617Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"b74f8249-1c2d-4db6-ad8d-6af943390e77","url":"/assessments/submit"}
[2025-07-18T05:42:28.620Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.716Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"cb7a1876-d50d-4ac0-b263-6790e313d4f8","url":"/assessments/submit"}
[2025-07-18T05:42:28.717Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.719Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"392fd33d-ab74-4da6-a91a-02c56656a57c","url":"/assessments/submit"}
[2025-07-18T05:42:28.720Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.722Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"0ee6c0c4-05ae-4a76-8872-6bc82a9413df","url":"/assessments/submit"}
[2025-07-18T05:42:28.723Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.725Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"43411672-8178-4b77-a062-73d1340d6dd3","url":"/assessments/submit"}
[2025-07-18T05:42:28.727Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.730Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"8cd96f02-f438-4e30-b81d-df35181b1dd3","url":"/assessments/submit"}
[2025-07-18T05:42:28.731Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.733Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"6c14d02a-a968-495c-889c-d9a3398c74a0","url":"/assessments/submit"}
[2025-07-18T05:42:28.734Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.736Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"ee52423e-4703-4517-8fe5-b8661b377999","url":"/assessments/submit"}
[2025-07-18T05:42:28.737Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.738Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"8d16eed0-635c-4881-900f-1fdfcee24078","url":"/assessments/submit"}
[2025-07-18T05:42:28.739Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.741Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"384e4e16-5ef1-4fc2-93c0-d0b08e952d12","url":"/assessments/submit"}
[2025-07-18T05:42:28.742Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.743Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"b48bb1d1-7bf9-4262-8655-88c3c751a97e","url":"/assessments/submit"}
[2025-07-18T05:42:28.744Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.746Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"8f153039-6f2b-411e-af79-c636d53cc3f9","url":"/assessments/submit"}
[2025-07-18T05:42:28.747Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.748Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"bc754f4a-5ac0-4053-8bba-a2a42a31a746","url":"/assessments/submit"}
[2025-07-18T05:42:28.749Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.751Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"849ecb09-081f-43d5-a71a-6bca85f6a8aa","url":"/assessments/submit"}
[2025-07-18T05:42:28.752Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.754Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"6bc0b5c1-151e-4b05-b743-4762063acbee","url":"/assessments/submit"}
[2025-07-18T05:42:28.755Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.757Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"009e09fc-311a-4851-949e-b76859139743","url":"/assessments/submit"}
[2025-07-18T05:42:28.758Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.759Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"040790f4-eeb4-46d7-84e7-768e0f8c500b","url":"/assessments/submit"}
[2025-07-18T05:42:28.760Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.761Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"b29a61ec-cda2-4ce1-9e63-a655109ba149","url":"/assessments/submit"}
[2025-07-18T05:42:28.762Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.764Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"4aa44128-345e-4ebe-b3eb-39676d2596ce","url":"/assessments/submit"}
[2025-07-18T05:42:28.766Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.767Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"12e2ba85-6226-4701-80f5-f7831eedfd48","url":"/assessments/submit"}
[2025-07-18T05:42:28.768Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.769Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"d6d1f30e-be74-494c-8a9b-bb501e91a0e3","url":"/assessments/submit"}
[2025-07-18T05:42:28.772Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.777Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"bb269993-1022-43b2-8926-4fbf4043ba0c","url":"/assessments/submit"}
[2025-07-18T05:42:28.778Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.781Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"078de71f-122d-41ec-be55-b4b93c20d6b8","url":"/assessments/submit"}
[2025-07-18T05:42:28.782Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.783Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"b03799da-c6da-4925-87c6-901060f1ffc3","url":"/assessments/submit"}
[2025-07-18T05:42:28.784Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.802Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"3dbea5fc-4925-4aa7-bfcd-318599a9e5b1","url":"/assessments/submit"}
[2025-07-18T05:42:28.804Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.805Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"c52e498b-c989-4e27-b783-2fe3316085a3","url":"/assessments/submit"}
[2025-07-18T05:42:28.806Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.807Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"8f72b4c2-3265-4ba0-8f52-6a1ff9d5e497","url":"/assessments/submit"}
[2025-07-18T05:42:28.808Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.810Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"7e1bb330-f26a-4a0f-a0df-b16e75e63d36","url":"/assessments/submit"}
[2025-07-18T05:42:28.810Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.812Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"3653691f-57cf-4782-8edf-157e46f19bd8","url":"/assessments/submit"}
[2025-07-18T05:42:28.813Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.814Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"11556c6b-f185-42ce-a3b6-369dd56a6ac5","url":"/assessments/submit"}
[2025-07-18T05:42:28.815Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.818Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"d7d7720a-b095-4f24-8dea-4b4fae817a5a","url":"/assessments/submit"}
[2025-07-18T05:42:28.819Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.821Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"68abe24d-9fd9-4a34-9538-81ebed775b4c","url":"/assessments/submit"}
[2025-07-18T05:42:28.821Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.823Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"ed022609-ebc6-41d0-a464-2dc10ca8773e","url":"/assessments/submit"}
[2025-07-18T05:42:28.823Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.825Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"45f1e061-9c35-4aad-802e-6d1a58fbdaf0","url":"/assessments/submit"}
[2025-07-18T05:42:28.826Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.827Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"24a91af5-f630-4d85-8a86-1863e3c977ec","url":"/assessments/submit"}
[2025-07-18T05:42:28.828Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.830Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"f1b2443e-1fd7-40a2-b26e-************","url":"/assessments/submit"}
[2025-07-18T05:42:28.830Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.832Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"c540dde8-d7e7-4b31-946b-c44073078958","url":"/assessments/submit"}
[2025-07-18T05:42:28.833Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.834Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"c760022b-5504-4436-b0df-12e523fb08cf","url":"/assessments/submit"}
[2025-07-18T05:42:28.835Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.837Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"950a1c16-ba41-4ac6-bea8-b052c6bd32fb","url":"/assessments/submit"}
[2025-07-18T05:42:28.838Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.839Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"b713b871-2151-4908-82c7-e25043087388","url":"/assessments/submit"}
[2025-07-18T05:42:28.840Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.842Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"31ffaa30-7eca-4caa-a144-e4d4184797f8","url":"/assessments/submit"}
[2025-07-18T05:42:28.843Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.844Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"15060bb5-db18-49b3-931e-9222c270e677","url":"/assessments/submit"}
[2025-07-18T05:42:28.845Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.848Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"2280d0b1-52dd-4ea1-af58-caf1a0dc1ccb","url":"/assessments/submit"}
[2025-07-18T05:42:28.850Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.851Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"8644861e-dabc-4b8b-a627-a0ae38bc44fe","url":"/assessments/submit"}
[2025-07-18T05:42:28.852Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.854Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"36a5194c-32f2-4426-b5e4-76f19f18b956","url":"/assessments/submit"}
[2025-07-18T05:42:28.856Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.857Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"f47f861d-74bb-4198-a24d-8509841f9e80","url":"/assessments/submit"}
[2025-07-18T05:42:28.858Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.859Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"27046cf8-947c-460f-bcac-21f0770d22ec","url":"/assessments/submit"}
[2025-07-18T05:42:28.860Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.861Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"072f69fa-6cbe-400b-b796-7e3271da936d","url":"/assessments/submit"}
[2025-07-18T05:42:28.862Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.863Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"b4bc5f99-ad7c-4687-83c5-9e9e21aeba64","url":"/assessments/submit"}
[2025-07-18T05:42:28.864Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.865Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"b7572a8f-d3ba-410d-9871-a3439187285c","url":"/assessments/submit"}
[2025-07-18T05:42:28.865Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.867Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"cc61ca36-c057-44e6-8dee-f275f24dc1c0","url":"/assessments/submit"}
[2025-07-18T05:42:28.868Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.869Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"********-6bd8-4161-bbf0-e0bb0565df48","url":"/assessments/submit"}
[2025-07-18T05:42:28.870Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.871Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"9b4c5ed1-e673-4ee9-b696-5be44a561be0","url":"/assessments/submit"}
[2025-07-18T05:42:28.871Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.872Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"fd129d2c-39d3-404c-a103-21a379463fcb","url":"/assessments/submit"}
[2025-07-18T05:42:28.873Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.874Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"a26a6dd4-23e9-46f7-b782-15f7859d8342","url":"/assessments/submit"}
[2025-07-18T05:42:28.875Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.877Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"8c044fe4-4f97-4ace-8360-641d1fe1e52c","url":"/assessments/submit"}
[2025-07-18T05:42:28.877Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.893Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"778981a0-fa31-4a3a-9d23-e9cbb9c0c952","url":"/assessments/submit"}
[2025-07-18T05:42:28.894Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.895Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"********-9fe7-47c1-b50d-a0b5c5918171","url":"/assessments/submit"}
[2025-07-18T05:42:28.896Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.897Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"5cc55727-a792-495d-ac51-0d0e844e5d53","url":"/assessments/submit"}
[2025-07-18T05:42:28.898Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.899Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"2db8eac1-765e-4011-bb8d-7b92904ac01b","url":"/assessments/submit"}
[2025-07-18T05:42:28.900Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.901Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"68b8a7f4-00cf-4787-9396-f1983ed53648","url":"/assessments/submit"}
[2025-07-18T05:42:28.902Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.903Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"7dde7770-1e0e-481e-af32-84bc96240188","url":"/assessments/submit"}
[2025-07-18T05:42:28.904Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.923Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"727601f0-6746-4c23-b6af-d4d8f27c36b3","url":"/assessments/submit"}
[2025-07-18T05:42:28.924Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.925Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"6761c652-3586-4274-8e32-44b45eb0ab1b","url":"/assessments/submit"}
[2025-07-18T05:42:28.927Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.931Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"4f114070-2569-44d2-ae17-e0e84d825a51","url":"/assessments/submit"}
[2025-07-18T05:42:28.932Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.933Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"c50c6b3a-274f-47ad-97e4-19a762a6aed6","url":"/assessments/submit"}
[2025-07-18T05:42:28.934Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.935Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"44bf17ee-fb4d-4a0a-ab8c-ea5ac863032d","url":"/assessments/submit"}
[2025-07-18T05:42:28.936Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.938Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"f9b12420-847f-4968-b8b9-4153fdc8aae2","url":"/assessments/submit"}
[2025-07-18T05:42:28.938Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.939Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"25a10f5b-94bb-4e95-b143-09ead677e663","url":"/assessments/submit"}
[2025-07-18T05:42:28.941Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.942Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"4b9c8855-018c-44b3-89ef-8f0201454847","url":"/assessments/submit"}
[2025-07-18T05:42:28.943Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.944Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"39ede2d4-b490-4c9a-b193-f3328fbd4857","url":"/assessments/submit"}
[2025-07-18T05:42:28.945Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.947Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"ce779b7f-d252-445b-99cf-776c8d253223","url":"/assessments/submit"}
[2025-07-18T05:42:28.948Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.949Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"8eaf3b43-8867-4573-91ec-926d03d93a07","url":"/assessments/submit"}
[2025-07-18T05:42:28.949Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.950Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"77a4b43c-1906-4c6e-ab4b-7cf5ca9cc4fe","url":"/assessments/submit"}
[2025-07-18T05:42:28.951Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.952Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"69ae9167-80c7-4164-8f08-14fc7e5f96b8","url":"/assessments/submit"}
[2025-07-18T05:42:28.953Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.954Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"3de2f9e7-75b6-429a-90a4-b4366adf0874","url":"/assessments/submit"}
[2025-07-18T05:42:28.955Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.956Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"c48a95d5-717b-487b-b2ce-61d7e09e5b43","url":"/assessments/submit"}
[2025-07-18T05:42:28.956Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.958Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"9ae3d55e-01fd-4000-9698-d17c23b6e39c","url":"/assessments/submit"}
[2025-07-18T05:42:28.958Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:42:28.959Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"da9eee0f-2f64-4776-943c-45d0496b69e0","url":"/assessments/submit"}
[2025-07-18T05:42:28.960Z] [INFO] ::1 - - [18/Jul/2025:05:42:28 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:43:18.695Z] [INFO] ::1 - - [18/Jul/2025:05:43:18 +0000] "GET /health HTTP/1.1" 200 591 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-18T05:45:25.251Z] [WARN] Validation error {"source":"body","errors":{"viaIs.appreciationOfBeauty":"VIA-IS assessment data is required"},"userId":"da6e7756-6da7-4762-9478-15989c524ba5","url":"/assessments/submit"}
[2025-07-18T05:45:25.252Z] [INFO] ::1 - - [18/Jul/2025:05:45:25 +0000] "POST /assessments/submit HTTP/1.1" 400 161 "-" "axios/1.10.0"
[2025-07-18T05:46:46.268Z] [INFO] Assessment submission received {"userId":"da6e7756-6da7-4762-9478-15989c524ba5","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:46:46.285Z] [INFO] Token deduction successful {"userId":"da6e7756-6da7-4762-9478-15989c524ba5","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:46:46.286Z] [INFO] Job created {"jobId":"23e58e05-b38d-4cb5-bbc1-4ce7aba19abb","userId":"da6e7756-6da7-4762-9478-15989c524ba5","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:46:46.287Z] [INFO] Assessment job published to queue {"jobId":"23e58e05-b38d-4cb5-bbc1-4ce7aba19abb","userId":"da6e7756-6da7-4762-9478-15989c524ba5","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:46:46.290Z] [INFO] ::1 - - [18/Jul/2025:05:46:46 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-18T05:46:56.319Z] [INFO] Assessment submission received {"userId":"da6e7756-6da7-4762-9478-15989c524ba5","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:46:56.322Z] [INFO] Assessment submission received {"userId":"bb269993-1022-43b2-8926-4fbf4043ba0c","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:46:56.324Z] [INFO] Assessment submission received {"userId":"ff453c60-d4cc-4a3b-895b-58aea0d76994","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:46:56.337Z] [INFO] Assessment submission received {"userId":"842827f5-e6a0-464c-adee-d849b9ffb009","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:46:56.383Z] [INFO] Token deduction successful {"userId":"da6e7756-6da7-4762-9478-15989c524ba5","deductedAmount":1,"remainingBalance":1}
[2025-07-18T05:46:56.383Z] [INFO] Job created {"jobId":"706cb084-15da-40a5-827f-cda6dd355a7f","userId":"da6e7756-6da7-4762-9478-15989c524ba5","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:46:56.385Z] [INFO] Assessment job published to queue {"jobId":"706cb084-15da-40a5-827f-cda6dd355a7f","userId":"da6e7756-6da7-4762-9478-15989c524ba5","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:46:56.386Z] [INFO] Token deduction successful {"userId":"bb269993-1022-43b2-8926-4fbf4043ba0c","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:46:56.387Z] [INFO] Job created {"jobId":"f57adc7d-95c3-4cea-96af-de83780d0a20","userId":"bb269993-1022-43b2-8926-4fbf4043ba0c","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:46:56.387Z] [INFO] Assessment job published to queue {"jobId":"f57adc7d-95c3-4cea-96af-de83780d0a20","userId":"bb269993-1022-43b2-8926-4fbf4043ba0c","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:46:56.388Z] [INFO] Assessment submission received {"userId":"15902e84-9592-4b67-b3c2-1ec79e1dd54d","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:46:56.391Z] [INFO] Token deduction successful {"userId":"ff453c60-d4cc-4a3b-895b-58aea0d76994","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:46:56.391Z] [INFO] Job created {"jobId":"b7a01cf7-90b3-487f-a046-3ba5f294263a","userId":"ff453c60-d4cc-4a3b-895b-58aea0d76994","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:46:56.392Z] [INFO] Assessment job published to queue {"jobId":"b7a01cf7-90b3-487f-a046-3ba5f294263a","userId":"ff453c60-d4cc-4a3b-895b-58aea0d76994","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:46:56.393Z] [INFO] Token deduction successful {"userId":"842827f5-e6a0-464c-adee-d849b9ffb009","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:46:56.393Z] [INFO] Job created {"jobId":"6dfd5389-9782-453c-870f-7e842822cd25","userId":"842827f5-e6a0-464c-adee-d849b9ffb009","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:46:56.395Z] [INFO] Assessment job published to queue {"jobId":"6dfd5389-9782-453c-870f-7e842822cd25","userId":"842827f5-e6a0-464c-adee-d849b9ffb009","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:46:56.398Z] [INFO] ::1 - - [18/Jul/2025:05:46:56 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-18T05:46:56.409Z] [INFO] Assessment submission received {"userId":"b41051d1-550d-4d43-a899-ff6731e904ef","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:46:56.412Z] [INFO] Assessment submission received {"userId":"a95c638e-a5c5-4d5a-bac3-440783832e88","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:46:56.414Z] [INFO] ::1 - - [18/Jul/2025:05:46:56 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-18T05:46:56.418Z] [INFO] Assessment submission received {"userId":"d9acc7c4-6671-4254-8f39-6034e3924c8b","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:46:56.422Z] [INFO] Assessment submission received {"userId":"078de71f-122d-41ec-be55-b4b93c20d6b8","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:46:56.425Z] [INFO] ::1 - - [18/Jul/2025:05:46:56 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-18T05:46:56.427Z] [INFO] Assessment submission received {"userId":"6c14d02a-a968-495c-889c-d9a3398c74a0","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:46:56.432Z] [INFO] ::1 - - [18/Jul/2025:05:46:56 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-18T05:46:56.440Z] [INFO] Token deduction successful {"userId":"15902e84-9592-4b67-b3c2-1ec79e1dd54d","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:46:56.441Z] [INFO] Job created {"jobId":"953e4079-db1e-4961-83f7-92ca8ff520eb","userId":"15902e84-9592-4b67-b3c2-1ec79e1dd54d","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:46:56.441Z] [INFO] Assessment job published to queue {"jobId":"953e4079-db1e-4961-83f7-92ca8ff520eb","userId":"15902e84-9592-4b67-b3c2-1ec79e1dd54d","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:46:56.443Z] [INFO] ::1 - - [18/Jul/2025:05:46:56 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-18T05:46:56.468Z] [INFO] Token deduction successful {"userId":"b41051d1-550d-4d43-a899-ff6731e904ef","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:46:56.469Z] [INFO] Job created {"jobId":"0ce3850f-28e8-43e8-98a3-8fbf07eb4920","userId":"b41051d1-550d-4d43-a899-ff6731e904ef","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:46:56.469Z] [INFO] Assessment job published to queue {"jobId":"0ce3850f-28e8-43e8-98a3-8fbf07eb4920","userId":"b41051d1-550d-4d43-a899-ff6731e904ef","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:46:56.471Z] [INFO] Token deduction successful {"userId":"6c14d02a-a968-495c-889c-d9a3398c74a0","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:46:56.471Z] [INFO] Job created {"jobId":"a608b30c-a89d-41d7-b5b0-9b89efea16d0","userId":"6c14d02a-a968-495c-889c-d9a3398c74a0","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:46:56.472Z] [INFO] Assessment job published to queue {"jobId":"a608b30c-a89d-41d7-b5b0-9b89efea16d0","userId":"6c14d02a-a968-495c-889c-d9a3398c74a0","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:46:56.473Z] [INFO] Token deduction successful {"userId":"a95c638e-a5c5-4d5a-bac3-440783832e88","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:46:56.473Z] [INFO] Job created {"jobId":"c736afcc-caf2-430a-9d81-a12a72b367a6","userId":"a95c638e-a5c5-4d5a-bac3-440783832e88","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:46:56.474Z] [INFO] Assessment job published to queue {"jobId":"c736afcc-caf2-430a-9d81-a12a72b367a6","userId":"a95c638e-a5c5-4d5a-bac3-440783832e88","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:46:56.475Z] [INFO] Token deduction successful {"userId":"d9acc7c4-6671-4254-8f39-6034e3924c8b","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:46:56.476Z] [INFO] Job created {"jobId":"6672a02d-2629-46dc-a4c9-07c6c1842be3","userId":"d9acc7c4-6671-4254-8f39-6034e3924c8b","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:46:56.477Z] [INFO] Assessment job published to queue {"jobId":"6672a02d-2629-46dc-a4c9-07c6c1842be3","userId":"d9acc7c4-6671-4254-8f39-6034e3924c8b","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:46:56.478Z] [INFO] ::1 - - [18/Jul/2025:05:46:56 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-18T05:46:56.479Z] [INFO] Token deduction successful {"userId":"078de71f-122d-41ec-be55-b4b93c20d6b8","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:46:56.479Z] [INFO] Job created {"jobId":"ffcd9e7f-9de6-4d32-99b4-56b82711b047","userId":"078de71f-122d-41ec-be55-b4b93c20d6b8","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:46:56.480Z] [INFO] Assessment job published to queue {"jobId":"ffcd9e7f-9de6-4d32-99b4-56b82711b047","userId":"078de71f-122d-41ec-be55-b4b93c20d6b8","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:46:56.484Z] [INFO] ::1 - - [18/Jul/2025:05:46:56 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-18T05:46:56.487Z] [INFO] ::1 - - [18/Jul/2025:05:46:56 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-18T05:46:56.490Z] [INFO] ::1 - - [18/Jul/2025:05:46:56 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-18T05:46:56.491Z] [INFO] ::1 - - [18/Jul/2025:05:46:56 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-18T05:47:05.716Z] [INFO] Assessment submission received {"userId":"da6e7756-6da7-4762-9478-15989c524ba5","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:05.718Z] [INFO] Assessment submission received {"userId":"b41051d1-550d-4d43-a899-ff6731e904ef","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:05.720Z] [INFO] Assessment submission received {"userId":"15902e84-9592-4b67-b3c2-1ec79e1dd54d","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:05.722Z] [INFO] Assessment submission received {"userId":"a95c638e-a5c5-4d5a-bac3-440783832e88","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:05.725Z] [INFO] Assessment submission received {"userId":"d9acc7c4-6671-4254-8f39-6034e3924c8b","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:05.727Z] [INFO] Assessment submission received {"userId":"6c14d02a-a968-495c-889c-d9a3398c74a0","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:05.774Z] [INFO] Assessment submission received {"userId":"842827f5-e6a0-464c-adee-d849b9ffb009","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:05.776Z] [INFO] Token deduction successful {"userId":"da6e7756-6da7-4762-9478-15989c524ba5","deductedAmount":1,"remainingBalance":0}
[2025-07-18T05:47:05.776Z] [INFO] Job created {"jobId":"95728b57-312a-4fa3-854f-6542a1aa88ee","userId":"da6e7756-6da7-4762-9478-15989c524ba5","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:05.777Z] [INFO] Assessment job published to queue {"jobId":"95728b57-312a-4fa3-854f-6542a1aa88ee","userId":"da6e7756-6da7-4762-9478-15989c524ba5","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:05.778Z] [INFO] Token deduction successful {"userId":"a95c638e-a5c5-4d5a-bac3-440783832e88","deductedAmount":1,"remainingBalance":1}
[2025-07-18T05:47:05.779Z] [INFO] Job created {"jobId":"16d08e48-839e-47db-9403-1768e0cb5674","userId":"a95c638e-a5c5-4d5a-bac3-440783832e88","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:05.780Z] [INFO] Assessment job published to queue {"jobId":"16d08e48-839e-47db-9403-1768e0cb5674","userId":"a95c638e-a5c5-4d5a-bac3-440783832e88","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:05.781Z] [INFO] Token deduction successful {"userId":"d9acc7c4-6671-4254-8f39-6034e3924c8b","deductedAmount":1,"remainingBalance":1}
[2025-07-18T05:47:05.781Z] [INFO] Job created {"jobId":"4b547a7a-0847-421e-acb3-fee3f0987368","userId":"d9acc7c4-6671-4254-8f39-6034e3924c8b","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:05.782Z] [INFO] Assessment job published to queue {"jobId":"4b547a7a-0847-421e-acb3-fee3f0987368","userId":"d9acc7c4-6671-4254-8f39-6034e3924c8b","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:05.783Z] [INFO] Assessment submission received {"userId":"bb269993-1022-43b2-8926-4fbf4043ba0c","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:05.785Z] [INFO] ::1 - - [18/Jul/2025:05:47:05 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-18T05:47:05.787Z] [INFO] Assessment submission received {"userId":"ff453c60-d4cc-4a3b-895b-58aea0d76994","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:05.788Z] [INFO] Token deduction successful {"userId":"b41051d1-550d-4d43-a899-ff6731e904ef","deductedAmount":1,"remainingBalance":1}
[2025-07-18T05:47:05.789Z] [INFO] Job created {"jobId":"f8c49fed-7ce2-40ba-ac23-e07e74119fb8","userId":"b41051d1-550d-4d43-a899-ff6731e904ef","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:05.789Z] [INFO] Assessment job published to queue {"jobId":"f8c49fed-7ce2-40ba-ac23-e07e74119fb8","userId":"b41051d1-550d-4d43-a899-ff6731e904ef","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:05.791Z] [INFO] Token deduction successful {"userId":"15902e84-9592-4b67-b3c2-1ec79e1dd54d","deductedAmount":1,"remainingBalance":1}
[2025-07-18T05:47:05.792Z] [INFO] Job created {"jobId":"3be7010f-69f1-498e-9937-41543fd9b95b","userId":"15902e84-9592-4b67-b3c2-1ec79e1dd54d","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:05.793Z] [INFO] Assessment job published to queue {"jobId":"3be7010f-69f1-498e-9937-41543fd9b95b","userId":"15902e84-9592-4b67-b3c2-1ec79e1dd54d","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:05.794Z] [INFO] Token deduction successful {"userId":"6c14d02a-a968-495c-889c-d9a3398c74a0","deductedAmount":1,"remainingBalance":1}
[2025-07-18T05:47:05.794Z] [INFO] Job created {"jobId":"c1e1db62-a1f5-46a0-9f11-ea780ddcb701","userId":"6c14d02a-a968-495c-889c-d9a3398c74a0","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:05.795Z] [INFO] Assessment job published to queue {"jobId":"c1e1db62-a1f5-46a0-9f11-ea780ddcb701","userId":"6c14d02a-a968-495c-889c-d9a3398c74a0","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:05.796Z] [INFO] Assessment submission received {"userId":"078de71f-122d-41ec-be55-b4b93c20d6b8","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:05.798Z] [INFO] ::1 - - [18/Jul/2025:05:47:05 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-18T05:47:05.802Z] [INFO] ::1 - - [18/Jul/2025:05:47:05 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-18T05:47:05.808Z] [INFO] ::1 - - [18/Jul/2025:05:47:05 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-18T05:47:05.813Z] [INFO] ::1 - - [18/Jul/2025:05:47:05 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-18T05:47:05.815Z] [INFO] ::1 - - [18/Jul/2025:05:47:05 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-18T05:47:05.818Z] [INFO] Token deduction successful {"userId":"842827f5-e6a0-464c-adee-d849b9ffb009","deductedAmount":1,"remainingBalance":1}
[2025-07-18T05:47:05.819Z] [INFO] Job created {"jobId":"99f2873d-6e84-4c6d-9c1b-f26342b7741a","userId":"842827f5-e6a0-464c-adee-d849b9ffb009","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:05.819Z] [INFO] Assessment job published to queue {"jobId":"99f2873d-6e84-4c6d-9c1b-f26342b7741a","userId":"842827f5-e6a0-464c-adee-d849b9ffb009","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:05.820Z] [INFO] Token deduction successful {"userId":"bb269993-1022-43b2-8926-4fbf4043ba0c","deductedAmount":1,"remainingBalance":1}
[2025-07-18T05:47:05.821Z] [INFO] Job created {"jobId":"f1cb8393-57b4-45a9-a177-ca1e6cc1c0df","userId":"bb269993-1022-43b2-8926-4fbf4043ba0c","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:05.823Z] [INFO] Assessment job published to queue {"jobId":"f1cb8393-57b4-45a9-a177-ca1e6cc1c0df","userId":"bb269993-1022-43b2-8926-4fbf4043ba0c","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:05.824Z] [INFO] Token deduction successful {"userId":"ff453c60-d4cc-4a3b-895b-58aea0d76994","deductedAmount":1,"remainingBalance":1}
[2025-07-18T05:47:05.825Z] [INFO] Job created {"jobId":"8958ea65-5ea0-4ba9-b087-0ad18ba0552e","userId":"ff453c60-d4cc-4a3b-895b-58aea0d76994","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:05.825Z] [INFO] Assessment job published to queue {"jobId":"8958ea65-5ea0-4ba9-b087-0ad18ba0552e","userId":"ff453c60-d4cc-4a3b-895b-58aea0d76994","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:05.826Z] [INFO] ::1 - - [18/Jul/2025:05:47:05 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-18T05:47:05.828Z] [INFO] Token deduction successful {"userId":"078de71f-122d-41ec-be55-b4b93c20d6b8","deductedAmount":1,"remainingBalance":1}
[2025-07-18T05:47:05.828Z] [INFO] Job created {"jobId":"0f5e5721-73b0-4a64-94a6-d372d1ed319e","userId":"078de71f-122d-41ec-be55-b4b93c20d6b8","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:05.828Z] [INFO] Assessment job published to queue {"jobId":"0f5e5721-73b0-4a64-94a6-d372d1ed319e","userId":"078de71f-122d-41ec-be55-b4b93c20d6b8","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:05.831Z] [INFO] ::1 - - [18/Jul/2025:05:47:05 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-18T05:47:05.832Z] [INFO] ::1 - - [18/Jul/2025:05:47:05 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-18T05:47:05.835Z] [INFO] ::1 - - [18/Jul/2025:05:47:05 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-18T05:47:38.142Z] [ERROR] User verification failed: Auth service unreachable {"userId":"da6e7756-6da7-4762-9478-15989c524ba5","error":"timeout of 10000ms exceeded","url":"http://localhost:3001"}
[2025-07-18T05:47:38.143Z] [ERROR] Authentication error {"error":"Authentication service is temporarily unavailable","stack":"AppError: Authentication service is temporarily unavailable\n    at Object.verifyUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\assessment-service\\src\\services\\authService.js:70:13)\n    at runNextTicks (node:internal/process/task_queues:65:5)\n    at listOnTimeout (node:internal/timers:549:9)\n    at process.processTimers (node:internal/timers:523:7)\n    at async authenticateToken (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\assessment-service\\src\\middleware\\auth.js:38:18)","ip":"::1","userAgent":"axios/1.10.0","url":"/assessments/submit"}
[2025-07-18T05:47:38.144Z] [INFO] ::1 - - [18/Jul/2025:05:47:38 +0000] "POST /assessments/submit HTTP/1.1" 401 96 "-" "axios/1.10.0"
[2025-07-18T05:47:38.157Z] [ERROR] User verification failed: Auth service unreachable {"userId":"b41051d1-550d-4d43-a899-ff6731e904ef","error":"timeout of 10000ms exceeded","url":"http://localhost:3001"}
[2025-07-18T05:47:38.158Z] [ERROR] Authentication error {"error":"Authentication service is temporarily unavailable","stack":"AppError: Authentication service is temporarily unavailable\n    at Object.verifyUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\assessment-service\\src\\services\\authService.js:70:13)\n    at runNextTicks (node:internal/process/task_queues:65:5)\n    at listOnTimeout (node:internal/timers:549:9)\n    at process.processTimers (node:internal/timers:523:7)\n    at async authenticateToken (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\assessment-service\\src\\middleware\\auth.js:38:18)","ip":"::1","userAgent":"axios/1.10.0","url":"/assessments/submit"}
[2025-07-18T05:47:38.159Z] [INFO] ::1 - - [18/Jul/2025:05:47:38 +0000] "POST /assessments/submit HTTP/1.1" 401 96 "-" "axios/1.10.0"
[2025-07-18T05:47:38.159Z] [ERROR] User verification failed: Auth service unreachable {"userId":"d9acc7c4-6671-4254-8f39-6034e3924c8b","error":"timeout of 10000ms exceeded","url":"http://localhost:3001"}
[2025-07-18T05:47:38.160Z] [ERROR] Authentication error {"error":"Authentication service is temporarily unavailable","stack":"AppError: Authentication service is temporarily unavailable\n    at Object.verifyUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\assessment-service\\src\\services\\authService.js:70:13)\n    at runNextTicks (node:internal/process/task_queues:65:5)\n    at listOnTimeout (node:internal/timers:549:9)\n    at process.processTimers (node:internal/timers:523:7)\n    at async authenticateToken (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\assessment-service\\src\\middleware\\auth.js:38:18)","ip":"::1","userAgent":"axios/1.10.0","url":"/assessments/submit"}
[2025-07-18T05:47:38.161Z] [INFO] ::1 - - [18/Jul/2025:05:47:38 +0000] "POST /assessments/submit HTTP/1.1" 401 96 "-" "axios/1.10.0"
[2025-07-18T05:47:38.161Z] [ERROR] User verification failed: Auth service unreachable {"userId":"15902e84-9592-4b67-b3c2-1ec79e1dd54d","error":"timeout of 10000ms exceeded","url":"http://localhost:3001"}
[2025-07-18T05:47:38.162Z] [ERROR] Authentication error {"error":"Authentication service is temporarily unavailable","stack":"AppError: Authentication service is temporarily unavailable\n    at Object.verifyUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\assessment-service\\src\\services\\authService.js:70:13)\n    at runNextTicks (node:internal/process/task_queues:65:5)\n    at listOnTimeout (node:internal/timers:549:9)\n    at process.processTimers (node:internal/timers:523:7)\n    at async authenticateToken (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\assessment-service\\src\\middleware\\auth.js:38:18)","ip":"::1","userAgent":"axios/1.10.0","url":"/assessments/submit"}
[2025-07-18T05:47:38.162Z] [INFO] ::1 - - [18/Jul/2025:05:47:38 +0000] "POST /assessments/submit HTTP/1.1" 401 96 "-" "axios/1.10.0"
[2025-07-18T05:47:38.163Z] [ERROR] User verification failed: Auth service unreachable {"userId":"a95c638e-a5c5-4d5a-bac3-440783832e88","error":"timeout of 10000ms exceeded","url":"http://localhost:3001"}
[2025-07-18T05:47:38.163Z] [ERROR] Authentication error {"error":"Authentication service is temporarily unavailable","stack":"AppError: Authentication service is temporarily unavailable\n    at Object.verifyUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\assessment-service\\src\\services\\authService.js:70:13)\n    at runNextTicks (node:internal/process/task_queues:65:5)\n    at listOnTimeout (node:internal/timers:549:9)\n    at process.processTimers (node:internal/timers:523:7)\n    at async authenticateToken (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\assessment-service\\src\\middleware\\auth.js:38:18)","ip":"::1","userAgent":"axios/1.10.0","url":"/assessments/submit"}
[2025-07-18T05:47:38.164Z] [INFO] ::1 - - [18/Jul/2025:05:47:38 +0000] "POST /assessments/submit HTTP/1.1" 401 96 "-" "axios/1.10.0"
[2025-07-18T05:47:38.167Z] [ERROR] User verification failed: Auth service unreachable {"userId":"6c14d02a-a968-495c-889c-d9a3398c74a0","error":"timeout of 10000ms exceeded","url":"http://localhost:3001"}
[2025-07-18T05:47:38.167Z] [ERROR] Authentication error {"error":"Authentication service is temporarily unavailable","stack":"AppError: Authentication service is temporarily unavailable\n    at Object.verifyUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\assessment-service\\src\\services\\authService.js:70:13)\n    at runNextTicks (node:internal/process/task_queues:65:5)\n    at listOnTimeout (node:internal/timers:549:9)\n    at process.processTimers (node:internal/timers:523:7)\n    at async authenticateToken (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\assessment-service\\src\\middleware\\auth.js:38:18)","ip":"::1","userAgent":"axios/1.10.0","url":"/assessments/submit"}
[2025-07-18T05:47:38.168Z] [INFO] ::1 - - [18/Jul/2025:05:47:38 +0000] "POST /assessments/submit HTTP/1.1" 401 96 "-" "axios/1.10.0"
[2025-07-18T05:47:38.169Z] [ERROR] User verification failed: Auth service unreachable {"userId":"2d1c9020-140a-46dc-842d-2ca54be7a854","error":"timeout of 10000ms exceeded","url":"http://localhost:3001"}
[2025-07-18T05:47:38.169Z] [ERROR] Authentication error {"error":"Authentication service is temporarily unavailable","stack":"AppError: Authentication service is temporarily unavailable\n    at Object.verifyUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\assessment-service\\src\\services\\authService.js:70:13)\n    at runNextTicks (node:internal/process/task_queues:65:5)\n    at listOnTimeout (node:internal/timers:549:9)\n    at process.processTimers (node:internal/timers:523:7)\n    at async authenticateToken (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\assessment-service\\src\\middleware\\auth.js:38:18)","ip":"::1","userAgent":"axios/1.10.0","url":"/assessments/submit"}
[2025-07-18T05:47:38.170Z] [INFO] ::1 - - [18/Jul/2025:05:47:38 +0000] "POST /assessments/submit HTTP/1.1" 401 96 "-" "axios/1.10.0"
[2025-07-18T05:47:38.171Z] [ERROR] User verification failed: Auth service unreachable {"userId":"842827f5-e6a0-464c-adee-d849b9ffb009","error":"timeout of 10000ms exceeded","url":"http://localhost:3001"}
[2025-07-18T05:47:38.172Z] [ERROR] Authentication error {"error":"Authentication service is temporarily unavailable","stack":"AppError: Authentication service is temporarily unavailable\n    at Object.verifyUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\assessment-service\\src\\services\\authService.js:70:13)\n    at runNextTicks (node:internal/process/task_queues:65:5)\n    at listOnTimeout (node:internal/timers:549:9)\n    at process.processTimers (node:internal/timers:523:7)\n    at async authenticateToken (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\assessment-service\\src\\middleware\\auth.js:38:18)","ip":"::1","userAgent":"axios/1.10.0","url":"/assessments/submit"}
[2025-07-18T05:47:38.174Z] [INFO] ::1 - - [18/Jul/2025:05:47:38 +0000] "POST /assessments/submit HTTP/1.1" 401 96 "-" "axios/1.10.0"
[2025-07-18T05:47:38.176Z] [ERROR] User verification failed: Auth service unreachable {"userId":"ff453c60-d4cc-4a3b-895b-58aea0d76994","error":"timeout of 10000ms exceeded","url":"http://localhost:3001"}
[2025-07-18T05:47:38.176Z] [ERROR] Authentication error {"error":"Authentication service is temporarily unavailable","stack":"AppError: Authentication service is temporarily unavailable\n    at Object.verifyUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\assessment-service\\src\\services\\authService.js:70:13)\n    at runNextTicks (node:internal/process/task_queues:65:5)\n    at listOnTimeout (node:internal/timers:549:9)\n    at process.processTimers (node:internal/timers:523:7)\n    at async authenticateToken (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\assessment-service\\src\\middleware\\auth.js:38:18)","ip":"::1","userAgent":"axios/1.10.0","url":"/assessments/submit"}
[2025-07-18T05:47:38.178Z] [INFO] ::1 - - [18/Jul/2025:05:47:38 +0000] "POST /assessments/submit HTTP/1.1" 401 96 "-" "axios/1.10.0"
[2025-07-18T05:47:38.179Z] [ERROR] User verification failed: Auth service unreachable {"userId":"078de71f-122d-41ec-be55-b4b93c20d6b8","error":"timeout of 10000ms exceeded","url":"http://localhost:3001"}
[2025-07-18T05:47:38.179Z] [ERROR] Authentication error {"error":"Authentication service is temporarily unavailable","stack":"AppError: Authentication service is temporarily unavailable\n    at Object.verifyUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\assessment-service\\src\\services\\authService.js:70:13)\n    at async authenticateToken (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\assessment-service\\src\\middleware\\auth.js:38:18)","ip":"::1","userAgent":"axios/1.10.0","url":"/assessments/submit"}
[2025-07-18T05:47:38.180Z] [INFO] ::1 - - [18/Jul/2025:05:47:38 +0000] "POST /assessments/submit HTTP/1.1" 401 96 "-" "axios/1.10.0"
[2025-07-18T05:47:38.181Z] [ERROR] User verification failed: Auth service unreachable {"userId":"b74f8249-1c2d-4db6-ad8d-6af943390e77","error":"timeout of 10000ms exceeded","url":"http://localhost:3001"}
[2025-07-18T05:47:38.181Z] [ERROR] Authentication error {"error":"Authentication service is temporarily unavailable","stack":"AppError: Authentication service is temporarily unavailable\n    at Object.verifyUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\assessment-service\\src\\services\\authService.js:70:13)\n    at runNextTicks (node:internal/process/task_queues:65:5)\n    at listOnTimeout (node:internal/timers:549:9)\n    at process.processTimers (node:internal/timers:523:7)\n    at async authenticateToken (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\assessment-service\\src\\middleware\\auth.js:38:18)","ip":"::1","userAgent":"axios/1.10.0","url":"/assessments/submit"}
[2025-07-18T05:47:38.182Z] [INFO] ::1 - - [18/Jul/2025:05:47:38 +0000] "POST /assessments/submit HTTP/1.1" 401 96 "-" "axios/1.10.0"
[2025-07-18T05:47:38.183Z] [ERROR] User verification failed: Auth service unreachable {"userId":"0ee6c0c4-05ae-4a76-8872-6bc82a9413df","error":"timeout of 10000ms exceeded","url":"http://localhost:3001"}
[2025-07-18T05:47:38.183Z] [ERROR] Authentication error {"error":"Authentication service is temporarily unavailable","stack":"AppError: Authentication service is temporarily unavailable\n    at Object.verifyUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\assessment-service\\src\\services\\authService.js:70:13)\n    at runNextTicks (node:internal/process/task_queues:65:5)\n    at listOnTimeout (node:internal/timers:549:9)\n    at process.processTimers (node:internal/timers:523:7)\n    at async authenticateToken (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\assessment-service\\src\\middleware\\auth.js:38:18)","ip":"::1","userAgent":"axios/1.10.0","url":"/assessments/submit"}
[2025-07-18T05:47:38.185Z] [INFO] ::1 - - [18/Jul/2025:05:47:38 +0000] "POST /assessments/submit HTTP/1.1" 401 96 "-" "axios/1.10.0"
[2025-07-18T05:47:38.185Z] [ERROR] User verification failed: Auth service unreachable {"userId":"43411672-8178-4b77-a062-73d1340d6dd3","error":"timeout of 10000ms exceeded","url":"http://localhost:3001"}
[2025-07-18T05:47:38.186Z] [ERROR] Authentication error {"error":"Authentication service is temporarily unavailable","stack":"AppError: Authentication service is temporarily unavailable\n    at Object.verifyUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\assessment-service\\src\\services\\authService.js:70:13)\n    at async authenticateToken (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\assessment-service\\src\\middleware\\auth.js:38:18)","ip":"::1","userAgent":"axios/1.10.0","url":"/assessments/submit"}
[2025-07-18T05:47:38.187Z] [INFO] ::1 - - [18/Jul/2025:05:47:38 +0000] "POST /assessments/submit HTTP/1.1" 401 96 "-" "axios/1.10.0"
[2025-07-18T05:47:38.187Z] [ERROR] User verification failed: Auth service unreachable {"userId":"ee52423e-4703-4517-8fe5-b8661b377999","error":"timeout of 10000ms exceeded","url":"http://localhost:3001"}
[2025-07-18T05:47:38.188Z] [ERROR] Authentication error {"error":"Authentication service is temporarily unavailable","stack":"AppError: Authentication service is temporarily unavailable\n    at Object.verifyUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\assessment-service\\src\\services\\authService.js:70:13)\n    at runNextTicks (node:internal/process/task_queues:65:5)\n    at listOnTimeout (node:internal/timers:549:9)\n    at process.processTimers (node:internal/timers:523:7)\n    at async authenticateToken (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\assessment-service\\src\\middleware\\auth.js:38:18)","ip":"::1","userAgent":"axios/1.10.0","url":"/assessments/submit"}
[2025-07-18T05:47:38.189Z] [INFO] ::1 - - [18/Jul/2025:05:47:38 +0000] "POST /assessments/submit HTTP/1.1" 401 96 "-" "axios/1.10.0"
[2025-07-18T05:47:38.189Z] [ERROR] User verification failed: Auth service unreachable {"userId":"8d16eed0-635c-4881-900f-1fdfcee24078","error":"timeout of 10000ms exceeded","url":"http://localhost:3001"}
[2025-07-18T05:47:38.190Z] [ERROR] Authentication error {"error":"Authentication service is temporarily unavailable","stack":"AppError: Authentication service is temporarily unavailable\n    at Object.verifyUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\assessment-service\\src\\services\\authService.js:70:13)\n    at runNextTicks (node:internal/process/task_queues:65:5)\n    at listOnTimeout (node:internal/timers:549:9)\n    at process.processTimers (node:internal/timers:523:7)\n    at async authenticateToken (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\assessment-service\\src\\middleware\\auth.js:38:18)","ip":"::1","userAgent":"axios/1.10.0","url":"/assessments/submit"}
[2025-07-18T05:47:38.191Z] [INFO] ::1 - - [18/Jul/2025:05:47:38 +0000] "POST /assessments/submit HTTP/1.1" 401 96 "-" "axios/1.10.0"
[2025-07-18T05:47:38.191Z] [ERROR] User verification failed: Auth service unreachable {"userId":"384e4e16-5ef1-4fc2-93c0-d0b08e952d12","error":"timeout of 10000ms exceeded","url":"http://localhost:3001"}
[2025-07-18T05:47:38.192Z] [ERROR] Authentication error {"error":"Authentication service is temporarily unavailable","stack":"AppError: Authentication service is temporarily unavailable\n    at Object.verifyUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\assessment-service\\src\\services\\authService.js:70:13)\n    at runNextTicks (node:internal/process/task_queues:65:5)\n    at listOnTimeout (node:internal/timers:549:9)\n    at process.processTimers (node:internal/timers:523:7)\n    at async authenticateToken (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\assessment-service\\src\\middleware\\auth.js:38:18)","ip":"::1","userAgent":"axios/1.10.0","url":"/assessments/submit"}
[2025-07-18T05:47:38.193Z] [INFO] ::1 - - [18/Jul/2025:05:47:38 +0000] "POST /assessments/submit HTTP/1.1" 401 96 "-" "axios/1.10.0"
[2025-07-18T05:47:38.193Z] [ERROR] User verification failed: Auth service unreachable {"userId":"b48bb1d1-7bf9-4262-8655-88c3c751a97e","error":"timeout of 10000ms exceeded","url":"http://localhost:3001"}
[2025-07-18T05:47:38.194Z] [ERROR] Authentication error {"error":"Authentication service is temporarily unavailable","stack":"AppError: Authentication service is temporarily unavailable\n    at Object.verifyUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\assessment-service\\src\\services\\authService.js:70:13)\n    at async authenticateToken (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\assessment-service\\src\\middleware\\auth.js:38:18)","ip":"::1","userAgent":"axios/1.10.0","url":"/assessments/submit"}
[2025-07-18T05:47:38.194Z] [INFO] ::1 - - [18/Jul/2025:05:47:38 +0000] "POST /assessments/submit HTTP/1.1" 401 96 "-" "axios/1.10.0"
[2025-07-18T05:47:42.404Z] [INFO] Assessment submission received {"userId":"bb269993-1022-43b2-8926-4fbf4043ba0c","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:42.406Z] [INFO] Assessment submission received {"userId":"8cd96f02-f438-4e30-b81d-df35181b1dd3","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:42.407Z] [INFO] Assessment submission received {"userId":"8f153039-6f2b-411e-af79-c636d53cc3f9","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:42.408Z] [INFO] Assessment submission received {"userId":"bc754f4a-5ac0-4053-8bba-a2a42a31a746","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:42.409Z] [INFO] Assessment submission received {"userId":"80b44903-76e5-4d53-9831-574ecab7f147","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:42.411Z] [INFO] Assessment submission received {"userId":"cb7a1876-d50d-4ac0-b263-6790e313d4f8","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:42.413Z] [INFO] Assessment submission received {"userId":"392fd33d-ab74-4da6-a91a-02c56656a57c","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.169Z] [INFO] Assessment submission received {"userId":"6bc0b5c1-151e-4b05-b743-4762063acbee","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.177Z] [INFO] Assessment submission received {"userId":"009e09fc-311a-4851-949e-b76859139743","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.184Z] [INFO] Assessment submission received {"userId":"040790f4-eeb4-46d7-84e7-768e0f8c500b","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.186Z] [INFO] Assessment submission received {"userId":"4aa44128-345e-4ebe-b3eb-39676d2596ce","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.188Z] [INFO] Assessment submission received {"userId":"12e2ba85-6226-4701-80f5-f7831eedfd48","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.189Z] [INFO] Assessment submission received {"userId":"b03799da-c6da-4925-87c6-901060f1ffc3","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.191Z] [INFO] Assessment submission received {"userId":"3dbea5fc-4925-4aa7-bfcd-318599a9e5b1","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.192Z] [INFO] Assessment submission received {"userId":"8f72b4c2-3265-4ba0-8f52-6a1ff9d5e497","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.194Z] [INFO] Assessment submission received {"userId":"7e1bb330-f26a-4a0f-a0df-b16e75e63d36","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.195Z] [INFO] Assessment submission received {"userId":"c52e498b-c989-4e27-b783-2fe3316085a3","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.197Z] [INFO] Assessment submission received {"userId":"3653691f-57cf-4782-8edf-157e46f19bd8","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.200Z] [INFO] Assessment submission received {"userId":"11556c6b-f185-42ce-a3b6-369dd56a6ac5","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.203Z] [INFO] Assessment submission received {"userId":"d7d7720a-b095-4f24-8dea-4b4fae817a5a","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.205Z] [INFO] Assessment submission received {"userId":"68abe24d-9fd9-4a34-9538-81ebed775b4c","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.207Z] [INFO] Assessment submission received {"userId":"ed022609-ebc6-41d0-a464-2dc10ca8773e","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.209Z] [INFO] Assessment submission received {"userId":"45f1e061-9c35-4aad-802e-6d1a58fbdaf0","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.211Z] [INFO] Assessment submission received {"userId":"f1b2443e-1fd7-40a2-b26e-************","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.221Z] [INFO] Assessment submission received {"userId":"24a91af5-f630-4d85-8a86-1863e3c977ec","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.223Z] [INFO] Assessment submission received {"userId":"c540dde8-d7e7-4b31-946b-c44073078958","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.225Z] [INFO] Assessment submission received {"userId":"849ecb09-081f-43d5-a71a-6bca85f6a8aa","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.227Z] [INFO] Assessment submission received {"userId":"b29a61ec-cda2-4ce1-9e63-a655109ba149","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.229Z] [INFO] Assessment submission received {"userId":"c760022b-5504-4436-b0df-12e523fb08cf","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.230Z] [INFO] Assessment submission received {"userId":"950a1c16-ba41-4ac6-bea8-b052c6bd32fb","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.232Z] [INFO] Assessment submission received {"userId":"b713b871-2151-4908-82c7-e25043087388","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.234Z] [INFO] Assessment submission received {"userId":"27046cf8-947c-460f-bcac-21f0770d22ec","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.236Z] [INFO] Assessment submission received {"userId":"15060bb5-db18-49b3-931e-9222c270e677","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.237Z] [INFO] Assessment submission received {"userId":"2280d0b1-52dd-4ea1-af58-caf1a0dc1ccb","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.239Z] [INFO] Assessment submission received {"userId":"36a5194c-32f2-4426-b5e4-76f19f18b956","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.241Z] [INFO] Assessment submission received {"userId":"8644861e-dabc-4b8b-a627-a0ae38bc44fe","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.243Z] [INFO] Assessment submission received {"userId":"b7572a8f-d3ba-410d-9871-a3439187285c","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.249Z] [INFO] Assessment submission received {"userId":"d6d1f30e-be74-494c-8a9b-bb501e91a0e3","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.254Z] [INFO] Assessment submission received {"userId":"31ffaa30-7eca-4caa-a144-e4d4184797f8","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.265Z] [INFO] Assessment submission received {"userId":"cc61ca36-c057-44e6-8dee-f275f24dc1c0","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.267Z] [INFO] Assessment submission received {"userId":"********-6bd8-4161-bbf0-e0bb0565df48","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.269Z] [INFO] Assessment submission received {"userId":"f47f861d-74bb-4198-a24d-8509841f9e80","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.271Z] [INFO] Assessment submission received {"userId":"072f69fa-6cbe-400b-b796-7e3271da936d","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.272Z] [INFO] Assessment submission received {"userId":"fd129d2c-39d3-404c-a103-21a379463fcb","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.273Z] [INFO] Assessment submission received {"userId":"b4bc5f99-ad7c-4687-83c5-9e9e21aeba64","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.275Z] [INFO] Assessment submission received {"userId":"9b4c5ed1-e673-4ee9-b696-5be44a561be0","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.277Z] [INFO] Assessment submission received {"userId":"a26a6dd4-23e9-46f7-b782-15f7859d8342","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.278Z] [INFO] Assessment submission received {"userId":"8c044fe4-4f97-4ace-8360-641d1fe1e52c","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.280Z] [INFO] Assessment submission received {"userId":"778981a0-fa31-4a3a-9d23-e9cbb9c0c952","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.281Z] [INFO] Assessment submission received {"userId":"********-9fe7-47c1-b50d-a0b5c5918171","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.283Z] [INFO] Assessment submission received {"userId":"5cc55727-a792-495d-ac51-0d0e844e5d53","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.284Z] [INFO] Assessment submission received {"userId":"68b8a7f4-00cf-4787-9396-f1983ed53648","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.285Z] [INFO] Assessment submission received {"userId":"727601f0-6746-4c23-b6af-d4d8f27c36b3","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.286Z] [INFO] Assessment submission received {"userId":"6761c652-3586-4274-8e32-44b45eb0ab1b","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.299Z] [INFO] Assessment submission received {"userId":"2db8eac1-765e-4011-bb8d-7b92904ac01b","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.301Z] [INFO] Assessment submission received {"userId":"f9b12420-847f-4968-b8b9-4153fdc8aae2","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.302Z] [INFO] Assessment submission received {"userId":"4f114070-2569-44d2-ae17-e0e84d825a51","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.303Z] [INFO] Assessment submission received {"userId":"c50c6b3a-274f-47ad-97e4-19a762a6aed6","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.305Z] [INFO] Assessment submission received {"userId":"44bf17ee-fb4d-4a0a-ab8c-ea5ac863032d","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.306Z] [INFO] Assessment submission received {"userId":"25a10f5b-94bb-4e95-b143-09ead677e663","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.307Z] [INFO] Assessment submission received {"userId":"4b9c8855-018c-44b3-89ef-8f0201454847","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.308Z] [INFO] Assessment submission received {"userId":"39ede2d4-b490-4c9a-b193-f3328fbd4857","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.310Z] [INFO] Assessment submission received {"userId":"ce779b7f-d252-445b-99cf-776c8d253223","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.311Z] [INFO] Assessment submission received {"userId":"9ae3d55e-01fd-4000-9698-d17c23b6e39c","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.312Z] [INFO] Assessment submission received {"userId":"8eaf3b43-8867-4573-91ec-926d03d93a07","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.313Z] [INFO] Assessment submission received {"userId":"77a4b43c-1906-4c6e-ab4b-7cf5ca9cc4fe","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.315Z] [INFO] Assessment submission received {"userId":"da9eee0f-2f64-4776-943c-45d0496b69e0","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.316Z] [INFO] Assessment submission received {"userId":"69ae9167-80c7-4164-8f08-14fc7e5f96b8","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.317Z] [INFO] Assessment submission received {"userId":"3de2f9e7-75b6-429a-90a4-b4366adf0874","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.318Z] [INFO] Assessment submission received {"userId":"ae217bd8-d2f3-4a8b-aac3-b3516790c2e2","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.320Z] [INFO] Assessment submission received {"userId":"829d69b3-2fdf-44eb-86a6-1d04b90cc581","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.321Z] [INFO] Assessment submission received {"userId":"7f361f12-3132-4693-8a61-f2ad8a8c6b0c","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.323Z] [INFO] Assessment submission received {"userId":"1beefbcc-ded3-4675-85df-6c1468d16c02","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.340Z] [INFO] Assessment submission received {"userId":"e5084c4d-a410-4bed-aaae-8b1e791b1180","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.342Z] [INFO] Assessment submission received {"userId":"50520da3-8855-4d53-bac8-711fc04cfd19","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.343Z] [INFO] Assessment submission received {"userId":"c0a5909b-84fd-4460-8b7d-76601a8a73e2","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.345Z] [INFO] Assessment submission received {"userId":"ee3c2f77-9ce0-4e25-a239-0ccdb4b80c24","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.346Z] [INFO] Assessment submission received {"userId":"63a9574c-b28b-4dc2-b0a6-0e009a92c1ae","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.347Z] [INFO] Assessment submission received {"userId":"62a862d9-1280-4b90-b469-db2e46dd6941","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.348Z] [INFO] Assessment submission received {"userId":"0d81b437-2a5b-45bb-87e3-b22638c79d2d","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.349Z] [INFO] Assessment submission received {"userId":"7dde7770-1e0e-481e-af32-84bc96240188","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.350Z] [INFO] Assessment submission received {"userId":"c48a95d5-717b-487b-b2ce-61d7e09e5b43","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.352Z] [INFO] Token deduction successful {"userId":"8cd96f02-f438-4e30-b81d-df35181b1dd3","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.352Z] [INFO] Job created {"jobId":"e9668692-e8f9-4212-be35-c7b70906154a","userId":"8cd96f02-f438-4e30-b81d-df35181b1dd3","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.352Z] [INFO] Assessment job published to queue {"jobId":"e9668692-e8f9-4212-be35-c7b70906154a","userId":"8cd96f02-f438-4e30-b81d-df35181b1dd3","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.353Z] [INFO] Token deduction successful {"userId":"8f153039-6f2b-411e-af79-c636d53cc3f9","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.354Z] [INFO] Job created {"jobId":"6f2fd8e4-b671-4eb2-a448-659faa922611","userId":"8f153039-6f2b-411e-af79-c636d53cc3f9","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.354Z] [INFO] Assessment job published to queue {"jobId":"6f2fd8e4-b671-4eb2-a448-659faa922611","userId":"8f153039-6f2b-411e-af79-c636d53cc3f9","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.355Z] [INFO] Token deduction successful {"userId":"bc754f4a-5ac0-4053-8bba-a2a42a31a746","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.355Z] [INFO] Job created {"jobId":"51d2d7f8-0216-46b9-9d68-19463f60cf73","userId":"bc754f4a-5ac0-4053-8bba-a2a42a31a746","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.355Z] [INFO] Assessment job published to queue {"jobId":"51d2d7f8-0216-46b9-9d68-19463f60cf73","userId":"bc754f4a-5ac0-4053-8bba-a2a42a31a746","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.356Z] [INFO] Token deduction successful {"userId":"80b44903-76e5-4d53-9831-574ecab7f147","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.356Z] [INFO] Job created {"jobId":"e91643f1-a51c-4c93-892c-d6dee18f9c14","userId":"80b44903-76e5-4d53-9831-574ecab7f147","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.357Z] [INFO] Assessment job published to queue {"jobId":"e91643f1-a51c-4c93-892c-d6dee18f9c14","userId":"80b44903-76e5-4d53-9831-574ecab7f147","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.357Z] [INFO] Token deduction successful {"userId":"cb7a1876-d50d-4ac0-b263-6790e313d4f8","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.358Z] [INFO] Job created {"jobId":"296471d2-b7eb-4324-a17a-78d362373419","userId":"cb7a1876-d50d-4ac0-b263-6790e313d4f8","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.358Z] [INFO] Assessment job published to queue {"jobId":"296471d2-b7eb-4324-a17a-78d362373419","userId":"cb7a1876-d50d-4ac0-b263-6790e313d4f8","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.359Z] [INFO] Token deduction successful {"userId":"bb269993-1022-43b2-8926-4fbf4043ba0c","deductedAmount":1,"remainingBalance":0}
[2025-07-18T05:47:45.360Z] [INFO] Job created {"jobId":"ed1114a3-aebc-419f-98d1-3a9a3b37c61a","userId":"bb269993-1022-43b2-8926-4fbf4043ba0c","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.361Z] [INFO] Assessment job published to queue {"jobId":"ed1114a3-aebc-419f-98d1-3a9a3b37c61a","userId":"bb269993-1022-43b2-8926-4fbf4043ba0c","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.362Z] [INFO] Assessment submission received {"userId":"4bcc4e0f-367a-4ecd-a233-ec3e9d48de38","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:47:45.363Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-18T05:47:45.364Z] [INFO] Token deduction successful {"userId":"392fd33d-ab74-4da6-a91a-02c56656a57c","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.364Z] [INFO] Job created {"jobId":"b3ba67e6-ad9d-407f-8e6c-1e2f422ad840","userId":"392fd33d-ab74-4da6-a91a-02c56656a57c","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.365Z] [INFO] Assessment job published to queue {"jobId":"b3ba67e6-ad9d-407f-8e6c-1e2f422ad840","userId":"392fd33d-ab74-4da6-a91a-02c56656a57c","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.371Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-18T05:47:45.374Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-18T05:47:45.377Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-18T05:47:45.380Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-18T05:47:45.384Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-18T05:47:45.387Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-18T05:47:45.549Z] [INFO] Token deduction successful {"userId":"12e2ba85-6226-4701-80f5-f7831eedfd48","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.550Z] [INFO] Job created {"jobId":"fafda137-2dfb-4947-bedc-5a44f3421a1f","userId":"12e2ba85-6226-4701-80f5-f7831eedfd48","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.550Z] [INFO] Assessment job published to queue {"jobId":"fafda137-2dfb-4947-bedc-5a44f3421a1f","userId":"12e2ba85-6226-4701-80f5-f7831eedfd48","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.552Z] [INFO] Token deduction successful {"userId":"b03799da-c6da-4925-87c6-901060f1ffc3","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.552Z] [INFO] Job created {"jobId":"fb1116b1-a5e1-4100-8ad5-1b99d2f5626f","userId":"b03799da-c6da-4925-87c6-901060f1ffc3","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.552Z] [INFO] Assessment job published to queue {"jobId":"fb1116b1-a5e1-4100-8ad5-1b99d2f5626f","userId":"b03799da-c6da-4925-87c6-901060f1ffc3","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.553Z] [INFO] Token deduction successful {"userId":"6bc0b5c1-151e-4b05-b743-4762063acbee","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.554Z] [INFO] Job created {"jobId":"417116ed-7c8b-4913-aa56-00549a16a439","userId":"6bc0b5c1-151e-4b05-b743-4762063acbee","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.554Z] [INFO] Assessment job published to queue {"jobId":"417116ed-7c8b-4913-aa56-00549a16a439","userId":"6bc0b5c1-151e-4b05-b743-4762063acbee","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.555Z] [INFO] Token deduction successful {"userId":"009e09fc-311a-4851-949e-b76859139743","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.556Z] [INFO] Job created {"jobId":"be02616f-a58b-481e-b2aa-894bf4ea9404","userId":"009e09fc-311a-4851-949e-b76859139743","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.556Z] [INFO] Assessment job published to queue {"jobId":"be02616f-a58b-481e-b2aa-894bf4ea9404","userId":"009e09fc-311a-4851-949e-b76859139743","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.557Z] [INFO] Token deduction successful {"userId":"040790f4-eeb4-46d7-84e7-768e0f8c500b","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.557Z] [INFO] Job created {"jobId":"2c6cceb3-6d75-45b0-8fd0-bda0b708105a","userId":"040790f4-eeb4-46d7-84e7-768e0f8c500b","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.558Z] [INFO] Assessment job published to queue {"jobId":"2c6cceb3-6d75-45b0-8fd0-bda0b708105a","userId":"040790f4-eeb4-46d7-84e7-768e0f8c500b","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.558Z] [INFO] Token deduction successful {"userId":"4aa44128-345e-4ebe-b3eb-39676d2596ce","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.559Z] [INFO] Job created {"jobId":"16fa9e36-9332-4595-bc6e-1e0c67173dae","userId":"4aa44128-345e-4ebe-b3eb-39676d2596ce","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.560Z] [INFO] Assessment job published to queue {"jobId":"16fa9e36-9332-4595-bc6e-1e0c67173dae","userId":"4aa44128-345e-4ebe-b3eb-39676d2596ce","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.560Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-18T05:47:45.564Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-18T05:47:45.568Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-18T05:47:45.569Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-18T05:47:45.572Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-18T05:47:45.573Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-18T05:47:45.575Z] [INFO] Token deduction successful {"userId":"3dbea5fc-4925-4aa7-bfcd-318599a9e5b1","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.575Z] [INFO] Job created {"jobId":"e6e2a0b1-f712-415b-b838-843d3cf541a8","userId":"3dbea5fc-4925-4aa7-bfcd-318599a9e5b1","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.576Z] [INFO] Assessment job published to queue {"jobId":"e6e2a0b1-f712-415b-b838-843d3cf541a8","userId":"3dbea5fc-4925-4aa7-bfcd-318599a9e5b1","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.577Z] [INFO] Token deduction successful {"userId":"8f72b4c2-3265-4ba0-8f52-6a1ff9d5e497","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.577Z] [INFO] Job created {"jobId":"6c8f18fd-0dba-4a94-8bf0-b804667cc62b","userId":"8f72b4c2-3265-4ba0-8f52-6a1ff9d5e497","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.578Z] [INFO] Assessment job published to queue {"jobId":"6c8f18fd-0dba-4a94-8bf0-b804667cc62b","userId":"8f72b4c2-3265-4ba0-8f52-6a1ff9d5e497","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.579Z] [INFO] Token deduction successful {"userId":"7e1bb330-f26a-4a0f-a0df-b16e75e63d36","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.580Z] [INFO] Job created {"jobId":"e871fb5e-1e2a-46c1-9a2a-04081585be3a","userId":"7e1bb330-f26a-4a0f-a0df-b16e75e63d36","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.580Z] [INFO] Assessment job published to queue {"jobId":"e871fb5e-1e2a-46c1-9a2a-04081585be3a","userId":"7e1bb330-f26a-4a0f-a0df-b16e75e63d36","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.581Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-18T05:47:45.582Z] [INFO] Token deduction successful {"userId":"c52e498b-c989-4e27-b783-2fe3316085a3","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.582Z] [INFO] Job created {"jobId":"c643f010-a909-48da-bf1f-0bb678ca8eba","userId":"c52e498b-c989-4e27-b783-2fe3316085a3","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.583Z] [INFO] Assessment job published to queue {"jobId":"c643f010-a909-48da-bf1f-0bb678ca8eba","userId":"c52e498b-c989-4e27-b783-2fe3316085a3","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.585Z] [INFO] Token deduction successful {"userId":"3653691f-57cf-4782-8edf-157e46f19bd8","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.585Z] [INFO] Job created {"jobId":"3e09ab89-6060-4e6a-b3c0-d7a08e2dc63e","userId":"3653691f-57cf-4782-8edf-157e46f19bd8","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.586Z] [INFO] Assessment job published to queue {"jobId":"3e09ab89-6060-4e6a-b3c0-d7a08e2dc63e","userId":"3653691f-57cf-4782-8edf-157e46f19bd8","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.586Z] [INFO] Token deduction successful {"userId":"11556c6b-f185-42ce-a3b6-369dd56a6ac5","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.587Z] [INFO] Job created {"jobId":"29c86e36-8f56-4b6e-864d-e5ab9d9c0eb8","userId":"11556c6b-f185-42ce-a3b6-369dd56a6ac5","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.587Z] [INFO] Assessment job published to queue {"jobId":"29c86e36-8f56-4b6e-864d-e5ab9d9c0eb8","userId":"11556c6b-f185-42ce-a3b6-369dd56a6ac5","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.588Z] [INFO] Token deduction successful {"userId":"d7d7720a-b095-4f24-8dea-4b4fae817a5a","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.588Z] [INFO] Job created {"jobId":"bb463fb3-2f18-45d5-a2fc-40fce7c4a8e2","userId":"d7d7720a-b095-4f24-8dea-4b4fae817a5a","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.589Z] [INFO] Assessment job published to queue {"jobId":"bb463fb3-2f18-45d5-a2fc-40fce7c4a8e2","userId":"d7d7720a-b095-4f24-8dea-4b4fae817a5a","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.590Z] [INFO] Token deduction successful {"userId":"68abe24d-9fd9-4a34-9538-81ebed775b4c","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.590Z] [INFO] Job created {"jobId":"4eca1c5a-2ffb-4996-9416-fd359c63a8a6","userId":"68abe24d-9fd9-4a34-9538-81ebed775b4c","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.591Z] [INFO] Assessment job published to queue {"jobId":"4eca1c5a-2ffb-4996-9416-fd359c63a8a6","userId":"68abe24d-9fd9-4a34-9538-81ebed775b4c","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.592Z] [INFO] Token deduction successful {"userId":"ed022609-ebc6-41d0-a464-2dc10ca8773e","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.592Z] [INFO] Job created {"jobId":"20cc46b5-f83d-4e2b-86ef-fed571528a50","userId":"ed022609-ebc6-41d0-a464-2dc10ca8773e","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.593Z] [INFO] Assessment job published to queue {"jobId":"20cc46b5-f83d-4e2b-86ef-fed571528a50","userId":"ed022609-ebc6-41d0-a464-2dc10ca8773e","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.594Z] [INFO] Token deduction successful {"userId":"45f1e061-9c35-4aad-802e-6d1a58fbdaf0","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.595Z] [INFO] Job created {"jobId":"02e87e37-d55a-4a83-902d-0389dddab2fa","userId":"45f1e061-9c35-4aad-802e-6d1a58fbdaf0","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.595Z] [INFO] Assessment job published to queue {"jobId":"02e87e37-d55a-4a83-902d-0389dddab2fa","userId":"45f1e061-9c35-4aad-802e-6d1a58fbdaf0","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.597Z] [INFO] Token deduction successful {"userId":"f1b2443e-1fd7-40a2-b26e-************","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.598Z] [INFO] Job created {"jobId":"efb0205a-4ac9-4a67-8e64-d512cd142df7","userId":"f1b2443e-1fd7-40a2-b26e-************","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.598Z] [INFO] Assessment job published to queue {"jobId":"efb0205a-4ac9-4a67-8e64-d512cd142df7","userId":"f1b2443e-1fd7-40a2-b26e-************","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.599Z] [INFO] Token deduction successful {"userId":"24a91af5-f630-4d85-8a86-1863e3c977ec","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.600Z] [INFO] Job created {"jobId":"7870dc08-7870-4d93-bb9a-6b419a330294","userId":"24a91af5-f630-4d85-8a86-1863e3c977ec","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.600Z] [INFO] Assessment job published to queue {"jobId":"7870dc08-7870-4d93-bb9a-6b419a330294","userId":"24a91af5-f630-4d85-8a86-1863e3c977ec","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.601Z] [INFO] Token deduction successful {"userId":"c540dde8-d7e7-4b31-946b-c44073078958","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.601Z] [INFO] Job created {"jobId":"2b28451c-1940-4deb-b6c9-fb05b72d2eba","userId":"c540dde8-d7e7-4b31-946b-c44073078958","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.602Z] [INFO] Assessment job published to queue {"jobId":"2b28451c-1940-4deb-b6c9-fb05b72d2eba","userId":"c540dde8-d7e7-4b31-946b-c44073078958","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.603Z] [INFO] Token deduction successful {"userId":"849ecb09-081f-43d5-a71a-6bca85f6a8aa","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.603Z] [INFO] Job created {"jobId":"ec60db2c-393f-4432-b87d-f246f19161ce","userId":"849ecb09-081f-43d5-a71a-6bca85f6a8aa","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.604Z] [INFO] Assessment job published to queue {"jobId":"ec60db2c-393f-4432-b87d-f246f19161ce","userId":"849ecb09-081f-43d5-a71a-6bca85f6a8aa","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.604Z] [INFO] Token deduction successful {"userId":"b29a61ec-cda2-4ce1-9e63-a655109ba149","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.605Z] [INFO] Job created {"jobId":"fd3e8d9b-c646-4292-8a0b-ccfe32e6e8b2","userId":"b29a61ec-cda2-4ce1-9e63-a655109ba149","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.605Z] [INFO] Assessment job published to queue {"jobId":"fd3e8d9b-c646-4292-8a0b-ccfe32e6e8b2","userId":"b29a61ec-cda2-4ce1-9e63-a655109ba149","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.606Z] [INFO] Token deduction successful {"userId":"c760022b-5504-4436-b0df-12e523fb08cf","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.606Z] [INFO] Job created {"jobId":"0531a284-96f0-4593-b0e4-76fc83203b8e","userId":"c760022b-5504-4436-b0df-12e523fb08cf","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.607Z] [INFO] Assessment job published to queue {"jobId":"0531a284-96f0-4593-b0e4-76fc83203b8e","userId":"c760022b-5504-4436-b0df-12e523fb08cf","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.608Z] [INFO] Token deduction successful {"userId":"950a1c16-ba41-4ac6-bea8-b052c6bd32fb","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.608Z] [INFO] Job created {"jobId":"90ab426b-e3af-4374-bc4d-96c7580b6e1b","userId":"950a1c16-ba41-4ac6-bea8-b052c6bd32fb","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.609Z] [INFO] Assessment job published to queue {"jobId":"90ab426b-e3af-4374-bc4d-96c7580b6e1b","userId":"950a1c16-ba41-4ac6-bea8-b052c6bd32fb","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.610Z] [INFO] Token deduction successful {"userId":"b713b871-2151-4908-82c7-e25043087388","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.610Z] [INFO] Job created {"jobId":"aca2d0cc-dcf2-443d-8a32-a5bb4c608213","userId":"b713b871-2151-4908-82c7-e25043087388","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.611Z] [INFO] Assessment job published to queue {"jobId":"aca2d0cc-dcf2-443d-8a32-a5bb4c608213","userId":"b713b871-2151-4908-82c7-e25043087388","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.611Z] [INFO] Token deduction successful {"userId":"27046cf8-947c-460f-bcac-21f0770d22ec","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.612Z] [INFO] Job created {"jobId":"d4bede51-d3bd-4269-ac73-01f49f528d08","userId":"27046cf8-947c-460f-bcac-21f0770d22ec","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.612Z] [INFO] Assessment job published to queue {"jobId":"d4bede51-d3bd-4269-ac73-01f49f528d08","userId":"27046cf8-947c-460f-bcac-21f0770d22ec","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.613Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-18T05:47:45.614Z] [INFO] Token deduction successful {"userId":"15060bb5-db18-49b3-931e-9222c270e677","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.615Z] [INFO] Job created {"jobId":"a5eb9a84-2a5e-412c-a829-0a76b1fa8cc1","userId":"15060bb5-db18-49b3-931e-9222c270e677","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.615Z] [INFO] Assessment job published to queue {"jobId":"a5eb9a84-2a5e-412c-a829-0a76b1fa8cc1","userId":"15060bb5-db18-49b3-931e-9222c270e677","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.616Z] [INFO] Token deduction successful {"userId":"d6d1f30e-be74-494c-8a9b-bb501e91a0e3","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.616Z] [INFO] Job created {"jobId":"472738dc-e3de-430e-8671-cdb3a4bc1ca3","userId":"d6d1f30e-be74-494c-8a9b-bb501e91a0e3","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.617Z] [INFO] Assessment job published to queue {"jobId":"472738dc-e3de-430e-8671-cdb3a4bc1ca3","userId":"d6d1f30e-be74-494c-8a9b-bb501e91a0e3","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.618Z] [INFO] Token deduction successful {"userId":"31ffaa30-7eca-4caa-a144-e4d4184797f8","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.618Z] [INFO] Job created {"jobId":"f2bb542a-ada9-4639-8347-e1d269b0a60d","userId":"31ffaa30-7eca-4caa-a144-e4d4184797f8","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.618Z] [INFO] Assessment job published to queue {"jobId":"f2bb542a-ada9-4639-8347-e1d269b0a60d","userId":"31ffaa30-7eca-4caa-a144-e4d4184797f8","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.619Z] [INFO] Token deduction successful {"userId":"cc61ca36-c057-44e6-8dee-f275f24dc1c0","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.619Z] [INFO] Job created {"jobId":"506efc46-0044-489c-8f5c-df7f522457dc","userId":"cc61ca36-c057-44e6-8dee-f275f24dc1c0","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.620Z] [INFO] Assessment job published to queue {"jobId":"506efc46-0044-489c-8f5c-df7f522457dc","userId":"cc61ca36-c057-44e6-8dee-f275f24dc1c0","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.621Z] [INFO] Token deduction successful {"userId":"********-6bd8-4161-bbf0-e0bb0565df48","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.621Z] [INFO] Job created {"jobId":"4a9d0fce-f62a-4624-a8a6-9b41700464e9","userId":"********-6bd8-4161-bbf0-e0bb0565df48","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.622Z] [INFO] Assessment job published to queue {"jobId":"4a9d0fce-f62a-4624-a8a6-9b41700464e9","userId":"********-6bd8-4161-bbf0-e0bb0565df48","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.622Z] [INFO] Token deduction successful {"userId":"f47f861d-74bb-4198-a24d-8509841f9e80","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.623Z] [INFO] Job created {"jobId":"e4b7fd1e-9f49-44e3-82d0-6f725f14d3a1","userId":"f47f861d-74bb-4198-a24d-8509841f9e80","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.623Z] [INFO] Assessment job published to queue {"jobId":"e4b7fd1e-9f49-44e3-82d0-6f725f14d3a1","userId":"f47f861d-74bb-4198-a24d-8509841f9e80","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.624Z] [INFO] Token deduction successful {"userId":"072f69fa-6cbe-400b-b796-7e3271da936d","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.624Z] [INFO] Job created {"jobId":"1418fbbe-d7be-43b7-93f4-bc05f3a0406c","userId":"072f69fa-6cbe-400b-b796-7e3271da936d","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.625Z] [INFO] Assessment job published to queue {"jobId":"1418fbbe-d7be-43b7-93f4-bc05f3a0406c","userId":"072f69fa-6cbe-400b-b796-7e3271da936d","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.626Z] [INFO] Token deduction successful {"userId":"fd129d2c-39d3-404c-a103-21a379463fcb","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.626Z] [INFO] Job created {"jobId":"fe714dbf-bea6-4d59-8b14-eecc1bbc1411","userId":"fd129d2c-39d3-404c-a103-21a379463fcb","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.627Z] [INFO] Assessment job published to queue {"jobId":"fe714dbf-bea6-4d59-8b14-eecc1bbc1411","userId":"fd129d2c-39d3-404c-a103-21a379463fcb","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.628Z] [INFO] Token deduction successful {"userId":"b4bc5f99-ad7c-4687-83c5-9e9e21aeba64","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.629Z] [INFO] Job created {"jobId":"992ee829-8114-489c-9c8e-40ece18550a3","userId":"b4bc5f99-ad7c-4687-83c5-9e9e21aeba64","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.629Z] [INFO] Assessment job published to queue {"jobId":"992ee829-8114-489c-9c8e-40ece18550a3","userId":"b4bc5f99-ad7c-4687-83c5-9e9e21aeba64","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.630Z] [INFO] Token deduction successful {"userId":"9b4c5ed1-e673-4ee9-b696-5be44a561be0","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.630Z] [INFO] Job created {"jobId":"8156b2af-e634-4eb4-a633-f75da0487db1","userId":"9b4c5ed1-e673-4ee9-b696-5be44a561be0","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.631Z] [INFO] Assessment job published to queue {"jobId":"8156b2af-e634-4eb4-a633-f75da0487db1","userId":"9b4c5ed1-e673-4ee9-b696-5be44a561be0","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.632Z] [INFO] Token deduction successful {"userId":"2280d0b1-52dd-4ea1-af58-caf1a0dc1ccb","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.632Z] [INFO] Job created {"jobId":"83d1a7c4-fb36-485f-a29a-72d47c66ee67","userId":"2280d0b1-52dd-4ea1-af58-caf1a0dc1ccb","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.633Z] [INFO] Assessment job published to queue {"jobId":"83d1a7c4-fb36-485f-a29a-72d47c66ee67","userId":"2280d0b1-52dd-4ea1-af58-caf1a0dc1ccb","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.634Z] [INFO] Token deduction successful {"userId":"36a5194c-32f2-4426-b5e4-76f19f18b956","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.634Z] [INFO] Job created {"jobId":"205fc9f9-8a40-4c1b-9996-5c5dc71694bc","userId":"36a5194c-32f2-4426-b5e4-76f19f18b956","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.634Z] [INFO] Assessment job published to queue {"jobId":"205fc9f9-8a40-4c1b-9996-5c5dc71694bc","userId":"36a5194c-32f2-4426-b5e4-76f19f18b956","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.635Z] [INFO] Token deduction successful {"userId":"8644861e-dabc-4b8b-a627-a0ae38bc44fe","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.635Z] [INFO] Job created {"jobId":"bc08ec3c-681f-44f7-8c4f-2cf747cbca54","userId":"8644861e-dabc-4b8b-a627-a0ae38bc44fe","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.636Z] [INFO] Assessment job published to queue {"jobId":"bc08ec3c-681f-44f7-8c4f-2cf747cbca54","userId":"8644861e-dabc-4b8b-a627-a0ae38bc44fe","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.636Z] [INFO] Token deduction successful {"userId":"b7572a8f-d3ba-410d-9871-a3439187285c","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.637Z] [INFO] Job created {"jobId":"9d85c017-5dc3-4581-a34d-d336008fd414","userId":"b7572a8f-d3ba-410d-9871-a3439187285c","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.637Z] [INFO] Assessment job published to queue {"jobId":"9d85c017-5dc3-4581-a34d-d336008fd414","userId":"b7572a8f-d3ba-410d-9871-a3439187285c","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.638Z] [INFO] Token deduction successful {"userId":"a26a6dd4-23e9-46f7-b782-15f7859d8342","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.638Z] [INFO] Job created {"jobId":"f3111154-e2e7-4949-82f1-ea5776f97cab","userId":"a26a6dd4-23e9-46f7-b782-15f7859d8342","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.639Z] [INFO] Assessment job published to queue {"jobId":"f3111154-e2e7-4949-82f1-ea5776f97cab","userId":"a26a6dd4-23e9-46f7-b782-15f7859d8342","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.640Z] [INFO] Token deduction successful {"userId":"********-9fe7-47c1-b50d-a0b5c5918171","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.641Z] [INFO] Job created {"jobId":"00b2690b-3f04-4af6-82e7-4bf208b6eca2","userId":"********-9fe7-47c1-b50d-a0b5c5918171","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.641Z] [INFO] Assessment job published to queue {"jobId":"00b2690b-3f04-4af6-82e7-4bf208b6eca2","userId":"********-9fe7-47c1-b50d-a0b5c5918171","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.642Z] [INFO] Token deduction successful {"userId":"5cc55727-a792-495d-ac51-0d0e844e5d53","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.642Z] [INFO] Job created {"jobId":"c2fc42ee-3232-4c64-9095-46b80854611f","userId":"5cc55727-a792-495d-ac51-0d0e844e5d53","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.643Z] [INFO] Assessment job published to queue {"jobId":"c2fc42ee-3232-4c64-9095-46b80854611f","userId":"5cc55727-a792-495d-ac51-0d0e844e5d53","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.643Z] [INFO] Token deduction successful {"userId":"68b8a7f4-00cf-4787-9396-f1983ed53648","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.644Z] [INFO] Job created {"jobId":"b66de2d6-4bb2-4b56-b703-23195898b34a","userId":"68b8a7f4-00cf-4787-9396-f1983ed53648","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.644Z] [INFO] Assessment job published to queue {"jobId":"b66de2d6-4bb2-4b56-b703-23195898b34a","userId":"68b8a7f4-00cf-4787-9396-f1983ed53648","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.645Z] [INFO] Token deduction successful {"userId":"727601f0-6746-4c23-b6af-d4d8f27c36b3","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.645Z] [INFO] Job created {"jobId":"531eb1c6-36d4-46c2-975e-a182060ecd75","userId":"727601f0-6746-4c23-b6af-d4d8f27c36b3","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.646Z] [INFO] Assessment job published to queue {"jobId":"531eb1c6-36d4-46c2-975e-a182060ecd75","userId":"727601f0-6746-4c23-b6af-d4d8f27c36b3","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.646Z] [INFO] Token deduction successful {"userId":"2db8eac1-765e-4011-bb8d-7b92904ac01b","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.646Z] [INFO] Job created {"jobId":"2eef24e2-9b02-4174-8550-e23e5de814d3","userId":"2db8eac1-765e-4011-bb8d-7b92904ac01b","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.647Z] [INFO] Assessment job published to queue {"jobId":"2eef24e2-9b02-4174-8550-e23e5de814d3","userId":"2db8eac1-765e-4011-bb8d-7b92904ac01b","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.647Z] [INFO] Token deduction successful {"userId":"f9b12420-847f-4968-b8b9-4153fdc8aae2","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.648Z] [INFO] Job created {"jobId":"fe7c8560-8c4e-4a4a-9f67-3947e20b605b","userId":"f9b12420-847f-4968-b8b9-4153fdc8aae2","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.648Z] [INFO] Assessment job published to queue {"jobId":"fe7c8560-8c4e-4a4a-9f67-3947e20b605b","userId":"f9b12420-847f-4968-b8b9-4153fdc8aae2","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.649Z] [INFO] Token deduction successful {"userId":"4f114070-2569-44d2-ae17-e0e84d825a51","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.649Z] [INFO] Job created {"jobId":"418f211b-e163-4ba8-b13f-74d7d53c7496","userId":"4f114070-2569-44d2-ae17-e0e84d825a51","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.651Z] [INFO] Assessment job published to queue {"jobId":"418f211b-e163-4ba8-b13f-74d7d53c7496","userId":"4f114070-2569-44d2-ae17-e0e84d825a51","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.652Z] [INFO] Token deduction successful {"userId":"c50c6b3a-274f-47ad-97e4-19a762a6aed6","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.652Z] [INFO] Job created {"jobId":"77f7a0dc-b005-4e9d-a5c9-fc4b5548431d","userId":"c50c6b3a-274f-47ad-97e4-19a762a6aed6","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.652Z] [INFO] Assessment job published to queue {"jobId":"77f7a0dc-b005-4e9d-a5c9-fc4b5548431d","userId":"c50c6b3a-274f-47ad-97e4-19a762a6aed6","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.653Z] [INFO] Token deduction successful {"userId":"44bf17ee-fb4d-4a0a-ab8c-ea5ac863032d","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.653Z] [INFO] Job created {"jobId":"464191fe-682e-4b40-8340-84f10634ada9","userId":"44bf17ee-fb4d-4a0a-ab8c-ea5ac863032d","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.654Z] [INFO] Assessment job published to queue {"jobId":"464191fe-682e-4b40-8340-84f10634ada9","userId":"44bf17ee-fb4d-4a0a-ab8c-ea5ac863032d","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.654Z] [INFO] Token deduction successful {"userId":"25a10f5b-94bb-4e95-b143-09ead677e663","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.655Z] [INFO] Job created {"jobId":"6b92acb0-8333-4ead-8cdd-56982fde8dce","userId":"25a10f5b-94bb-4e95-b143-09ead677e663","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.655Z] [INFO] Assessment job published to queue {"jobId":"6b92acb0-8333-4ead-8cdd-56982fde8dce","userId":"25a10f5b-94bb-4e95-b143-09ead677e663","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.656Z] [INFO] Token deduction successful {"userId":"4b9c8855-018c-44b3-89ef-8f0201454847","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.656Z] [INFO] Job created {"jobId":"2945c4ec-edf8-4d6a-8b36-73991e0fc36a","userId":"4b9c8855-018c-44b3-89ef-8f0201454847","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.657Z] [INFO] Assessment job published to queue {"jobId":"2945c4ec-edf8-4d6a-8b36-73991e0fc36a","userId":"4b9c8855-018c-44b3-89ef-8f0201454847","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.657Z] [INFO] Token deduction successful {"userId":"39ede2d4-b490-4c9a-b193-f3328fbd4857","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.658Z] [INFO] Job created {"jobId":"37b217c7-1770-47a3-9496-648091e1cf7c","userId":"39ede2d4-b490-4c9a-b193-f3328fbd4857","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.658Z] [INFO] Assessment job published to queue {"jobId":"37b217c7-1770-47a3-9496-648091e1cf7c","userId":"39ede2d4-b490-4c9a-b193-f3328fbd4857","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.659Z] [INFO] Token deduction successful {"userId":"ce779b7f-d252-445b-99cf-776c8d253223","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.659Z] [INFO] Job created {"jobId":"6a546621-9c70-43a7-acd9-45e6b47c80b1","userId":"ce779b7f-d252-445b-99cf-776c8d253223","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.659Z] [INFO] Assessment job published to queue {"jobId":"6a546621-9c70-43a7-acd9-45e6b47c80b1","userId":"ce779b7f-d252-445b-99cf-776c8d253223","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.660Z] [INFO] Token deduction successful {"userId":"9ae3d55e-01fd-4000-9698-d17c23b6e39c","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.660Z] [INFO] Job created {"jobId":"6a3d6e80-dd87-45df-8de0-40ae2c149148","userId":"9ae3d55e-01fd-4000-9698-d17c23b6e39c","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.661Z] [INFO] Assessment job published to queue {"jobId":"6a3d6e80-dd87-45df-8de0-40ae2c149148","userId":"9ae3d55e-01fd-4000-9698-d17c23b6e39c","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.661Z] [INFO] Token deduction successful {"userId":"8eaf3b43-8867-4573-91ec-926d03d93a07","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.662Z] [INFO] Job created {"jobId":"e6392f8a-a825-48b3-8bac-13387e57d264","userId":"8eaf3b43-8867-4573-91ec-926d03d93a07","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.662Z] [INFO] Assessment job published to queue {"jobId":"e6392f8a-a825-48b3-8bac-13387e57d264","userId":"8eaf3b43-8867-4573-91ec-926d03d93a07","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.663Z] [INFO] Token deduction successful {"userId":"77a4b43c-1906-4c6e-ab4b-7cf5ca9cc4fe","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.663Z] [INFO] Job created {"jobId":"c6c29de9-54c8-4497-b044-25e0e32acb56","userId":"77a4b43c-1906-4c6e-ab4b-7cf5ca9cc4fe","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.663Z] [INFO] Assessment job published to queue {"jobId":"c6c29de9-54c8-4497-b044-25e0e32acb56","userId":"77a4b43c-1906-4c6e-ab4b-7cf5ca9cc4fe","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.664Z] [INFO] Token deduction successful {"userId":"da9eee0f-2f64-4776-943c-45d0496b69e0","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.664Z] [INFO] Job created {"jobId":"a5b78f8a-e8ab-4232-964c-d2f964ce877e","userId":"da9eee0f-2f64-4776-943c-45d0496b69e0","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.664Z] [INFO] Assessment job published to queue {"jobId":"a5b78f8a-e8ab-4232-964c-d2f964ce877e","userId":"da9eee0f-2f64-4776-943c-45d0496b69e0","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.665Z] [INFO] Token deduction successful {"userId":"69ae9167-80c7-4164-8f08-14fc7e5f96b8","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.665Z] [INFO] Job created {"jobId":"a0f8654b-b285-4d67-87bb-c95c4a743c7f","userId":"69ae9167-80c7-4164-8f08-14fc7e5f96b8","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.666Z] [INFO] Assessment job published to queue {"jobId":"a0f8654b-b285-4d67-87bb-c95c4a743c7f","userId":"69ae9167-80c7-4164-8f08-14fc7e5f96b8","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.666Z] [INFO] Token deduction successful {"userId":"8c044fe4-4f97-4ace-8360-641d1fe1e52c","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.666Z] [INFO] Job created {"jobId":"931a64f5-7a25-4c4f-a818-3a501738b98c","userId":"8c044fe4-4f97-4ace-8360-641d1fe1e52c","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.667Z] [INFO] Assessment job published to queue {"jobId":"931a64f5-7a25-4c4f-a818-3a501738b98c","userId":"8c044fe4-4f97-4ace-8360-641d1fe1e52c","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.667Z] [INFO] Token deduction successful {"userId":"778981a0-fa31-4a3a-9d23-e9cbb9c0c952","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.668Z] [INFO] Job created {"jobId":"be1703cb-15ec-4d17-9327-c71353b0f823","userId":"778981a0-fa31-4a3a-9d23-e9cbb9c0c952","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.668Z] [INFO] Assessment job published to queue {"jobId":"be1703cb-15ec-4d17-9327-c71353b0f823","userId":"778981a0-fa31-4a3a-9d23-e9cbb9c0c952","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.669Z] [INFO] Token deduction successful {"userId":"3de2f9e7-75b6-429a-90a4-b4366adf0874","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.669Z] [INFO] Job created {"jobId":"419efb9c-d674-4a0f-8265-d1b013d0e667","userId":"3de2f9e7-75b6-429a-90a4-b4366adf0874","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.669Z] [INFO] Assessment job published to queue {"jobId":"419efb9c-d674-4a0f-8265-d1b013d0e667","userId":"3de2f9e7-75b6-429a-90a4-b4366adf0874","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.670Z] [INFO] Token deduction successful {"userId":"829d69b3-2fdf-44eb-86a6-1d04b90cc581","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.670Z] [INFO] Job created {"jobId":"cab94508-e557-47b1-9642-b4c0dba95480","userId":"829d69b3-2fdf-44eb-86a6-1d04b90cc581","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.671Z] [INFO] Assessment job published to queue {"jobId":"cab94508-e557-47b1-9642-b4c0dba95480","userId":"829d69b3-2fdf-44eb-86a6-1d04b90cc581","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.672Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-18T05:47:45.672Z] [INFO] Token deduction successful {"userId":"ae217bd8-d2f3-4a8b-aac3-b3516790c2e2","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.673Z] [INFO] Job created {"jobId":"f7f3d621-8ff9-4ef9-a6d8-eef46124cfd3","userId":"ae217bd8-d2f3-4a8b-aac3-b3516790c2e2","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.673Z] [INFO] Assessment job published to queue {"jobId":"f7f3d621-8ff9-4ef9-a6d8-eef46124cfd3","userId":"ae217bd8-d2f3-4a8b-aac3-b3516790c2e2","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.674Z] [INFO] Token deduction successful {"userId":"7f361f12-3132-4693-8a61-f2ad8a8c6b0c","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.674Z] [INFO] Job created {"jobId":"83e24814-87e3-46f4-8381-52b0aa24c3dc","userId":"7f361f12-3132-4693-8a61-f2ad8a8c6b0c","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.674Z] [INFO] Assessment job published to queue {"jobId":"83e24814-87e3-46f4-8381-52b0aa24c3dc","userId":"7f361f12-3132-4693-8a61-f2ad8a8c6b0c","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.675Z] [INFO] Token deduction successful {"userId":"6761c652-3586-4274-8e32-44b45eb0ab1b","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.675Z] [INFO] Job created {"jobId":"757d91fc-405d-4909-93ac-4028971e7398","userId":"6761c652-3586-4274-8e32-44b45eb0ab1b","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.676Z] [INFO] Assessment job published to queue {"jobId":"757d91fc-405d-4909-93ac-4028971e7398","userId":"6761c652-3586-4274-8e32-44b45eb0ab1b","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.676Z] [INFO] Token deduction successful {"userId":"1beefbcc-ded3-4675-85df-6c1468d16c02","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.676Z] [INFO] Job created {"jobId":"4a610299-e43f-4ac3-b3b2-ac92563e5560","userId":"1beefbcc-ded3-4675-85df-6c1468d16c02","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.677Z] [INFO] Assessment job published to queue {"jobId":"4a610299-e43f-4ac3-b3b2-ac92563e5560","userId":"1beefbcc-ded3-4675-85df-6c1468d16c02","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.677Z] [INFO] Token deduction successful {"userId":"e5084c4d-a410-4bed-aaae-8b1e791b1180","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.678Z] [INFO] Job created {"jobId":"9d2ca6c4-9eb6-4dfb-9331-90e7af086166","userId":"e5084c4d-a410-4bed-aaae-8b1e791b1180","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.678Z] [INFO] Assessment job published to queue {"jobId":"9d2ca6c4-9eb6-4dfb-9331-90e7af086166","userId":"e5084c4d-a410-4bed-aaae-8b1e791b1180","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.678Z] [INFO] Token deduction successful {"userId":"50520da3-8855-4d53-bac8-711fc04cfd19","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.679Z] [INFO] Job created {"jobId":"cfeb55e9-0840-4885-8da9-859e9d469036","userId":"50520da3-8855-4d53-bac8-711fc04cfd19","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.679Z] [INFO] Assessment job published to queue {"jobId":"cfeb55e9-0840-4885-8da9-859e9d469036","userId":"50520da3-8855-4d53-bac8-711fc04cfd19","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.680Z] [INFO] Token deduction successful {"userId":"ee3c2f77-9ce0-4e25-a239-0ccdb4b80c24","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.680Z] [INFO] Job created {"jobId":"661ce4db-7ab4-4f33-80c9-54ddf85e53fa","userId":"ee3c2f77-9ce0-4e25-a239-0ccdb4b80c24","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.680Z] [INFO] Assessment job published to queue {"jobId":"661ce4db-7ab4-4f33-80c9-54ddf85e53fa","userId":"ee3c2f77-9ce0-4e25-a239-0ccdb4b80c24","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.681Z] [INFO] Token deduction successful {"userId":"63a9574c-b28b-4dc2-b0a6-0e009a92c1ae","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.681Z] [INFO] Job created {"jobId":"c3ac7294-fab3-40bd-b4af-a94b1a2523dc","userId":"63a9574c-b28b-4dc2-b0a6-0e009a92c1ae","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.681Z] [INFO] Assessment job published to queue {"jobId":"c3ac7294-fab3-40bd-b4af-a94b1a2523dc","userId":"63a9574c-b28b-4dc2-b0a6-0e009a92c1ae","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.682Z] [INFO] Token deduction successful {"userId":"62a862d9-1280-4b90-b469-db2e46dd6941","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.682Z] [INFO] Job created {"jobId":"2e6cf6b3-8b56-48d8-8ac1-cbe7e0beff55","userId":"62a862d9-1280-4b90-b469-db2e46dd6941","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.683Z] [INFO] Assessment job published to queue {"jobId":"2e6cf6b3-8b56-48d8-8ac1-cbe7e0beff55","userId":"62a862d9-1280-4b90-b469-db2e46dd6941","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.683Z] [INFO] Token deduction successful {"userId":"0d81b437-2a5b-45bb-87e3-b22638c79d2d","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.683Z] [INFO] Job created {"jobId":"6b09de7b-051f-4526-8aee-126bb4e8a62f","userId":"0d81b437-2a5b-45bb-87e3-b22638c79d2d","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.684Z] [INFO] Assessment job published to queue {"jobId":"6b09de7b-051f-4526-8aee-126bb4e8a62f","userId":"0d81b437-2a5b-45bb-87e3-b22638c79d2d","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.684Z] [INFO] Token deduction successful {"userId":"7dde7770-1e0e-481e-af32-84bc96240188","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.684Z] [INFO] Job created {"jobId":"9e3b9846-09e6-4286-beaf-147f9db0f27b","userId":"7dde7770-1e0e-481e-af32-84bc96240188","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.685Z] [INFO] Assessment job published to queue {"jobId":"9e3b9846-09e6-4286-beaf-147f9db0f27b","userId":"7dde7770-1e0e-481e-af32-84bc96240188","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.685Z] [INFO] Token deduction successful {"userId":"c48a95d5-717b-487b-b2ce-61d7e09e5b43","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.686Z] [INFO] Job created {"jobId":"92e49306-edb7-4129-9e75-a1eb9d3653aa","userId":"c48a95d5-717b-487b-b2ce-61d7e09e5b43","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.686Z] [INFO] Assessment job published to queue {"jobId":"92e49306-edb7-4129-9e75-a1eb9d3653aa","userId":"c48a95d5-717b-487b-b2ce-61d7e09e5b43","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.687Z] [INFO] Token deduction successful {"userId":"c0a5909b-84fd-4460-8b7d-76601a8a73e2","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.687Z] [INFO] Job created {"jobId":"65929f8e-412d-4043-9dfe-117594362aa6","userId":"c0a5909b-84fd-4460-8b7d-76601a8a73e2","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.687Z] [INFO] Assessment job published to queue {"jobId":"65929f8e-412d-4043-9dfe-117594362aa6","userId":"c0a5909b-84fd-4460-8b7d-76601a8a73e2","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.688Z] [INFO] Token deduction successful {"userId":"4bcc4e0f-367a-4ecd-a233-ec3e9d48de38","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:47:45.688Z] [INFO] Job created {"jobId":"3302a620-d718-4dc6-a3b2-d4eefd934be6","userId":"4bcc4e0f-367a-4ecd-a233-ec3e9d48de38","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:47:45.688Z] [INFO] Assessment job published to queue {"jobId":"3302a620-d718-4dc6-a3b2-d4eefd934be6","userId":"4bcc4e0f-367a-4ecd-a233-ec3e9d48de38","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:47:45.690Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.691Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.692Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.694Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.695Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.696Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.697Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.697Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.699Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.700Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.701Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.702Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.704Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.705Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.706Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.707Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.708Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.709Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.710Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.712Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.713Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.714Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.716Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.717Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.719Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.720Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.721Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.722Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.723Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.724Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.725Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.726Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.727Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.728Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.729Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.731Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.732Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.733Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.736Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.738Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.741Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.743Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.744Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.745Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.746Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.748Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.749Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.750Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.751Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.752Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.754Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.755Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.756Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.757Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.758Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.760Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.761Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.762Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.763Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.764Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.765Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.766Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.768Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.769Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.770Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.771Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:47:45.772Z] [INFO] ::1 - - [18/Jul/2025:05:47:45 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:52:12.270Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-18T05:52:12.297Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-18T05:52:12.298Z] [INFO] Queue service initialized successfully
[2025-07-18T05:52:12.298Z] [INFO] All services initialized successfully
[2025-07-18T05:52:12.301Z] [INFO] Assessment Service running on port 3003
[2025-07-18T05:52:12.301Z] [INFO] Environment: development
[2025-07-18T05:52:44.314Z] [WARN] Insufficient token balance {"userId":"da6e7756-6da7-4762-9478-15989c524ba5","email":"<EMAIL>","currentBalance":0,"requiredTokens":1,"url":"/assessments/submit"}
[2025-07-18T05:52:44.319Z] [INFO] ::1 - - [18/Jul/2025:05:52:44 +0000] "POST /assessments/submit HTTP/1.1" 402 161 "-" "axios/1.10.0"
[2025-07-18T05:56:42.172Z] [INFO] Connecting to RabbitMQ... {"url":"amqp://localhost:5672"}
[2025-07-18T05:56:42.198Z] [INFO] RabbitMQ connection established successfully {"exchange":"atma_exchange","queue":"assessment_analysis","routingKey":"analysis.process"}
[2025-07-18T05:56:42.199Z] [INFO] Queue service initialized successfully
[2025-07-18T05:56:42.199Z] [INFO] All services initialized successfully
[2025-07-18T05:56:42.201Z] [INFO] Assessment Service running on port 3003
[2025-07-18T05:56:42.202Z] [INFO] Environment: development
[2025-07-18T05:57:24.337Z] [INFO] ::1 - - [18/Jul/2025:05:57:24 +0000] "GET /health HTTP/1.1" 200 589 "-" "ATMA-API-Gateway-HealthCheck"
[2025-07-18T05:57:33.555Z] [INFO] Assessment submission received {"userId":"cfa28d6d-330b-4cef-a724-b310132a0642","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:57:33.574Z] [INFO] Token deduction successful {"userId":"cfa28d6d-330b-4cef-a724-b310132a0642","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:57:33.576Z] [INFO] Job created {"jobId":"15dedfb7-ff25-4892-96e2-cb9d336883b6","userId":"cfa28d6d-330b-4cef-a724-b310132a0642","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:57:33.577Z] [INFO] Assessment job published to queue {"jobId":"15dedfb7-ff25-4892-96e2-cb9d336883b6","userId":"cfa28d6d-330b-4cef-a724-b310132a0642","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:57:33.580Z] [INFO] ::1 - - [18/Jul/2025:05:57:33 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-18T05:57:46.321Z] [INFO] Assessment submission received {"userId":"********-471a-4b23-8ab1-6679c27a27ef","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:57:46.323Z] [INFO] Assessment submission received {"userId":"c13ee8cc-7144-4ca1-b0b2-b9a909a89592","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:57:46.325Z] [INFO] Assessment submission received {"userId":"4ada4ec1-3775-4d55-b2d6-39f767814a67","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:57:46.327Z] [INFO] Assessment submission received {"userId":"1a3ec0f0-d95d-4f12-9671-71011df132f6","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:57:46.333Z] [INFO] Assessment submission received {"userId":"96fbbad0-3e6f-489c-816f-60f0f8ce2f02","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:57:46.335Z] [INFO] Assessment submission received {"userId":"f9d77206-0bfb-4fc4-a3ad-863fabbf194e","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:57:46.337Z] [INFO] Assessment submission received {"userId":"d60630cd-340f-4697-811e-029a25930b18","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:57:46.339Z] [INFO] Assessment submission received {"userId":"1ed440a9-6d1e-4373-8a64-d953b395bc05","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:57:46.349Z] [INFO] Assessment submission received {"userId":"0d2d57e3-2fe3-440c-9fb1-ed3ed6805209","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:57:46.381Z] [INFO] Token deduction successful {"userId":"f9d77206-0bfb-4fc4-a3ad-863fabbf194e","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:57:46.381Z] [INFO] Job created {"jobId":"c47f726c-07d3-4f9e-9e01-546010ee8e20","userId":"f9d77206-0bfb-4fc4-a3ad-863fabbf194e","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:57:46.382Z] [INFO] Assessment job published to queue {"jobId":"c47f726c-07d3-4f9e-9e01-546010ee8e20","userId":"f9d77206-0bfb-4fc4-a3ad-863fabbf194e","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:57:46.383Z] [INFO] Token deduction successful {"userId":"d60630cd-340f-4697-811e-029a25930b18","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:57:46.383Z] [INFO] Job created {"jobId":"8466e11f-873d-4a79-b722-a300e58816c1","userId":"d60630cd-340f-4697-811e-029a25930b18","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:57:46.384Z] [INFO] Assessment job published to queue {"jobId":"8466e11f-873d-4a79-b722-a300e58816c1","userId":"d60630cd-340f-4697-811e-029a25930b18","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:57:46.385Z] [INFO] Token deduction successful {"userId":"1ed440a9-6d1e-4373-8a64-d953b395bc05","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:57:46.385Z] [INFO] Job created {"jobId":"7921fdec-1585-4cbc-a541-0ef2605548f1","userId":"1ed440a9-6d1e-4373-8a64-d953b395bc05","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:57:46.386Z] [INFO] Assessment job published to queue {"jobId":"7921fdec-1585-4cbc-a541-0ef2605548f1","userId":"1ed440a9-6d1e-4373-8a64-d953b395bc05","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:57:46.388Z] [INFO] Assessment submission received {"userId":"0dce7f0f-44ec-4609-a48f-c9dbdd4b55b6","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:57:46.390Z] [INFO] ::1 - - [18/Jul/2025:05:57:46 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-18T05:57:46.392Z] [INFO] ::1 - - [18/Jul/2025:05:57:46 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-18T05:57:46.395Z] [INFO] Token deduction successful {"userId":"c13ee8cc-7144-4ca1-b0b2-b9a909a89592","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:57:46.396Z] [INFO] Job created {"jobId":"61b75abb-11ce-4723-9840-9b5ef94d6485","userId":"c13ee8cc-7144-4ca1-b0b2-b9a909a89592","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:57:46.397Z] [INFO] Assessment job published to queue {"jobId":"61b75abb-11ce-4723-9840-9b5ef94d6485","userId":"c13ee8cc-7144-4ca1-b0b2-b9a909a89592","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:57:46.398Z] [INFO] ::1 - - [18/Jul/2025:05:57:46 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-18T05:57:46.399Z] [INFO] Token deduction successful {"userId":"********-471a-4b23-8ab1-6679c27a27ef","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:57:46.400Z] [INFO] Job created {"jobId":"c543204f-67f7-476b-b5ee-6e4493e86b1e","userId":"********-471a-4b23-8ab1-6679c27a27ef","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:57:46.401Z] [INFO] Assessment job published to queue {"jobId":"c543204f-67f7-476b-b5ee-6e4493e86b1e","userId":"********-471a-4b23-8ab1-6679c27a27ef","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:57:46.402Z] [INFO] Token deduction successful {"userId":"4ada4ec1-3775-4d55-b2d6-39f767814a67","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:57:46.403Z] [INFO] Job created {"jobId":"e1eddb5d-4399-4980-940d-28d903dbb004","userId":"4ada4ec1-3775-4d55-b2d6-39f767814a67","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:57:46.403Z] [INFO] Assessment job published to queue {"jobId":"e1eddb5d-4399-4980-940d-28d903dbb004","userId":"4ada4ec1-3775-4d55-b2d6-39f767814a67","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:57:46.404Z] [INFO] Token deduction successful {"userId":"1a3ec0f0-d95d-4f12-9671-71011df132f6","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:57:46.404Z] [INFO] Job created {"jobId":"4ba65443-e878-4ebe-ad72-461cfc76d06a","userId":"1a3ec0f0-d95d-4f12-9671-71011df132f6","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:57:46.405Z] [INFO] Assessment job published to queue {"jobId":"4ba65443-e878-4ebe-ad72-461cfc76d06a","userId":"1a3ec0f0-d95d-4f12-9671-71011df132f6","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:57:46.406Z] [INFO] Token deduction successful {"userId":"96fbbad0-3e6f-489c-816f-60f0f8ce2f02","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:57:46.407Z] [INFO] Job created {"jobId":"5821d4bc-144f-4e2b-a0a0-fc220910bce0","userId":"96fbbad0-3e6f-489c-816f-60f0f8ce2f02","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:57:46.408Z] [INFO] Assessment job published to queue {"jobId":"5821d4bc-144f-4e2b-a0a0-fc220910bce0","userId":"96fbbad0-3e6f-489c-816f-60f0f8ce2f02","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:57:46.409Z] [INFO] ::1 - - [18/Jul/2025:05:57:46 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-18T05:57:46.411Z] [INFO] Token deduction successful {"userId":"0d2d57e3-2fe3-440c-9fb1-ed3ed6805209","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:57:46.411Z] [INFO] Job created {"jobId":"93fa8b84-2d71-4461-b5a9-ed20596b71ee","userId":"0d2d57e3-2fe3-440c-9fb1-ed3ed6805209","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:57:46.412Z] [INFO] Assessment job published to queue {"jobId":"93fa8b84-2d71-4461-b5a9-ed20596b71ee","userId":"0d2d57e3-2fe3-440c-9fb1-ed3ed6805209","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:57:46.414Z] [INFO] ::1 - - [18/Jul/2025:05:57:46 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-18T05:57:46.416Z] [INFO] ::1 - - [18/Jul/2025:05:57:46 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-18T05:57:46.418Z] [INFO] Token deduction successful {"userId":"0dce7f0f-44ec-4609-a48f-c9dbdd4b55b6","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:57:46.418Z] [INFO] Job created {"jobId":"49c70cfe-f475-4636-b89f-d8c66591aa8b","userId":"0dce7f0f-44ec-4609-a48f-c9dbdd4b55b6","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:57:46.419Z] [INFO] Assessment job published to queue {"jobId":"49c70cfe-f475-4636-b89f-d8c66591aa8b","userId":"0dce7f0f-44ec-4609-a48f-c9dbdd4b55b6","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:57:46.420Z] [INFO] ::1 - - [18/Jul/2025:05:57:46 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-18T05:57:46.422Z] [INFO] ::1 - - [18/Jul/2025:05:57:46 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-18T05:57:46.424Z] [INFO] ::1 - - [18/Jul/2025:05:57:46 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-18T05:57:46.425Z] [INFO] ::1 - - [18/Jul/2025:05:57:46 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-18T05:58:47.893Z] [INFO] Assessment submission received {"userId":"65d0aa1c-9be2-4e5a-a084-cee004367470","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:47.895Z] [INFO] Assessment submission received {"userId":"fde333bd-27bb-4d3e-913a-0990b71afbfb","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:47.897Z] [INFO] Assessment submission received {"userId":"63d27f57-5494-40ba-9836-082f0b3404ed","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:47.899Z] [INFO] Assessment submission received {"userId":"c1fc0120-08ef-4332-9e93-70551c0ee0be","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:47.900Z] [INFO] Assessment submission received {"userId":"c406d275-f856-490c-88e7-1d65cabfbb79","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:47.902Z] [INFO] Assessment submission received {"userId":"f48235b1-0021-4b24-9e3f-b8252738e9ad","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:47.903Z] [INFO] Assessment submission received {"userId":"2dfa7646-1404-4763-bb5a-d2aab6786054","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:47.905Z] [INFO] Assessment submission received {"userId":"319fdabd-7d6c-4df0-b605-3026d21abc2d","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:47.906Z] [INFO] Assessment submission received {"userId":"e1c9d4e2-9550-417a-a5d7-de160bbbc8b4","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:47.908Z] [INFO] Assessment submission received {"userId":"b4ab491f-ed6d-46d0-a9cd-f1b51719baab","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.018Z] [INFO] Assessment submission received {"userId":"f449e912-ce03-4387-80a6-ce4f71e77a30","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.021Z] [INFO] Assessment submission received {"userId":"747b5fde-04c1-47b4-bdd5-f5be41c1a3d2","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.024Z] [INFO] Assessment submission received {"userId":"ade74031-dc44-4e25-9659-2311bdb2a4cc","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.040Z] [INFO] Assessment submission received {"userId":"35f2db47-b6ec-4ce6-a668-74453ae571e8","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.045Z] [INFO] Assessment submission received {"userId":"c11cecd3-b465-4e57-8fb1-acfd4aca9b8d","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.047Z] [INFO] Assessment submission received {"userId":"db543ec8-2bfb-4cd9-9a0f-10ca575fb46f","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.052Z] [INFO] Assessment submission received {"userId":"7055fdae-bb2a-4603-891b-16e9c93e4b01","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.055Z] [INFO] Assessment submission received {"userId":"69940f10-31e3-4323-8649-ddc0b821e825","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.057Z] [INFO] Assessment submission received {"userId":"26790b31-9604-43f7-ae37-16d8804f86f4","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.059Z] [INFO] Assessment submission received {"userId":"72dc1a2f-8b1b-4586-beac-c32cf8969848","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.061Z] [INFO] Assessment submission received {"userId":"0b1d7771-4b37-40d4-9b7f-706fae49370b","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.063Z] [INFO] Assessment submission received {"userId":"1dd700fe-d27f-4109-b04f-7380e86c7d32","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.065Z] [INFO] Assessment submission received {"userId":"356e5538-f2a0-4f8c-9fa7-e1cb9e75171a","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.068Z] [INFO] Assessment submission received {"userId":"8792fa51-c647-4f08-abd9-c49a716239f2","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.071Z] [INFO] Assessment submission received {"userId":"d528fdd3-3235-483b-a43d-d1d1c29b01eb","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.074Z] [INFO] Assessment submission received {"userId":"37d40867-0dda-4e5f-9ba7-b7548c6ea715","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.087Z] [INFO] Assessment submission received {"userId":"968fb035-bdfb-41a5-9734-69b83a1e85bf","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.104Z] [INFO] Assessment submission received {"userId":"2208cfb9-80ff-4d70-a625-e1d994a9093b","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.106Z] [INFO] Assessment submission received {"userId":"5d7bd584-b48f-44f6-a5e8-d01808fd80cd","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.109Z] [INFO] Assessment submission received {"userId":"6f19ce8a-9f83-456b-a11a-7513a10f0235","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.111Z] [INFO] Assessment submission received {"userId":"a6d5c5b2-9ace-4afe-bb24-91e007357a25","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.113Z] [INFO] Assessment submission received {"userId":"6ebd6622-507a-49c6-9c98-a061e87c1cb6","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.115Z] [INFO] Assessment submission received {"userId":"664c8c37-c9d3-4b84-97b3-2fe5214238ce","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.117Z] [INFO] Assessment submission received {"userId":"b6edae56-b8c3-45fb-95fb-08b5df506e72","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.119Z] [INFO] Assessment submission received {"userId":"b73639d0-56f9-44f6-94c7-9cc3f7832f1a","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.121Z] [INFO] Assessment submission received {"userId":"00ce7f85-1d83-412d-b6cd-9015bad9077c","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.123Z] [INFO] Assessment submission received {"userId":"d0c8c8e4-4277-47cc-83d4-1548b6f20468","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.125Z] [INFO] Assessment submission received {"userId":"1cdb63e1-1d4c-4aa0-a685-ee21e6b9d236","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.132Z] [INFO] Assessment submission received {"userId":"db98a2d3-0cd6-49a6-8f57-f06a9bc6f458","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.140Z] [INFO] Assessment submission received {"userId":"bd061e52-dee4-4e17-94f0-d403f2ea61c3","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.142Z] [INFO] Assessment submission received {"userId":"ea223d63-4476-45d8-839f-31fbe4ed3194","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.144Z] [INFO] Assessment submission received {"userId":"dfd2e0d4-472e-4ccf-bec6-7e3a69d2725c","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.146Z] [INFO] Assessment submission received {"userId":"91a792be-755a-4b66-a944-af43cf688f2f","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.147Z] [INFO] Assessment submission received {"userId":"8e409b72-0a72-4f04-a9f4-79d81447a229","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.149Z] [INFO] Assessment submission received {"userId":"d4110d28-c32d-4cb6-9730-139ff41d1169","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.151Z] [INFO] Assessment submission received {"userId":"11464c13-8334-41e5-a97d-0098420f0173","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.152Z] [INFO] Assessment submission received {"userId":"7f9f4712-8f91-4c82-a890-120cb4c3ec1c","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.154Z] [INFO] Assessment submission received {"userId":"f003da6f-4921-4f03-9241-071b0055fce6","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.156Z] [INFO] Assessment submission received {"userId":"6ec7837d-cc51-4f3b-8160-9c83e0bdbfe7","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.158Z] [INFO] Assessment submission received {"userId":"af03d9a7-6d00-46bf-b32d-ac507946342c","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.159Z] [INFO] Assessment submission received {"userId":"60601ef6-c84c-4858-a78d-72ee46df3df4","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.161Z] [INFO] Assessment submission received {"userId":"a072bcbf-fbcd-4246-b68e-e39a8285eeb9","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.163Z] [INFO] Assessment submission received {"userId":"0c34f35d-7a8b-4da6-95af-08ff4a700577","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.164Z] [INFO] Assessment submission received {"userId":"9072bb34-46f4-4208-a95c-56ef28f038e8","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.166Z] [INFO] Assessment submission received {"userId":"444b3e73-8333-470a-882a-586df43fc9b0","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.176Z] [INFO] Assessment submission received {"userId":"f8ab4a93-7814-4073-a5f1-df687584b743","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.177Z] [INFO] Assessment submission received {"userId":"d8c933fc-f1d8-4133-8aba-2c22549353ab","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.179Z] [INFO] Assessment submission received {"userId":"48623a90-e313-4c9a-a7a3-71ec5b4aff44","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.181Z] [INFO] Assessment submission received {"userId":"db281a40-84dc-4b26-86c8-bc8b06cd7aa7","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.183Z] [INFO] Assessment submission received {"userId":"0e88f689-a35b-4a21-b195-027b1b9e6681","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.184Z] [INFO] Assessment submission received {"userId":"1ae0bb96-cde0-42f9-be09-1aba1548cbdd","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.185Z] [INFO] Assessment submission received {"userId":"719ef682-deef-4e94-ad96-9a1619a94a22","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.187Z] [INFO] Assessment submission received {"userId":"bacd761e-1249-4c5e-91ed-38ba0e1483bf","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.188Z] [INFO] Assessment submission received {"userId":"dcd7e23c-e9e2-4601-b286-c05f12ae152c","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.190Z] [INFO] Assessment submission received {"userId":"0277d3c2-afe7-4b86-b7e0-103ecef32ec2","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.191Z] [INFO] Assessment submission received {"userId":"092dad60-4419-4008-9705-a536ba6f2705","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.194Z] [INFO] Assessment submission received {"userId":"9d77de08-8bf3-44dc-b217-58a0080f0f35","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.196Z] [INFO] Assessment submission received {"userId":"69b239ec-3149-45f2-8302-a9dfb59966f2","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.197Z] [INFO] Assessment submission received {"userId":"0e39e42d-d8b6-4ee3-a5c4-caf995b2d2e1","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.199Z] [INFO] Assessment submission received {"userId":"9fa89cbf-1f10-4657-b67e-206e296c2952","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.243Z] [INFO] Assessment submission received {"userId":"8495b445-c3f0-4f52-9423-ca88f55bc64c","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.244Z] [INFO] Assessment submission received {"userId":"55cc0e87-ee68-4039-9769-4ae2749771d6","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.246Z] [INFO] Assessment submission received {"userId":"c8f5c2b2-9145-41cd-914a-c4686579469a","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.253Z] [INFO] Assessment submission received {"userId":"eebfd093-9f16-4996-95b7-583c33ef843a","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.255Z] [INFO] Assessment submission received {"userId":"ae657677-b65c-4ba4-9bfd-b75ad258b5f8","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.329Z] [INFO] Assessment submission received {"userId":"e4f3cf88-6cfe-4d63-871a-9c5ac041154f","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.348Z] [INFO] Assessment submission received {"userId":"0bb56b1f-7fd7-4be9-bb6b-0e489815e19e","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.374Z] [INFO] Assessment submission received {"userId":"8a8b0100-eb47-4534-b00a-58e1c7a9ad79","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.376Z] [INFO] Assessment submission received {"userId":"8d7bd5af-8ab1-4e88-bbe3-4093f504e1f8","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.377Z] [INFO] Assessment submission received {"userId":"788fdba2-40df-4eba-8c20-b76e231fd57e","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.378Z] [INFO] Assessment submission received {"userId":"79ab9798-02ba-402e-b76b-619f5a03ea9d","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.379Z] [INFO] Assessment submission received {"userId":"169b9c0e-3c0d-4357-89d2-db81430900ef","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.381Z] [INFO] Assessment submission received {"userId":"f046a9d0-febc-4b99-9f41-a1c094124720","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.382Z] [INFO] Assessment submission received {"userId":"fc10fd8a-740a-444e-942f-0db730c1509e","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.383Z] [INFO] Assessment submission received {"userId":"53c8a423-e000-428a-b77b-21bf4261d6e7","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.384Z] [INFO] Assessment submission received {"userId":"c26ded89-4052-4dbf-abce-51259ebbc808","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.385Z] [INFO] Token deduction successful {"userId":"65d0aa1c-9be2-4e5a-a084-cee004367470","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.385Z] [INFO] Job created {"jobId":"075acd59-5b33-45db-bcbb-0ed8b300316f","userId":"65d0aa1c-9be2-4e5a-a084-cee004367470","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.386Z] [INFO] Assessment job published to queue {"jobId":"075acd59-5b33-45db-bcbb-0ed8b300316f","userId":"65d0aa1c-9be2-4e5a-a084-cee004367470","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.387Z] [INFO] Token deduction successful {"userId":"fde333bd-27bb-4d3e-913a-0990b71afbfb","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.388Z] [INFO] Job created {"jobId":"445cd6cd-7dfe-44e5-b434-5f499d5f3359","userId":"fde333bd-27bb-4d3e-913a-0990b71afbfb","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.388Z] [INFO] Assessment job published to queue {"jobId":"445cd6cd-7dfe-44e5-b434-5f499d5f3359","userId":"fde333bd-27bb-4d3e-913a-0990b71afbfb","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.389Z] [INFO] Token deduction successful {"userId":"63d27f57-5494-40ba-9836-082f0b3404ed","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.389Z] [INFO] Job created {"jobId":"1cf68551-f802-4bf7-b22c-623bcd29cd74","userId":"63d27f57-5494-40ba-9836-082f0b3404ed","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.389Z] [INFO] Assessment job published to queue {"jobId":"1cf68551-f802-4bf7-b22c-623bcd29cd74","userId":"63d27f57-5494-40ba-9836-082f0b3404ed","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.390Z] [INFO] Token deduction successful {"userId":"c406d275-f856-490c-88e7-1d65cabfbb79","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.390Z] [INFO] Job created {"jobId":"87beb702-2111-4ea6-acb6-0321833a5b20","userId":"c406d275-f856-490c-88e7-1d65cabfbb79","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.391Z] [INFO] Assessment job published to queue {"jobId":"87beb702-2111-4ea6-acb6-0321833a5b20","userId":"c406d275-f856-490c-88e7-1d65cabfbb79","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.391Z] [INFO] Token deduction successful {"userId":"f48235b1-0021-4b24-9e3f-b8252738e9ad","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.392Z] [INFO] Job created {"jobId":"1091abba-7056-49f6-a0c1-aa013f616a59","userId":"f48235b1-0021-4b24-9e3f-b8252738e9ad","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.392Z] [INFO] Assessment job published to queue {"jobId":"1091abba-7056-49f6-a0c1-aa013f616a59","userId":"f48235b1-0021-4b24-9e3f-b8252738e9ad","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.393Z] [INFO] Token deduction successful {"userId":"2dfa7646-1404-4763-bb5a-d2aab6786054","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.393Z] [INFO] Job created {"jobId":"1cb91808-fc3c-4516-85b7-1e14a4b557e6","userId":"2dfa7646-1404-4763-bb5a-d2aab6786054","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.394Z] [INFO] Assessment job published to queue {"jobId":"1cb91808-fc3c-4516-85b7-1e14a4b557e6","userId":"2dfa7646-1404-4763-bb5a-d2aab6786054","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.394Z] [INFO] Token deduction successful {"userId":"319fdabd-7d6c-4df0-b605-3026d21abc2d","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.395Z] [INFO] Job created {"jobId":"ab2ac780-9327-431b-b2c8-80234c817d21","userId":"319fdabd-7d6c-4df0-b605-3026d21abc2d","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.395Z] [INFO] Assessment job published to queue {"jobId":"ab2ac780-9327-431b-b2c8-80234c817d21","userId":"319fdabd-7d6c-4df0-b605-3026d21abc2d","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.396Z] [INFO] Token deduction successful {"userId":"e1c9d4e2-9550-417a-a5d7-de160bbbc8b4","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.396Z] [INFO] Job created {"jobId":"55be8192-7dd5-4a6c-8ef8-3b3e9d96f2e3","userId":"e1c9d4e2-9550-417a-a5d7-de160bbbc8b4","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.396Z] [INFO] Assessment job published to queue {"jobId":"55be8192-7dd5-4a6c-8ef8-3b3e9d96f2e3","userId":"e1c9d4e2-9550-417a-a5d7-de160bbbc8b4","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.397Z] [INFO] Token deduction successful {"userId":"b4ab491f-ed6d-46d0-a9cd-f1b51719baab","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.402Z] [INFO] Job created {"jobId":"ae736827-6180-4270-97a3-52b1cfbe9fdd","userId":"b4ab491f-ed6d-46d0-a9cd-f1b51719baab","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.402Z] [INFO] Assessment job published to queue {"jobId":"ae736827-6180-4270-97a3-52b1cfbe9fdd","userId":"b4ab491f-ed6d-46d0-a9cd-f1b51719baab","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.407Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 252 "-" "axios/1.10.0"
[2025-07-18T05:58:48.417Z] [INFO] Assessment submission received {"userId":"b8328a76-1e84-488d-8dd6-a9d1986a2313","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.424Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.428Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.429Z] [INFO] Token deduction successful {"userId":"c1fc0120-08ef-4332-9e93-70551c0ee0be","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.430Z] [INFO] Job created {"jobId":"fd62dfeb-e33e-498c-9ff0-eb5e10018883","userId":"c1fc0120-08ef-4332-9e93-70551c0ee0be","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.431Z] [INFO] Assessment job published to queue {"jobId":"fd62dfeb-e33e-498c-9ff0-eb5e10018883","userId":"c1fc0120-08ef-4332-9e93-70551c0ee0be","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.432Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.437Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.442Z] [INFO] Assessment submission received {"userId":"36ae66c2-cf19-41b6-90a8-dfa780403177","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.444Z] [INFO] Assessment submission received {"userId":"ab81dc5c-dc57-4dc7-b57f-a8722ad50734","userEmail":"<EMAIL>","assessmentTypes":["riasec","ocean","viaIs"],"ip":"::1"}
[2025-07-18T05:58:48.446Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.450Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.454Z] [INFO] Token deduction successful {"userId":"35f2db47-b6ec-4ce6-a668-74453ae571e8","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.455Z] [INFO] Job created {"jobId":"9dd55ea8-5382-41ab-b6ca-11deecedc42a","userId":"35f2db47-b6ec-4ce6-a668-74453ae571e8","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.456Z] [INFO] Assessment job published to queue {"jobId":"9dd55ea8-5382-41ab-b6ca-11deecedc42a","userId":"35f2db47-b6ec-4ce6-a668-74453ae571e8","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.457Z] [INFO] Token deduction successful {"userId":"c11cecd3-b465-4e57-8fb1-acfd4aca9b8d","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.457Z] [INFO] Job created {"jobId":"5986bd42-6830-471f-8c3f-5c96f620b077","userId":"c11cecd3-b465-4e57-8fb1-acfd4aca9b8d","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.458Z] [INFO] Assessment job published to queue {"jobId":"5986bd42-6830-471f-8c3f-5c96f620b077","userId":"c11cecd3-b465-4e57-8fb1-acfd4aca9b8d","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.459Z] [INFO] Token deduction successful {"userId":"db543ec8-2bfb-4cd9-9a0f-10ca575fb46f","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.460Z] [INFO] Job created {"jobId":"5313f1f1-0831-4096-b212-44b0bd20dee1","userId":"db543ec8-2bfb-4cd9-9a0f-10ca575fb46f","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.461Z] [INFO] Assessment job published to queue {"jobId":"5313f1f1-0831-4096-b212-44b0bd20dee1","userId":"db543ec8-2bfb-4cd9-9a0f-10ca575fb46f","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.463Z] [INFO] Token deduction successful {"userId":"7055fdae-bb2a-4603-891b-16e9c93e4b01","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.463Z] [INFO] Job created {"jobId":"65054ffa-102a-4c33-9fb1-1671ae0e2f76","userId":"7055fdae-bb2a-4603-891b-16e9c93e4b01","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.464Z] [INFO] Assessment job published to queue {"jobId":"65054ffa-102a-4c33-9fb1-1671ae0e2f76","userId":"7055fdae-bb2a-4603-891b-16e9c93e4b01","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.465Z] [INFO] Token deduction successful {"userId":"69940f10-31e3-4323-8649-ddc0b821e825","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.466Z] [INFO] Job created {"jobId":"11bb7da3-d0b8-4edc-b61a-e651853d007d","userId":"69940f10-31e3-4323-8649-ddc0b821e825","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.466Z] [INFO] Assessment job published to queue {"jobId":"11bb7da3-d0b8-4edc-b61a-e651853d007d","userId":"69940f10-31e3-4323-8649-ddc0b821e825","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.467Z] [INFO] Token deduction successful {"userId":"26790b31-9604-43f7-ae37-16d8804f86f4","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.467Z] [INFO] Job created {"jobId":"44e28cbd-062a-4536-a642-************","userId":"26790b31-9604-43f7-ae37-16d8804f86f4","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.468Z] [INFO] Assessment job published to queue {"jobId":"44e28cbd-062a-4536-a642-************","userId":"26790b31-9604-43f7-ae37-16d8804f86f4","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.469Z] [INFO] Token deduction successful {"userId":"72dc1a2f-8b1b-4586-beac-c32cf8969848","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.469Z] [INFO] Job created {"jobId":"2691f6a1-4470-465f-b27e-4772419e42d1","userId":"72dc1a2f-8b1b-4586-beac-c32cf8969848","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.470Z] [INFO] Assessment job published to queue {"jobId":"2691f6a1-4470-465f-b27e-4772419e42d1","userId":"72dc1a2f-8b1b-4586-beac-c32cf8969848","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.471Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.472Z] [INFO] Token deduction successful {"userId":"0b1d7771-4b37-40d4-9b7f-706fae49370b","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.472Z] [INFO] Job created {"jobId":"1aeabfc3-aca4-4ba0-a1ae-b73640cc376b","userId":"0b1d7771-4b37-40d4-9b7f-706fae49370b","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.473Z] [INFO] Assessment job published to queue {"jobId":"1aeabfc3-aca4-4ba0-a1ae-b73640cc376b","userId":"0b1d7771-4b37-40d4-9b7f-706fae49370b","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.474Z] [INFO] Token deduction successful {"userId":"1dd700fe-d27f-4109-b04f-7380e86c7d32","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.474Z] [INFO] Job created {"jobId":"4b872ecd-3a1d-4c2d-9288-304660542e18","userId":"1dd700fe-d27f-4109-b04f-7380e86c7d32","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.475Z] [INFO] Assessment job published to queue {"jobId":"4b872ecd-3a1d-4c2d-9288-304660542e18","userId":"1dd700fe-d27f-4109-b04f-7380e86c7d32","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.475Z] [INFO] Token deduction successful {"userId":"356e5538-f2a0-4f8c-9fa7-e1cb9e75171a","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.476Z] [INFO] Job created {"jobId":"0c6f844f-4bfb-463f-937a-c9bf8033449e","userId":"356e5538-f2a0-4f8c-9fa7-e1cb9e75171a","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.476Z] [INFO] Assessment job published to queue {"jobId":"0c6f844f-4bfb-463f-937a-c9bf8033449e","userId":"356e5538-f2a0-4f8c-9fa7-e1cb9e75171a","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.477Z] [INFO] Token deduction successful {"userId":"8792fa51-c647-4f08-abd9-c49a716239f2","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.478Z] [INFO] Job created {"jobId":"f130f04a-fc4a-4e4f-89aa-fdaf22f0b196","userId":"8792fa51-c647-4f08-abd9-c49a716239f2","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.478Z] [INFO] Assessment job published to queue {"jobId":"f130f04a-fc4a-4e4f-89aa-fdaf22f0b196","userId":"8792fa51-c647-4f08-abd9-c49a716239f2","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.479Z] [INFO] Token deduction successful {"userId":"f449e912-ce03-4387-80a6-ce4f71e77a30","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.480Z] [INFO] Job created {"jobId":"634e7635-6662-4b7c-a582-2201ad3bad28","userId":"f449e912-ce03-4387-80a6-ce4f71e77a30","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.480Z] [INFO] Assessment job published to queue {"jobId":"634e7635-6662-4b7c-a582-2201ad3bad28","userId":"f449e912-ce03-4387-80a6-ce4f71e77a30","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.481Z] [INFO] Token deduction successful {"userId":"747b5fde-04c1-47b4-bdd5-f5be41c1a3d2","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.481Z] [INFO] Job created {"jobId":"73a2b4b2-37f6-47b7-bbd2-da71e7b82441","userId":"747b5fde-04c1-47b4-bdd5-f5be41c1a3d2","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.482Z] [INFO] Assessment job published to queue {"jobId":"73a2b4b2-37f6-47b7-bbd2-da71e7b82441","userId":"747b5fde-04c1-47b4-bdd5-f5be41c1a3d2","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.483Z] [INFO] Token deduction successful {"userId":"ade74031-dc44-4e25-9659-2311bdb2a4cc","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.483Z] [INFO] Job created {"jobId":"efc4ba17-9e7c-4547-8234-8137149dc8d0","userId":"ade74031-dc44-4e25-9659-2311bdb2a4cc","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.484Z] [INFO] Assessment job published to queue {"jobId":"efc4ba17-9e7c-4547-8234-8137149dc8d0","userId":"ade74031-dc44-4e25-9659-2311bdb2a4cc","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.485Z] [INFO] Token deduction successful {"userId":"d528fdd3-3235-483b-a43d-d1d1c29b01eb","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.485Z] [INFO] Job created {"jobId":"71a7e9ba-5caa-410b-9194-6374a9831ee3","userId":"d528fdd3-3235-483b-a43d-d1d1c29b01eb","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.486Z] [INFO] Assessment job published to queue {"jobId":"71a7e9ba-5caa-410b-9194-6374a9831ee3","userId":"d528fdd3-3235-483b-a43d-d1d1c29b01eb","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.486Z] [INFO] Token deduction successful {"userId":"37d40867-0dda-4e5f-9ba7-b7548c6ea715","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.487Z] [INFO] Job created {"jobId":"2c2bbc25-47cb-4245-b538-00f66438f5a9","userId":"37d40867-0dda-4e5f-9ba7-b7548c6ea715","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.487Z] [INFO] Assessment job published to queue {"jobId":"2c2bbc25-47cb-4245-b538-00f66438f5a9","userId":"37d40867-0dda-4e5f-9ba7-b7548c6ea715","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.491Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.493Z] [INFO] Token deduction successful {"userId":"968fb035-bdfb-41a5-9734-69b83a1e85bf","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.494Z] [INFO] Job created {"jobId":"fa51d1ad-1428-4d11-8f04-aebc5c1f9eab","userId":"968fb035-bdfb-41a5-9734-69b83a1e85bf","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.494Z] [INFO] Assessment job published to queue {"jobId":"fa51d1ad-1428-4d11-8f04-aebc5c1f9eab","userId":"968fb035-bdfb-41a5-9734-69b83a1e85bf","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.495Z] [INFO] Token deduction successful {"userId":"6f19ce8a-9f83-456b-a11a-7513a10f0235","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.495Z] [INFO] Job created {"jobId":"3d104a5e-2895-4f87-bfd0-f355c6721a45","userId":"6f19ce8a-9f83-456b-a11a-7513a10f0235","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.496Z] [INFO] Assessment job published to queue {"jobId":"3d104a5e-2895-4f87-bfd0-f355c6721a45","userId":"6f19ce8a-9f83-456b-a11a-7513a10f0235","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.496Z] [INFO] Token deduction successful {"userId":"a6d5c5b2-9ace-4afe-bb24-91e007357a25","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.497Z] [INFO] Job created {"jobId":"121dafd4-bc54-4b1a-85a8-c022c9d9342e","userId":"a6d5c5b2-9ace-4afe-bb24-91e007357a25","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.497Z] [INFO] Assessment job published to queue {"jobId":"121dafd4-bc54-4b1a-85a8-c022c9d9342e","userId":"a6d5c5b2-9ace-4afe-bb24-91e007357a25","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.498Z] [INFO] Token deduction successful {"userId":"6ebd6622-507a-49c6-9c98-a061e87c1cb6","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.498Z] [INFO] Job created {"jobId":"d67c88c9-7379-45ad-ab00-8a9116b5803f","userId":"6ebd6622-507a-49c6-9c98-a061e87c1cb6","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.499Z] [INFO] Assessment job published to queue {"jobId":"d67c88c9-7379-45ad-ab00-8a9116b5803f","userId":"6ebd6622-507a-49c6-9c98-a061e87c1cb6","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.499Z] [INFO] Token deduction successful {"userId":"664c8c37-c9d3-4b84-97b3-2fe5214238ce","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.500Z] [INFO] Job created {"jobId":"ae35fcb9-9efa-402d-8a8b-84a063aff043","userId":"664c8c37-c9d3-4b84-97b3-2fe5214238ce","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.500Z] [INFO] Assessment job published to queue {"jobId":"ae35fcb9-9efa-402d-8a8b-84a063aff043","userId":"664c8c37-c9d3-4b84-97b3-2fe5214238ce","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.501Z] [INFO] Token deduction successful {"userId":"b73639d0-56f9-44f6-94c7-9cc3f7832f1a","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.501Z] [INFO] Job created {"jobId":"712fec05-d2be-46dd-a9ff-4fc9cd01b476","userId":"b73639d0-56f9-44f6-94c7-9cc3f7832f1a","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.501Z] [INFO] Assessment job published to queue {"jobId":"712fec05-d2be-46dd-a9ff-4fc9cd01b476","userId":"b73639d0-56f9-44f6-94c7-9cc3f7832f1a","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.502Z] [INFO] Token deduction successful {"userId":"00ce7f85-1d83-412d-b6cd-9015bad9077c","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.502Z] [INFO] Job created {"jobId":"c14bafcb-6bd7-4513-98f0-7dc27497b1b2","userId":"00ce7f85-1d83-412d-b6cd-9015bad9077c","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.503Z] [INFO] Assessment job published to queue {"jobId":"c14bafcb-6bd7-4513-98f0-7dc27497b1b2","userId":"00ce7f85-1d83-412d-b6cd-9015bad9077c","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.504Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.505Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.507Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.508Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.509Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.510Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.512Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.513Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.515Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.516Z] [INFO] Token deduction successful {"userId":"b6edae56-b8c3-45fb-95fb-08b5df506e72","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.516Z] [INFO] Job created {"jobId":"dd9de9b2-5f1e-4cc5-a827-fd08ef48fe14","userId":"b6edae56-b8c3-45fb-95fb-08b5df506e72","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.517Z] [INFO] Assessment job published to queue {"jobId":"dd9de9b2-5f1e-4cc5-a827-fd08ef48fe14","userId":"b6edae56-b8c3-45fb-95fb-08b5df506e72","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.518Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.520Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.521Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.522Z] [INFO] Token deduction successful {"userId":"2208cfb9-80ff-4d70-a625-e1d994a9093b","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.523Z] [INFO] Job created {"jobId":"1695b2cf-7a55-42bb-98b0-30f43804b1cd","userId":"2208cfb9-80ff-4d70-a625-e1d994a9093b","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.523Z] [INFO] Assessment job published to queue {"jobId":"1695b2cf-7a55-42bb-98b0-30f43804b1cd","userId":"2208cfb9-80ff-4d70-a625-e1d994a9093b","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.524Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.525Z] [INFO] Token deduction successful {"userId":"5d7bd584-b48f-44f6-a5e8-d01808fd80cd","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.525Z] [INFO] Job created {"jobId":"46d63b87-8e53-4880-9736-75771af3e357","userId":"5d7bd584-b48f-44f6-a5e8-d01808fd80cd","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.526Z] [INFO] Assessment job published to queue {"jobId":"46d63b87-8e53-4880-9736-75771af3e357","userId":"5d7bd584-b48f-44f6-a5e8-d01808fd80cd","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.527Z] [INFO] Token deduction successful {"userId":"1cdb63e1-1d4c-4aa0-a685-ee21e6b9d236","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.527Z] [INFO] Job created {"jobId":"9813cf69-cd4d-4bcd-ac65-7ca0622c959c","userId":"1cdb63e1-1d4c-4aa0-a685-ee21e6b9d236","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.528Z] [INFO] Assessment job published to queue {"jobId":"9813cf69-cd4d-4bcd-ac65-7ca0622c959c","userId":"1cdb63e1-1d4c-4aa0-a685-ee21e6b9d236","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.529Z] [INFO] Token deduction successful {"userId":"db98a2d3-0cd6-49a6-8f57-f06a9bc6f458","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.529Z] [INFO] Job created {"jobId":"08e75cf6-258c-46c7-8275-80fc6fbc33b5","userId":"db98a2d3-0cd6-49a6-8f57-f06a9bc6f458","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.529Z] [INFO] Assessment job published to queue {"jobId":"08e75cf6-258c-46c7-8275-80fc6fbc33b5","userId":"db98a2d3-0cd6-49a6-8f57-f06a9bc6f458","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.530Z] [INFO] Token deduction successful {"userId":"dfd2e0d4-472e-4ccf-bec6-7e3a69d2725c","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.530Z] [INFO] Job created {"jobId":"d158010e-9bd6-4f14-8917-453fdcd46193","userId":"dfd2e0d4-472e-4ccf-bec6-7e3a69d2725c","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.531Z] [INFO] Assessment job published to queue {"jobId":"d158010e-9bd6-4f14-8917-453fdcd46193","userId":"dfd2e0d4-472e-4ccf-bec6-7e3a69d2725c","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.532Z] [INFO] Token deduction successful {"userId":"91a792be-755a-4b66-a944-af43cf688f2f","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.532Z] [INFO] Job created {"jobId":"7a570499-5cc9-4ba9-85e6-79352ad64a78","userId":"91a792be-755a-4b66-a944-af43cf688f2f","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.532Z] [INFO] Assessment job published to queue {"jobId":"7a570499-5cc9-4ba9-85e6-79352ad64a78","userId":"91a792be-755a-4b66-a944-af43cf688f2f","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.533Z] [INFO] Token deduction successful {"userId":"d0c8c8e4-4277-47cc-83d4-1548b6f20468","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.534Z] [INFO] Job created {"jobId":"aecd4a6a-2e44-44d1-8d4e-4b610bb340a9","userId":"d0c8c8e4-4277-47cc-83d4-1548b6f20468","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.534Z] [INFO] Assessment job published to queue {"jobId":"aecd4a6a-2e44-44d1-8d4e-4b610bb340a9","userId":"d0c8c8e4-4277-47cc-83d4-1548b6f20468","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.535Z] [INFO] Token deduction successful {"userId":"bd061e52-dee4-4e17-94f0-d403f2ea61c3","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.535Z] [INFO] Job created {"jobId":"bd46eaed-3a4f-4064-8b72-d464fb9c7094","userId":"bd061e52-dee4-4e17-94f0-d403f2ea61c3","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.535Z] [INFO] Assessment job published to queue {"jobId":"bd46eaed-3a4f-4064-8b72-d464fb9c7094","userId":"bd061e52-dee4-4e17-94f0-d403f2ea61c3","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.536Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.537Z] [INFO] Token deduction successful {"userId":"ea223d63-4476-45d8-839f-31fbe4ed3194","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.538Z] [INFO] Job created {"jobId":"75720f48-781d-432c-872d-2b33d551b1e9","userId":"ea223d63-4476-45d8-839f-31fbe4ed3194","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.539Z] [INFO] Assessment job published to queue {"jobId":"75720f48-781d-432c-872d-2b33d551b1e9","userId":"ea223d63-4476-45d8-839f-31fbe4ed3194","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.540Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.541Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.543Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.544Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.545Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.546Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.547Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.549Z] [INFO] Token deduction successful {"userId":"8e409b72-0a72-4f04-a9f4-79d81447a229","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.550Z] [INFO] Job created {"jobId":"e4117637-03a6-4f9f-8688-7a23a3b200a6","userId":"8e409b72-0a72-4f04-a9f4-79d81447a229","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.550Z] [INFO] Assessment job published to queue {"jobId":"e4117637-03a6-4f9f-8688-7a23a3b200a6","userId":"8e409b72-0a72-4f04-a9f4-79d81447a229","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.551Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.552Z] [INFO] Token deduction successful {"userId":"d4110d28-c32d-4cb6-9730-139ff41d1169","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.552Z] [INFO] Job created {"jobId":"********-0b2d-4917-aa56-************","userId":"d4110d28-c32d-4cb6-9730-139ff41d1169","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.553Z] [INFO] Assessment job published to queue {"jobId":"********-0b2d-4917-aa56-************","userId":"d4110d28-c32d-4cb6-9730-139ff41d1169","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.553Z] [INFO] Token deduction successful {"userId":"11464c13-8334-41e5-a97d-0098420f0173","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.554Z] [INFO] Job created {"jobId":"cbbd7082-0ab2-428e-b49b-e3f6dd16f40a","userId":"11464c13-8334-41e5-a97d-0098420f0173","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.554Z] [INFO] Assessment job published to queue {"jobId":"cbbd7082-0ab2-428e-b49b-e3f6dd16f40a","userId":"11464c13-8334-41e5-a97d-0098420f0173","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.555Z] [INFO] Token deduction successful {"userId":"7f9f4712-8f91-4c82-a890-120cb4c3ec1c","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.556Z] [INFO] Job created {"jobId":"c79e8b69-f044-42c8-beae-ba0856d7c72c","userId":"7f9f4712-8f91-4c82-a890-120cb4c3ec1c","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.556Z] [INFO] Assessment job published to queue {"jobId":"c79e8b69-f044-42c8-beae-ba0856d7c72c","userId":"7f9f4712-8f91-4c82-a890-120cb4c3ec1c","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.557Z] [INFO] Token deduction successful {"userId":"f003da6f-4921-4f03-9241-071b0055fce6","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.557Z] [INFO] Job created {"jobId":"f443a580-cf97-4b72-b396-1d0df5764b6f","userId":"f003da6f-4921-4f03-9241-071b0055fce6","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.557Z] [INFO] Assessment job published to queue {"jobId":"f443a580-cf97-4b72-b396-1d0df5764b6f","userId":"f003da6f-4921-4f03-9241-071b0055fce6","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.558Z] [INFO] Token deduction successful {"userId":"6ec7837d-cc51-4f3b-8160-9c83e0bdbfe7","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.559Z] [INFO] Job created {"jobId":"35d6ed05-260c-46eb-8f89-0864a890fe94","userId":"6ec7837d-cc51-4f3b-8160-9c83e0bdbfe7","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.559Z] [INFO] Assessment job published to queue {"jobId":"35d6ed05-260c-46eb-8f89-0864a890fe94","userId":"6ec7837d-cc51-4f3b-8160-9c83e0bdbfe7","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.560Z] [INFO] Token deduction successful {"userId":"af03d9a7-6d00-46bf-b32d-ac507946342c","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.560Z] [INFO] Job created {"jobId":"d39eec2f-c8ba-4f7a-ae92-aa73aa2420b1","userId":"af03d9a7-6d00-46bf-b32d-ac507946342c","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.561Z] [INFO] Assessment job published to queue {"jobId":"d39eec2f-c8ba-4f7a-ae92-aa73aa2420b1","userId":"af03d9a7-6d00-46bf-b32d-ac507946342c","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.562Z] [INFO] Token deduction successful {"userId":"60601ef6-c84c-4858-a78d-72ee46df3df4","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.562Z] [INFO] Job created {"jobId":"740986fc-8f66-4b9b-847e-eaef2fb29c48","userId":"60601ef6-c84c-4858-a78d-72ee46df3df4","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.563Z] [INFO] Assessment job published to queue {"jobId":"740986fc-8f66-4b9b-847e-eaef2fb29c48","userId":"60601ef6-c84c-4858-a78d-72ee46df3df4","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.564Z] [INFO] Token deduction successful {"userId":"a072bcbf-fbcd-4246-b68e-e39a8285eeb9","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.564Z] [INFO] Job created {"jobId":"cba7a8bd-c8df-4486-aa80-83f77bd9e522","userId":"a072bcbf-fbcd-4246-b68e-e39a8285eeb9","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.565Z] [INFO] Assessment job published to queue {"jobId":"cba7a8bd-c8df-4486-aa80-83f77bd9e522","userId":"a072bcbf-fbcd-4246-b68e-e39a8285eeb9","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.565Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.566Z] [INFO] Token deduction successful {"userId":"0c34f35d-7a8b-4da6-95af-08ff4a700577","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.567Z] [INFO] Job created {"jobId":"125c883a-40db-467a-abf0-368fe1a29530","userId":"0c34f35d-7a8b-4da6-95af-08ff4a700577","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.567Z] [INFO] Assessment job published to queue {"jobId":"125c883a-40db-467a-abf0-368fe1a29530","userId":"0c34f35d-7a8b-4da6-95af-08ff4a700577","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.568Z] [INFO] Token deduction successful {"userId":"9072bb34-46f4-4208-a95c-56ef28f038e8","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.568Z] [INFO] Job created {"jobId":"0e34edfd-c98e-4306-b74e-fbd0fb75e580","userId":"9072bb34-46f4-4208-a95c-56ef28f038e8","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.569Z] [INFO] Assessment job published to queue {"jobId":"0e34edfd-c98e-4306-b74e-fbd0fb75e580","userId":"9072bb34-46f4-4208-a95c-56ef28f038e8","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.569Z] [INFO] Token deduction successful {"userId":"444b3e73-8333-470a-882a-586df43fc9b0","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.570Z] [INFO] Job created {"jobId":"bf803bb0-d519-4ed7-a89e-90e85d8c1332","userId":"444b3e73-8333-470a-882a-586df43fc9b0","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.570Z] [INFO] Assessment job published to queue {"jobId":"bf803bb0-d519-4ed7-a89e-90e85d8c1332","userId":"444b3e73-8333-470a-882a-586df43fc9b0","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.579Z] [INFO] Token deduction successful {"userId":"48623a90-e313-4c9a-a7a3-71ec5b4aff44","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.580Z] [INFO] Job created {"jobId":"712f9859-a4a6-4d45-a699-e003d3913772","userId":"48623a90-e313-4c9a-a7a3-71ec5b4aff44","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.580Z] [INFO] Assessment job published to queue {"jobId":"712f9859-a4a6-4d45-a699-e003d3913772","userId":"48623a90-e313-4c9a-a7a3-71ec5b4aff44","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.581Z] [INFO] Token deduction successful {"userId":"db281a40-84dc-4b26-86c8-bc8b06cd7aa7","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.582Z] [INFO] Job created {"jobId":"dc2bdc68-99cb-47e8-9962-96cbd5576aea","userId":"db281a40-84dc-4b26-86c8-bc8b06cd7aa7","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.582Z] [INFO] Assessment job published to queue {"jobId":"dc2bdc68-99cb-47e8-9962-96cbd5576aea","userId":"db281a40-84dc-4b26-86c8-bc8b06cd7aa7","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.583Z] [INFO] Token deduction successful {"userId":"0e88f689-a35b-4a21-b195-027b1b9e6681","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.583Z] [INFO] Job created {"jobId":"4119f57d-0dfd-4e3d-840c-4ce761795d7a","userId":"0e88f689-a35b-4a21-b195-027b1b9e6681","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.584Z] [INFO] Assessment job published to queue {"jobId":"4119f57d-0dfd-4e3d-840c-4ce761795d7a","userId":"0e88f689-a35b-4a21-b195-027b1b9e6681","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.585Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.586Z] [INFO] Token deduction successful {"userId":"1ae0bb96-cde0-42f9-be09-1aba1548cbdd","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.586Z] [INFO] Job created {"jobId":"35e93aa0-2d44-40ab-9ece-499535aecb92","userId":"1ae0bb96-cde0-42f9-be09-1aba1548cbdd","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.587Z] [INFO] Assessment job published to queue {"jobId":"35e93aa0-2d44-40ab-9ece-499535aecb92","userId":"1ae0bb96-cde0-42f9-be09-1aba1548cbdd","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.589Z] [INFO] Token deduction successful {"userId":"719ef682-deef-4e94-ad96-9a1619a94a22","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.589Z] [INFO] Job created {"jobId":"d7f33edc-3820-4d9f-9a5f-925fe51a0eb7","userId":"719ef682-deef-4e94-ad96-9a1619a94a22","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.590Z] [INFO] Assessment job published to queue {"jobId":"d7f33edc-3820-4d9f-9a5f-925fe51a0eb7","userId":"719ef682-deef-4e94-ad96-9a1619a94a22","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.590Z] [INFO] Token deduction successful {"userId":"bacd761e-1249-4c5e-91ed-38ba0e1483bf","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.591Z] [INFO] Job created {"jobId":"c6f15b3a-1bdd-40f0-bc32-79a91fe66ea8","userId":"bacd761e-1249-4c5e-91ed-38ba0e1483bf","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.591Z] [INFO] Assessment job published to queue {"jobId":"c6f15b3a-1bdd-40f0-bc32-79a91fe66ea8","userId":"bacd761e-1249-4c5e-91ed-38ba0e1483bf","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.592Z] [INFO] Token deduction successful {"userId":"dcd7e23c-e9e2-4601-b286-c05f12ae152c","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.592Z] [INFO] Job created {"jobId":"2e7ac7be-8ee0-4ed0-bce4-b1905ee251a9","userId":"dcd7e23c-e9e2-4601-b286-c05f12ae152c","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.593Z] [INFO] Assessment job published to queue {"jobId":"2e7ac7be-8ee0-4ed0-bce4-b1905ee251a9","userId":"dcd7e23c-e9e2-4601-b286-c05f12ae152c","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.593Z] [INFO] Token deduction successful {"userId":"0277d3c2-afe7-4b86-b7e0-103ecef32ec2","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.594Z] [INFO] Job created {"jobId":"ac6699d1-f625-440c-9ea1-1d297259d5c4","userId":"0277d3c2-afe7-4b86-b7e0-103ecef32ec2","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.594Z] [INFO] Assessment job published to queue {"jobId":"ac6699d1-f625-440c-9ea1-1d297259d5c4","userId":"0277d3c2-afe7-4b86-b7e0-103ecef32ec2","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.596Z] [INFO] Token deduction successful {"userId":"f8ab4a93-7814-4073-a5f1-df687584b743","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.596Z] [INFO] Job created {"jobId":"e700dbd3-6a41-4e7a-862d-0725937e4901","userId":"f8ab4a93-7814-4073-a5f1-df687584b743","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.596Z] [INFO] Assessment job published to queue {"jobId":"e700dbd3-6a41-4e7a-862d-0725937e4901","userId":"f8ab4a93-7814-4073-a5f1-df687584b743","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.597Z] [INFO] Token deduction successful {"userId":"d8c933fc-f1d8-4133-8aba-2c22549353ab","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.598Z] [INFO] Job created {"jobId":"65d348d3-a1eb-42dd-bb45-c980c9d5a962","userId":"d8c933fc-f1d8-4133-8aba-2c22549353ab","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.598Z] [INFO] Assessment job published to queue {"jobId":"65d348d3-a1eb-42dd-bb45-c980c9d5a962","userId":"d8c933fc-f1d8-4133-8aba-2c22549353ab","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.599Z] [INFO] Token deduction successful {"userId":"092dad60-4419-4008-9705-a536ba6f2705","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.600Z] [INFO] Job created {"jobId":"9c0c05fb-75b4-4bcc-bb37-7a5abc305fcd","userId":"092dad60-4419-4008-9705-a536ba6f2705","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.600Z] [INFO] Assessment job published to queue {"jobId":"9c0c05fb-75b4-4bcc-bb37-7a5abc305fcd","userId":"092dad60-4419-4008-9705-a536ba6f2705","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.601Z] [INFO] Token deduction successful {"userId":"9d77de08-8bf3-44dc-b217-58a0080f0f35","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.602Z] [INFO] Job created {"jobId":"dab43730-ffea-48f9-bc92-f31a568d7d4c","userId":"9d77de08-8bf3-44dc-b217-58a0080f0f35","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.602Z] [INFO] Assessment job published to queue {"jobId":"dab43730-ffea-48f9-bc92-f31a568d7d4c","userId":"9d77de08-8bf3-44dc-b217-58a0080f0f35","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.603Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.604Z] [INFO] Token deduction successful {"userId":"69b239ec-3149-45f2-8302-a9dfb59966f2","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.605Z] [INFO] Job created {"jobId":"76ea8fec-c1d2-4bae-9563-d365598d1d44","userId":"69b239ec-3149-45f2-8302-a9dfb59966f2","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.605Z] [INFO] Assessment job published to queue {"jobId":"76ea8fec-c1d2-4bae-9563-d365598d1d44","userId":"69b239ec-3149-45f2-8302-a9dfb59966f2","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.606Z] [INFO] Token deduction successful {"userId":"9fa89cbf-1f10-4657-b67e-206e296c2952","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.607Z] [INFO] Job created {"jobId":"14e0a045-e16c-4112-bf15-d3ecdd0c8d4d","userId":"9fa89cbf-1f10-4657-b67e-206e296c2952","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.607Z] [INFO] Assessment job published to queue {"jobId":"14e0a045-e16c-4112-bf15-d3ecdd0c8d4d","userId":"9fa89cbf-1f10-4657-b67e-206e296c2952","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.608Z] [INFO] Token deduction successful {"userId":"8495b445-c3f0-4f52-9423-ca88f55bc64c","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.608Z] [INFO] Job created {"jobId":"ec7ebbee-0f7a-49aa-8152-e24cd7a84251","userId":"8495b445-c3f0-4f52-9423-ca88f55bc64c","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.609Z] [INFO] Assessment job published to queue {"jobId":"ec7ebbee-0f7a-49aa-8152-e24cd7a84251","userId":"8495b445-c3f0-4f52-9423-ca88f55bc64c","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.609Z] [INFO] Token deduction successful {"userId":"55cc0e87-ee68-4039-9769-4ae2749771d6","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.610Z] [INFO] Job created {"jobId":"b48d5856-76d5-436f-b03a-0315d3f6ae0b","userId":"55cc0e87-ee68-4039-9769-4ae2749771d6","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.610Z] [INFO] Assessment job published to queue {"jobId":"b48d5856-76d5-436f-b03a-0315d3f6ae0b","userId":"55cc0e87-ee68-4039-9769-4ae2749771d6","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.611Z] [INFO] Token deduction successful {"userId":"c8f5c2b2-9145-41cd-914a-c4686579469a","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.611Z] [INFO] Job created {"jobId":"8e1bb4f6-483f-448b-8fac-5d7033262a10","userId":"c8f5c2b2-9145-41cd-914a-c4686579469a","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.612Z] [INFO] Assessment job published to queue {"jobId":"8e1bb4f6-483f-448b-8fac-5d7033262a10","userId":"c8f5c2b2-9145-41cd-914a-c4686579469a","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.613Z] [INFO] Token deduction successful {"userId":"eebfd093-9f16-4996-95b7-583c33ef843a","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.613Z] [INFO] Job created {"jobId":"480b2d38-8ec9-4d6c-aeca-e0ae9dacfda3","userId":"eebfd093-9f16-4996-95b7-583c33ef843a","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.614Z] [INFO] Assessment job published to queue {"jobId":"480b2d38-8ec9-4d6c-aeca-e0ae9dacfda3","userId":"eebfd093-9f16-4996-95b7-583c33ef843a","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.615Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.618Z] [INFO] Token deduction successful {"userId":"0e39e42d-d8b6-4ee3-a5c4-caf995b2d2e1","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.619Z] [INFO] Job created {"jobId":"60c39048-ea40-42a5-a36a-56adff6f8568","userId":"0e39e42d-d8b6-4ee3-a5c4-caf995b2d2e1","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.619Z] [INFO] Assessment job published to queue {"jobId":"60c39048-ea40-42a5-a36a-56adff6f8568","userId":"0e39e42d-d8b6-4ee3-a5c4-caf995b2d2e1","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.620Z] [INFO] Token deduction successful {"userId":"ae657677-b65c-4ba4-9bfd-b75ad258b5f8","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.620Z] [INFO] Job created {"jobId":"aca2ec2e-23f5-4a25-88ec-14eeefccf989","userId":"ae657677-b65c-4ba4-9bfd-b75ad258b5f8","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.621Z] [INFO] Assessment job published to queue {"jobId":"aca2ec2e-23f5-4a25-88ec-14eeefccf989","userId":"ae657677-b65c-4ba4-9bfd-b75ad258b5f8","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.621Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.623Z] [INFO] Token deduction successful {"userId":"e4f3cf88-6cfe-4d63-871a-9c5ac041154f","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.623Z] [INFO] Job created {"jobId":"76809e44-e59c-493d-95a3-9894332506c3","userId":"e4f3cf88-6cfe-4d63-871a-9c5ac041154f","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.624Z] [INFO] Assessment job published to queue {"jobId":"76809e44-e59c-493d-95a3-9894332506c3","userId":"e4f3cf88-6cfe-4d63-871a-9c5ac041154f","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.624Z] [INFO] Token deduction successful {"userId":"0bb56b1f-7fd7-4be9-bb6b-0e489815e19e","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.625Z] [INFO] Job created {"jobId":"fdfb9a0c-e425-4b7e-bde8-ebd227ae284f","userId":"0bb56b1f-7fd7-4be9-bb6b-0e489815e19e","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.625Z] [INFO] Assessment job published to queue {"jobId":"fdfb9a0c-e425-4b7e-bde8-ebd227ae284f","userId":"0bb56b1f-7fd7-4be9-bb6b-0e489815e19e","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.626Z] [INFO] Token deduction successful {"userId":"8a8b0100-eb47-4534-b00a-58e1c7a9ad79","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.626Z] [INFO] Job created {"jobId":"3631f723-5870-47db-8dce-705342db08b2","userId":"8a8b0100-eb47-4534-b00a-58e1c7a9ad79","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.626Z] [INFO] Assessment job published to queue {"jobId":"3631f723-5870-47db-8dce-705342db08b2","userId":"8a8b0100-eb47-4534-b00a-58e1c7a9ad79","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.627Z] [INFO] Token deduction successful {"userId":"8d7bd5af-8ab1-4e88-bbe3-4093f504e1f8","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.627Z] [INFO] Job created {"jobId":"a8a10074-8238-4e22-ab4e-a3ca44ef3dec","userId":"8d7bd5af-8ab1-4e88-bbe3-4093f504e1f8","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.628Z] [INFO] Assessment job published to queue {"jobId":"a8a10074-8238-4e22-ab4e-a3ca44ef3dec","userId":"8d7bd5af-8ab1-4e88-bbe3-4093f504e1f8","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.629Z] [INFO] Token deduction successful {"userId":"788fdba2-40df-4eba-8c20-b76e231fd57e","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.629Z] [INFO] Job created {"jobId":"b5e86fcd-4edc-4745-ae42-b290aa903de8","userId":"788fdba2-40df-4eba-8c20-b76e231fd57e","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.629Z] [INFO] Assessment job published to queue {"jobId":"b5e86fcd-4edc-4745-ae42-b290aa903de8","userId":"788fdba2-40df-4eba-8c20-b76e231fd57e","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.630Z] [INFO] Token deduction successful {"userId":"79ab9798-02ba-402e-b76b-619f5a03ea9d","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.631Z] [INFO] Job created {"jobId":"63324a70-4815-47db-9059-26cbe6b7827e","userId":"79ab9798-02ba-402e-b76b-619f5a03ea9d","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.631Z] [INFO] Assessment job published to queue {"jobId":"63324a70-4815-47db-9059-26cbe6b7827e","userId":"79ab9798-02ba-402e-b76b-619f5a03ea9d","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.632Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.633Z] [INFO] Token deduction successful {"userId":"169b9c0e-3c0d-4357-89d2-db81430900ef","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.633Z] [INFO] Job created {"jobId":"d4205c03-ba43-458f-964d-4af937cc9069","userId":"169b9c0e-3c0d-4357-89d2-db81430900ef","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.634Z] [INFO] Assessment job published to queue {"jobId":"d4205c03-ba43-458f-964d-4af937cc9069","userId":"169b9c0e-3c0d-4357-89d2-db81430900ef","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.634Z] [INFO] Token deduction successful {"userId":"f046a9d0-febc-4b99-9f41-a1c094124720","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.635Z] [INFO] Job created {"jobId":"4f8cae1c-63ca-43ba-a556-69800bb8611a","userId":"f046a9d0-febc-4b99-9f41-a1c094124720","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.635Z] [INFO] Assessment job published to queue {"jobId":"4f8cae1c-63ca-43ba-a556-69800bb8611a","userId":"f046a9d0-febc-4b99-9f41-a1c094124720","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.636Z] [INFO] Token deduction successful {"userId":"fc10fd8a-740a-444e-942f-0db730c1509e","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.636Z] [INFO] Job created {"jobId":"6484e376-b5b0-4efb-a0ef-2069dd8823ac","userId":"fc10fd8a-740a-444e-942f-0db730c1509e","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.637Z] [INFO] Assessment job published to queue {"jobId":"6484e376-b5b0-4efb-a0ef-2069dd8823ac","userId":"fc10fd8a-740a-444e-942f-0db730c1509e","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.638Z] [INFO] Token deduction successful {"userId":"53c8a423-e000-428a-b77b-21bf4261d6e7","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.638Z] [INFO] Job created {"jobId":"c795b45c-2632-4666-b777-1ba344e0451e","userId":"53c8a423-e000-428a-b77b-21bf4261d6e7","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.638Z] [INFO] Assessment job published to queue {"jobId":"c795b45c-2632-4666-b777-1ba344e0451e","userId":"53c8a423-e000-428a-b77b-21bf4261d6e7","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.639Z] [INFO] Token deduction successful {"userId":"c26ded89-4052-4dbf-abce-51259ebbc808","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.639Z] [INFO] Job created {"jobId":"29260f0f-7ceb-4641-b9c7-e83aebddbdeb","userId":"c26ded89-4052-4dbf-abce-51259ebbc808","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.640Z] [INFO] Assessment job published to queue {"jobId":"29260f0f-7ceb-4641-b9c7-e83aebddbdeb","userId":"c26ded89-4052-4dbf-abce-51259ebbc808","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.640Z] [INFO] Token deduction successful {"userId":"b8328a76-1e84-488d-8dd6-a9d1986a2313","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.641Z] [INFO] Job created {"jobId":"17309e45-fdbe-42c1-ac4b-15f82ca9f20a","userId":"b8328a76-1e84-488d-8dd6-a9d1986a2313","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.641Z] [INFO] Assessment job published to queue {"jobId":"17309e45-fdbe-42c1-ac4b-15f82ca9f20a","userId":"b8328a76-1e84-488d-8dd6-a9d1986a2313","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.642Z] [INFO] Token deduction successful {"userId":"36ae66c2-cf19-41b6-90a8-dfa780403177","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.642Z] [INFO] Job created {"jobId":"28179c29-332a-4082-90a3-5a0238bc6de7","userId":"36ae66c2-cf19-41b6-90a8-dfa780403177","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.643Z] [INFO] Assessment job published to queue {"jobId":"28179c29-332a-4082-90a3-5a0238bc6de7","userId":"36ae66c2-cf19-41b6-90a8-dfa780403177","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.644Z] [INFO] Token deduction successful {"userId":"ab81dc5c-dc57-4dc7-b57f-a8722ad50734","deductedAmount":1,"remainingBalance":2}
[2025-07-18T05:58:48.644Z] [INFO] Job created {"jobId":"db04c029-b7e4-4094-b025-33464723650b","userId":"ab81dc5c-dc57-4dc7-b57f-a8722ad50734","userEmail":"<EMAIL>","status":"queued"}
[2025-07-18T05:58:48.644Z] [INFO] Assessment job published to queue {"jobId":"db04c029-b7e4-4094-b025-33464723650b","userId":"ab81dc5c-dc57-4dc7-b57f-a8722ad50734","userEmail":"<EMAIL>","exchange":"atma_exchange","routingKey":"analysis.process"}
[2025-07-18T05:58:48.645Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.648Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.651Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.653Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.655Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.656Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.657Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.658Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.660Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.661Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.663Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.664Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.665Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.667Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.667Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.668Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.669Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.671Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.672Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.673Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.674Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.675Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.676Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.677Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.678Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.679Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.680Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.681Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.682Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.684Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.685Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.686Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.687Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.688Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.689Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.690Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.691Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.692Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.693Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.694Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.695Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.696Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.698Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.699Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.700Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.701Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.702Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.703Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.704Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.705Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.706Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
[2025-07-18T05:58:48.707Z] [INFO] ::1 - - [18/Jul/2025:05:58:48 +0000] "POST /assessments/submit HTTP/1.1" 202 253 "-" "axios/1.10.0"
